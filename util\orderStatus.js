const orderService = require("../services/order.service");
const axios = require("axios");
const customerService = require("../services/customer.service");
const addressService = require("../services/address.service");
const productService = require("../services/products.service");
const emailHelper = require("../util/emailHelper");
const template = require("../util/templates");

exports.orderStatusChange = async () => {
    try {
        const orders = await orderService.find({
            isDelete: false,
            isActive: true,
            orderStatus: { $in: ["CONFIRMED", "PENDING", "OUT FOR DELIVERY"] }
        });
        if (orders.length > 0) {
            for (let order of orders) {
                const url = `https://app.ecofreight.ae/api/webservices/client/order/${order?.orderNo}/history_items`;
                const response = await axios.get(url);
                if (response) {
                    const { data } = response;
                    let updatedOrderStatus = "";
                    if (data?.data?.status === "The Shipment Is Created") {
                        updatedOrderStatus = "SHIPPED";
                    } else if (
                        data?.data?.status === "IN TRANSIST" ||
                        data?.data?.status === "RECEIVED AT Eco Freight" ||
                        data?.data?.status === "RECEIVED AT HUB"
                    ) {
                        updatedOrderStatus = "SHIPPED";
                    } else if (data?.data?.status?.includes("OUT FOR DELIVERY")) {
                        updatedOrderStatus = "OUT FOR DELIVERY";
                    } else if (data?.data?.status == "DELIVERED") {
                        updatedOrderStatus = "DELIVERED";
                    }
                    const orderStatusSequence = [
                        "PLACED",
                        "CONFIRMED",
                        "SHIPPED",
                        "OUT FOR DELIVERY",
                        "DELIVERED",
                        "RETURNED",
                        "REFUNDED"
                    ];

                    const currentStatusIndex = orderStatusSequence.indexOf(order?.orderStatus);
                    const newStatusIndex = orderStatusSequence.indexOf(updatedOrderStatus);

                    if (newStatusIndex > currentStatusIndex + 1) {
                        for (let i = currentStatusIndex + 1; i < newStatusIndex; i++) {
                            order.history.push({
                                status: orderStatusSequence[i],
                                date: new Date().toISOString()
                            });
                        }
                    }

                    if (order.history.includes(updatedOrderStatus)) {
                        return;
                    }

                    if (updatedOrderStatus != order?.orderStatus) {
                        order.orderStatus = updatedOrderStatus;
                        order.history.push({
                            status: updatedOrderStatus,
                            date: new Date().toISOString()
                        });
                    }

                    const updatedOrder = await orderService.update(
                        { orderNo: order?.orderNo },
                        order
                    );
                    if (updatedOrder) {
                        const customerDetails = await customerService.findOne({
                            _id: order.customer
                        });
                        let products = [];

                        if (customerDetails?.email) {
                            const addressDetails = await addressService.findOne({
                                _id: order.address
                            });
                            for (let product of updatedOrder.products) {
                                const productDetails = await productService.findOne({
                                    _id: product.product
                                });
                                products.push({
                                    thumbnail: process.env.DOMAIN + productDetails.thumbnail,
                                    name: productDetails.name?.en,
                                    quantity: product.quantity,
                                    total: product.total
                                });
                            }
                            const orderdata = {
                                status: updatedOrder?.orderStatus,
                                order: updatedOrder,
                                address: addressDetails,
                                products: products
                            };
                            const mailOptions = {
                                from: process.env.EMAIL_USER,
                                to: customerDetails?.email,
                                subject: "Order" + " " + updatedOrder?.orderStatus,
                                html: await template.orderPlaced(orderdata),
                                attachments: [
                                    {
                                        filename: "logo-light-full.png",
                                        path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                        cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                                    }
                                ]
                            };

                            emailHelper.sendMail(mailOptions, (error, info) => {
                                if (error) {
                                } else {
                                }
                            });
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.log("error ::", error);
    }
};
