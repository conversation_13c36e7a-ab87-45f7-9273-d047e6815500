const express = require('express')
const router = express.Router()
const controller = require('../../controllers/order.controller')

module.exports = () => {
    router.post('/get-orders', controller.getAllOrders);
    router.get('/order-details/:orderNo', controller.getOrderDetails);
    router.put('/update-order/:orderNo', controller.updateOrderStatus);

    router.put('/bulk-delete-orders', controller.bulkDeleteOrders);

    router.post('/sales-report', controller.salesReport);
    router.post('/top-selling-report', controller.topSellingReport);

    router.post("/orders/export-CSV", controller.exportCSV);

    router.put('/update-order/:orderNo/status', controller.updateOrderStatus);

    return router;
}