const mongoose = require('mongoose');

const coatingSchema = new mongoose.Schema({
    brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand", required: true },
    name: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    image: { type: String, required: true },
    price: { type: Number, required: true },
    currency: { type: String, required: true },
    refid: { type: String, required: true },
    slug: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('coating', coatingSchema)