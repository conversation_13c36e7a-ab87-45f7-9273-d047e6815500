const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/collection.service");
const db = require("../models");
const slug = require('../util/slug')
const productService = require("../services/products.service");
const jwt = require('jsonwebtoken');
const { KEYS } = require('../config/constants/key');
const { uploadWebp } = require("../util/uploadWebp");

async function uploadImage(req, body, slug) {
    if (req?.files?.image) {
        const name = `collection/${slug}/image/${Date.now()}-${req?.files["image"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["image"][0]?.path, name)
        body.image = key;
    }
}

async function uploadBanner(req, body, slug) {
    if (req?.files?.banner) {
        const name = `collection/${slug}/banner/${Date.now()}-${req?.files["banner"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["banner"][0]?.path, name)
        body.banner = key;
    }
}

async function uploadSeoImage(req, body) {
    if (req?.files?.ogImage) {
        const name = `collection/${body.slug}/ogImage/${Date.now()}-${req?.files["ogImage"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["ogImage"][0]?.path, name)
        body.seoDetails.ogImage = key;
    }
}

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = await service.count({}) + 1
        body.slug = await slug.createSlug(db.Collections, body?.title?.en, { slug: await slug.generateSlug(body?.title?.en), storeId: storeid })
        body.storeId = storeid

        await Promise.all([
            uploadImage(req, body, body.slug),
            uploadBanner(req, body, body.slug),
            uploadSeoImage(req, body.slug)
        ])

        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.COLLECTIONS.CREATED.error_code,
            "error_message": messages.COLLECTIONS.CREATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.getCollections = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.collectionDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req.params.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const { refid } = req.params;
        const collectionDetails = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        if (body?.title?.en != collectionDetails?.title?.en) body.slug = await slug.createSlug(db.Collections, body?.title?.en, { slug: await slug.generateSlug(body?.title?.en), storeId: storeid })

        await Promise.all([
            uploadImage(req, body, body.slug),
            uploadBanner(req, body, body.slug),
            uploadSeoImage(req, body.slug)
        ])

        const response = await service.update({ refid: req.params.refid, storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.COLLECTIONS.UPDATED.error_code,
            "error_message": messages.COLLECTIONS.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.collections = async (req, res) => {
    try {
        let { language, storeid } = req?.headers
        language = language ? language : 'en'
        storeid = storeid ? storeid : 'sa'
        const { authorization } = req.headers
        let customerId = null

        if (authorization) {
            let token = authorization.split('Bearer ')[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null
                customerId = decoded?.customerId
            })
        }

        const { slug } = req.params
        async function getCustomer() {
            if (customerId) return await db.Customers.findOne({ refid: customerId, isDelete: false, isActive: true })
        }
        const [collection, customer] = await Promise.all([
            db.Collections.findOne({ isDelete: false, isActive: true, slug: slug, storeId: storeid }).populate([
                {
                    path: 'products',
                    match: { isDelete: false, isActive: true, storeId: storeid },
                    select: 'name price offerPrice offerPercentage refid brand slug showDiscountPercentage thumbnail label color productType storeId',
                    populate: [
                        { path: 'brand', select: 'name' },
                        { path: 'label', select: 'name' },
                        { path: 'color', select: 'name color' },
                    ]
                },
            ]),
            getCustomer()
        ]);
        let products = []
        for (let product of collection?.products || []) {
            if(product?.storeId != storeid) continue;
            const productDetails = product
            let isWishlisted = false
            if (customer) {
                isWishlisted = customer.wishlist.includes(productDetails?._id)
            }

            let price = productDetails?.price?.aed;
            let offerPrice = productDetails?.offerPrice?.aed
            if (offerPrice < 1 || offerPrice >= price) offerPrice = null
            // let price = productDetails?.sizes && productDetails?.sizes.length > 0 ? productDetails?.sizes[0]?.price : productDetails?.price?.aed;
            // let offerPrice = productDetails?.sizes && productDetails?.sizes.length > 0 ? productDetails?.sizes[0]?.offerPrice : productDetails?.offerPrice?.aed || null;
            let offerPercentage = productDetails?.offerPercentage?.aed
                ? Math.round(productDetails?.offerPercentage?.aed)
                : productDetails?.offerPercentage
                    ? Math.round(productDetails?.offerPercentage)
                    : null;

            if (!offerPercentage) {
                if (productDetails?.price?.aed && productDetails?.offerPrice?.aed) {
                    offerPercentage = Math.round(((productDetails?.price?.aed - productDetails?.offerPrice?.aed) / productDetails?.price?.aed) * 100);
                }
            }

            products.push({
                '0': {
                    refid: productDetails?.refid,
                    _id: productDetails?._id,
                    name: productDetails?.name[language],
                    brand: productDetails?.brand?.name[language],
                    slug: productDetails?.slug,
                    showDiscountPercentage: productDetails?.showDiscountPercentage,
                    price: price,
                    offerPrice: offerPrice && offerPrice == price ? null : offerPrice,
                    offerPercentage: offerPercentage && offerPercentage != 0 ? offerPercentage : null || null,
                    isWishlisted: isWishlisted,
                    thumbnail: process.env.DOMAIN + productDetails?.thumbnail,
                    label: productDetails?.label && productDetails?.label.name[language] || null,
                    color: productDetails?.color,
                    productType: productDetails?.productType,
                }
            })
        }
        const response = {
            refid: collection?.refid,
            _id: collection?._id,
            title: collection?.title[language],
            slug: collection?.slug,
            products: products
        }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const collection = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        if (collection?.inHome) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": 1,
                "error_message": "Collection can not be deleted as it is in home",
            })
            return;
        }
        const response = await service.update({ refid: req.params.refid, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.COLLECTIONS.DELETED.error_code,
            "error_message": messages.COLLECTIONS.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}