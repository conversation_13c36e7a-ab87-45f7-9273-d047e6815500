module.exports = {
    SUCCESS_RESPONSE: { error_code: 0, error_message: 'Success' },
    SERVER_ERROR: { error_code: 90, error_message: 'Something went wrong' },
    VALIDATION_ERROR: { error_code: 100, error_message: 'Validation Error' },
    LOGIN_SUCCESS: { error_code: 0, error_message: 'Logged in successfully' },
    PASSWORD_MISMATCH: { error_code: 1, error_message: 'Password mismatch' },
    TOKEN_REQUIRED: { error_code: 401, error_message: 'Token required' },
    INVALID_TOKEN: { error_code: 401, error_message: 'Invalid Token. Authentication failed' },
    NO_USER_FOUND: { error_code: 404, error_message: "No user found" },
    NO_DATA: { error_code: 1, error_message: 'No data found' },
    DELETE_FAILED: { error_code: 1, error_message: 'Failed to delete' },
    HOME_UPDATED: { error_code: 0, error_message: 'Home page updated successfully' },
    IMPORTED : { error_code: 0, error_message: 'Imported successfully' },

    //ADMIN MESSAGES
    ADMIN: {
        CREATED: { error_code: 0, error_message: 'Admin created successfully' },
        UPDATED: { error_code: 0, error_message: 'Admin updated successfully' },
        NOT_FOUND: { error_code: 1, error_message: 'Admin not found' },
        DELETED: { error_code: 0, error_message: 'Admin deleted successfully' },
    },

    TERMS: {
        CREATED: { error_code: 0, error_message: 'Terms & Conditions created successfully' },
        UPDATED: { error_code: 0, error_message: 'Terms & Conditions updated successfully' },
    },

    PRIVACY_POLICY: {
        CREATED: { error_code: 0, error_message: 'Privacy Policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'Privacy Policy updated successfully' },
    },

    COOKIE_POLICY: {
        CREATED: { error_code: 0, error_message: 'Cookie Policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'Cookie Policy updated successfully' },
    },

    ABOUT: {
        CREATED: { error_code: 0, error_message: 'About Us created successfully' },
        UPDATED: { error_code: 0, error_message: 'About Us updated successfully' },
    },

    BRANDS: {
        CREATED: { error_code: 0, error_message: 'Brand created successfully' },
        UPDATED: { error_code: 0, error_message: 'Brand updated successfully' },
        DELETED: { error_code: 0, error_message: 'Brand deleted successfully' },
    },

    CATEGORY: {
        CREATED: { error_code: 0, error_message: 'Category created successfully' },
        UPDATED: { error_code: 0, error_message: 'Category updated successfully' },
        DELETED: { error_code: 0, error_message: 'Category deleted successfully' },
    },

    CUSTOMERS: {
        CREATED: { error_code: 0, error_message: 'Customer created successfully' },
        UPDATED: { error_code: 0, error_message: 'Customer updated successfully' },
        NOT_FOUND: { error_code: 1, error_message: 'Customer not found' },
        INVALID_OTP: { error_code: 1, error_message: 'Invalid OTP' },
        LOGIN: { error_code: 0, error_message: 'Logged in successfully' },
        OTP_SENT: { error_code: 0, error_message: 'OTP sent successfully' },
        OTP_EXPIRED: { error_code: 1, error_message: 'OTP expired' },
        OTP_REQUIRED: { error_code: 1, error_message: 'OTP , mobile and countryCode required' },
        REGISTERED: { error_code: 0, error_message: 'Registered successfully' },
        ADDED_TO_TRYCART: { error_code: 0, error_message: 'Added to trycart successfully' },
        REMOVED_FROM_TRYCART: { error_code: 0, error_message: 'Removed from trycart successfully' },
    },

    ADDRESS: {
        CREATED: { error_code: 0, error_message: 'Address created successfully' },
        UPDATED: { error_code: 0, error_message: 'Address updated successfully' },
        NOT_FOUND: { error_code: 1, error_message: 'Address not found' },
        DELETED: { error_code: 0, error_message: 'Address deleted successfully' },
    },

    FRAME_TYPES: {
        CREATED: { error_code: 0, error_message: 'Frame type created successfully' },
        UPDATED: { error_code: 0, error_message: 'Frame type updated successfully' },
        DELETED: { error_code: 0, error_message: 'Frame type deleted successfully' },
    },

    FRAME_SHAPES: {
        CREATED: { error_code: 0, error_message: 'Frame shape created successfully' },
        UPDATED: { error_code: 0, error_message: 'Frame shape updated successfully' },
        DELETED: { error_code: 0, error_message: 'Frame shape deleted successfully' },
    },

    PRODUCTS: {
        CREATED: { error_code: 0, error_message: 'Product created successfully' },
        UPDATED: { error_code: 0, error_message: 'Product updated successfully' },
        SKU_EXISTS: { error_code: 1, error_message: 'SKU already exists' },
        NOT_FOUND: { error_code: 1, error_message: 'Product not found' },
        LIMITED_STOCK: { error_code: 2, error_message: 'Product is out of stock' },
        DELETED: { error_code: 0, error_message: 'Product deleted successfully' },
    },

    WISHLIST: {
        ADD: { error_code: 0, error_message: 'Product added to wishlist' },
        REMOVE: { error_code: 0, error_message: 'Product removed from wishlist' },
    },

    REVIEWS: {
        CREATED: { error_code: 0, error_message: 'Review created successfully' },
        UPDATED: { error_code: 0, error_message: 'Review updated successfully' },
        FAILED: { error_code: 1, error_message: 'Review failed' },
        NOT_FOUND: { error_code: 1, error_message: 'Review not found' },
        DELETED: { error_code: 0, error_message: 'Review deleted successfully' },
    },

    AGE_GROUP: {
        CREATED: { error_code: 0, error_message: 'Age group created successfully' },
        UPDATED: { error_code: 0, error_message: 'Age group updated successfully' },
        DELETED: { error_code: 0, error_message: 'Age group deleted successfully' },
        ALREADY_EXIST: { error_code: 1, error_message: 'Age group already exist' },
    },

    PAYMNET_METHOD_FEE: {
        ALREADY_EXIST: { error_code: 1, error_message: 'Payment method type already exist' },
        CREATED: { error_code: 0, error_message: 'Payment method fee created successfully' },
        UPDATED: { error_code: 0, error_message: 'Payment method fee updated successfully' },
    },

    VM_POLICY: {
        ALREADY_EXIST: { error_code: 1, error_message: 'VM Policy already exist' },
        CREATED: { error_code: 0, error_message: 'VM Policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'VM Policy updated successfully' },
    },

    ATTRIBUTE: {
        CREATED: { error_code: 0, error_message: 'Attribute created successfully' },
        UPDATED: { error_code: 0, error_message: 'Attribute updated successfully' },
    },

    PRESCRIPTIONS: {
        CREATED: { error_code: 0, error_message: 'Prescription created successfully' },
        UPDATED: { error_code: 0, error_message: 'Prescription updated successfully' },
        DELETED: { error_code: 0, error_message: 'Prescription deleted successfully' },
    },

    STORE: {
        CREATED: { error_code: 0, error_message: 'Store created successfully' },
        UPDATED: { error_code: 0, error_message: 'Store updated successfully' },
        CONTENT_CREATED: { error_code: 0, error_message: 'Store content created successfully' },
        CONTENT_UPDATED: { error_code: 0, error_message: 'Store content updated successfully' },
        DELETED: { error_code: 0, error_message: 'Store deleted successfully' },
    },

    BLOGS: {
        CREATED: { error_code: 0, error_message: 'Blog created successfully' },
        UPDATED: { error_code: 0, error_message: 'Blog updated successfully' },
        NOT_FOUND: { error_code: 1, error_message: 'Blog not found' },
        DELETED: { error_code: 0, error_message: 'Blog deleted successfully' },
    },

    CONTACT_US: {
        CREATED: { error_code: 0, error_message: 'Contact form submitted successfully' },
        UPDATED: { error_code: 0, error_message: 'Contact us updated successfully' },
        BANNER: { error_code: 0, error_message: 'Contact page Added successfully' },
        BANNER_UPDATED: { error_code: 0, error_message: 'Contact page updated successfully' },
    },

    COLLECTIONS: {
        CREATED: { error_code: 0, error_message: 'Collection created successfully' },
        UPDATED: { error_code: 0, error_message: 'Collection updated successfully' },
        DELETED: { error_code: 0, error_message: 'Collection deleted successfully' },
    },

    INSURANCE: {
        SUBMITTED: {
            en: { error_code: 0, error_message: 'Insurance enquiry submitted successfully' },
            ar: { error_code: 0, error_message: 'تم تقديم استفسار التأمين بنجاح' }
        },
        CREATED: { error_code: 0, error_message: 'Insurance provider added successfully' },
        UPDATED: { error_code: 0, error_message: 'Insurance provider updated successfully' },
        PROVIDER_DELETED: { error_code: 0, error_message: 'Insurance provider deleted successfully' },
        CONTENT_CREATED: { error_code: 0, error_message: 'Insurance content added successfully' },
        CONTENT_UPDATED: { error_code: 0, error_message: 'Insurance content updated successfully' },
    },

    FAQ: {
        CREATED: { error_code: 0, error_message: 'FAQ created successfully' },
        UPDATED: { error_code: 0, error_message: 'FAQ updated successfully' },
    },

    NEWSLETTER: {
        SUBSCRIBED: { error_code: 0, error_message: 'Subscribed successfully' },
        ALREADY_SUBSCRIBED: { error_code: 1, error_message: 'Already subscribed' },
        DELETED: { error_code: 0, error_message: 'Deleted successfully' },
        UNSUBSCRIBED: { error_code: 0, error_message: 'Unsubscribed successfully' },
    },

    CART: {
        ADDED: { error_code: 0, error_message: 'Product added to cart' },
        EXISTS: { error_code: 1, error_message: 'Product already exists in cart' },
        REMOVED: { error_code: 0, error_message: 'Product removed from cart' },
        EMPTY: { error_code: 1, error_message: 'Cart is empty' },
        UPDATED: { error_code: 0, error_message: 'Cart updated successfully' },
    },

    COUPONS: {
        CREATED: { error_code: 0, error_message: 'Coupon created successfully' },
        UPDATED: { error_code: 0, error_message: 'Coupon updated successfully' },
        INVALID_COUPON: { error_code: 1, error_message: 'Invalid coupon code' },
        APPLIED: { error_code: 0, error_message: 'Coupon applied successfully' },
        EXPIRED: { error_code: 1, error_message: 'Coupon expired' },
        REMOVED: { error_code: 0, error_message: 'Coupon removed successfully' },
    },

    HEADER_MENU: {
        CREATED: { error_code: 0, error_message: 'Header menu created successfully' },
        UPDATED: { error_code: 0, error_message: 'Header menu updated successfully' },
        DELETED: { error_code: 0, error_message: 'Header menu deleted successfully' },
    },

    GENERAL_SETTINGS: {
        CREATED: { error_code: 0, error_message: ' Settings created successfully' },
        UPDATED: { error_code: 0, error_message: ' Settings updated successfully' },
    },

    COATING: {
        CREATED: { error_code: 0, error_message: 'Coating created successfully' },
        UPDATED: { error_code: 0, error_message: 'Coating updated successfully' },
        DELETED: { error_code: 0, error_message: 'Coating deleted successfully' },
    },

    ORDERS: {
        PLACED: { error_code: 0, error_message: 'Order placed successfully' },
        FAILED: { error_code: 1, error_message: 'Order failed' },
        NOT_FOUND: { error_code: 1, error_message: 'No order found' },
        CANCELLED: { error_code: 0, error_message: 'Order cancelled successfully' },
        FAILED_CANCELLED: { error_code: 1, error_message: 'Order cancellation failed' },
        NOT_FOUND: { error_code: 1, error_message: 'Order not found' },
        UPDATED: { error_code: 0, error_message: 'Order updated successfully' },
        CANT_CANCEL: { error_code: 1, error_message: 'Order can not be cancelled' },
    },

    BANNER: {
        CREATED: { error_code: 0, error_message: 'Banner created successfully' },
        UPDATED: { error_code: 0, error_message: 'Banner updated successfully' },
    },

    SUBSCRIBE_PLAN: {
        CREATED: { error_code: 0, error_message: 'Subscribe plan created successfully' },
        UPDATED: { error_code: 0, error_message: 'Subscribe plan updated successfully' },
        DELETED: { error_code: 0, error_message: 'Subscribe plan deleted successfully' },
    },

    TRYCART: {
        CREATED: { error_code: 0, error_message: 'Try cart settings created successfully' },
        UPDATED: { error_code: 0, error_message: 'Try cart settings updated successfully' },
    },

    COLORS: {
        CREATED: { error_code: 0, error_message: 'Color created successfully' },
        UPDATED: { error_code: 0, error_message: 'Color updated successfully' },
        DELETED: { error_code: 0, error_message: 'Color deleted successfully' },
    },

    SIZES: {
        CREATED: { error_code: 0, error_message: 'Size created successfully' },
        UPDATED: { error_code: 0, error_message: 'Size updated successfully' },
        DELETED: { error_code: 0, error_message: 'Size deleted successfully' },
    },

    CONTACT_LENS: {
        CREATED: { error_code: 0, error_message: 'Contact Lens content created successfully' },
        UPDATED: { error_code: 0, error_message: 'Contact Lens content updated successfully' },
        POWER_CREATED: { error_code: 0, error_message: 'Contact Lens power created successfully' },
        POWER_UPDATED: { error_code: 0, error_message: 'Contact Lens power updated successfully' },
    },

    LENS_BRAND: {
        CREATED: { error_code: 0, error_message: 'Lens brand created successfully' },
        UPDATED: { error_code: 0, error_message: 'Lens brand updated successfully' },
        DELETED: { error_code: 0, error_message: 'Lens brand deleted successfully' },
    },

    LENS_POWER: {
        CREATED: { error_code: 0, error_message: 'Lens power created successfully' },
        UPDATED: { error_code: 0, error_message: 'Lens power updated successfully' },
        DELETED: { error_code: 0, error_message: 'Lens power deleted successfully' },
    },

    LENS_INDEX: {
        CREATED: { error_code: 0, error_message: 'Lens index created successfully' },
        UPDATED: { error_code: 0, error_message: 'Lens index updated successfully' },
        DELETED: { error_code: 0, error_message: 'Lens index deleted successfully' },
    },

    LENS_TYPE: {
        CREATED: { error_code: 0, error_message: 'Lens type created successfully' },
        UPDATED: { error_code: 0, error_message: 'Lens type updated successfully' },
        DELETED: { error_code: 0, error_message: 'Lens type deleted successfully' },
    },

    SHIPPING_POLICY: {
        CREATED: { error_code: 0, error_message: 'Shipping policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'Shipping policy updated successfully' },
    },

    RETURN_POLICY: {
        CREATED: { error_code: 0, error_message: 'Return policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'Return policy updated successfully' },
    },

    REFUND_POLICY: {
        CREATED: { error_code: 0, error_message: 'Refund policy created successfully' },
        UPDATED: { error_code: 0, error_message: 'Refund policy updated successfully' },
    },

    LABELS: {
        CREATED: { error_code: 0, error_message: 'Label created successfully' },
        UPDATED: { error_code: 0, error_message: 'Label updated successfully' },
        DELETED: { error_code: 0, error_message: 'Label deleted successfully' },
    },

    FOOTER : {
        CREATED : { error_code: 0, error_message: 'Footer created successfully' },
        UPDATED : { error_code: 0, error_message: 'Footer updated successfully' },
    },

    ROLES : {
        CREATED : { error_code: 0, error_message: 'Role created successfully' },
        UPDATED : { error_code: 0, error_message: 'Role updated successfully' },
        PERMISSION_DENIED : { error_code: 1, error_message: 'Permission denied' },
        DELETED : { error_code: 0, error_message: 'Role deleted successfully' },
    },

    NON_LIST : {
        CREATED : { error_code: 0, error_message: 'Created successfully' },
        UPDATED : { error_code: 0, error_message: 'Updated successfully' },
        NOT_FOUND : { error_code: 1, error_message: 'Not found' },
        DELETED : { error_code: 0, error_message: 'Deleted successfully' },
    },

    META_TAG : {
        CREATED : { error_code: 0, error_message: 'Meta tag created successfully' },
        UPDATED : { error_code: 0, error_message: 'Meta tag updated successfully' },
    },

    FRONT_MATERIAL: {
        CREATED: { error_code: 0, error_message: 'Front Material created successfully' },
        UPDATED: { error_code: 0, error_message: 'Front Material updated successfully' },
        DELETED: { error_code: 0, error_message: 'Front Material deleted successfully' },
    },

    TYPE : {
        CREATED : { error_code: 0, error_message: 'Type created successfully' },
        UPDATED : { error_code: 0, error_message: 'Type updated successfully' },
        DELETED : { error_code: 0, error_message: 'Type deleted successfully' },
    },

    LENS_MATERIAL: {
        CREATED: { error_code: 0, error_message: 'Lens Material created successfully' },
        UPDATED: { error_code: 0, error_message: 'Lens Material updated successfully' },
        DELETED: { error_code: 0, error_message: 'Lens Material deleted successfully' },
    },

}