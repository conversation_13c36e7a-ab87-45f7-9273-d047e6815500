const helper = require('../util/responseHelper')
const messages = require('../config/constants/messages')
const service = require('../services/trycart.service')
const productService = require('../services/products.service')

exports.createTrycart = async (req, res) => {
    try {
        const { body } = req
        body.refid = await service.count({}) + 1
        const result = await service.create(body)
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            for (let product of result?.products) {
                const productDetails = await productService.update({ _id: product }, { isTryCart: true })
            }
            helper.deliverResponse(res, 200, result, {
                error_code: messages.TRYCART.CREATED.error_code,
                error_message: messages.TRYCART.CREATED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.updateTrycart = async (req, res) => {
    try {
        const { body } = req
        const result = await service.update({}, body)
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            const productIds = result.products.map(productId => productId);
            await productService.updateMany({ _id: { $in: productIds }, isDelete: false }, { isTryCart: true });
            await productService.updateMany({ _id: { $nin: productIds }, isDelete: false }, { isTryCart: false });

            helper.deliverResponse(res, 200, result, {
                error_code: messages.TRYCART.UPDATED.error_code,
                error_message: messages.TRYCART.UPDATED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getTrycart = async (req, res) => {
    try {
        const result = await service.find({ isDelete: false })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}