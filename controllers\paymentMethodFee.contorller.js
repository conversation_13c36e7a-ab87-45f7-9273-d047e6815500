const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/paymentMethodFee.service");


exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const methodType = await service.findOne({ type: body?.type, storeId: storeid })
        if (methodType) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": messages.PAYMNET_METHOD_FEE.ALREADY_EXIST.error_code,
                "error_message": messages.PAYMNET_METHOD_FEE.ALREADY_EXIST.error_message
            });
            return;
        }
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.PAYMNET_METHOD_FEE.CREATED.error_code,
            "error_message": messages.PAYMNET_METHOD_FEE.CREATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getPaymentMethodFees = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const response = await service.find({ storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.updateFees = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.methods.forEach(async method => {
            await service.update({ type: method?.type, storeId: storeid }, {fee: method?.fee});
        });
        return helper.deliverResponse(res, 200, {success: true}, {
            error_code: messages.PAYMNET_METHOD_FEE.UPDATED.error_code,
            error_message: messages.PAYMNET_METHOD_FEE.UPDATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}