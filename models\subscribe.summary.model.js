const mongoose = require("mongoose");

const subscribeSummarySchema = new mongoose.Schema({
    refid: { type: String, required: true },
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers", required: true },
    product: { type: mongoose.Schema.Types.ObjectId, ref: "products", required: true },
    plan: { type: mongoose.Schema.Types.ObjectId, ref: "subscribe.plans", required: true },
    contactLens: {
        multiple: { type: Boolean },
        sphLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
        sphRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
        cylLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
        cylRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
        axisLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
        axisRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
    },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });   

module.exports = mongoose.model("subscribe.summary", subscribeSummarySchema)