const mongoose = require('mongoose');

const storeSchema = new mongoose.Schema({
    refid: { type: String, required: true },
    slug: { type: String, required: true },
    brands: [{ type: mongoose.Schema.Types.ObjectId, ref: 'brands' }],
    insurance: [{ type: mongoose.Schema.Types.ObjectId, ref: 'insurance.provider' }],
    name: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    address: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    email: { type: String, required: true },
    countryCode: { type: String, required: true },
    country: { type: String },
    services: [{ type: String }],
    facilities: [{ type: String }],
    specialities: [{ type: String }],
    isSpeciality: { type: Boolean, default: false },
    store: { type: String },
    isClickAndCollect: { type: Boolean, default: true },
    mobile: { type: String, required: true },
    thumbnail: { type: String },
    officeHours: [{
        day: { type: String },
        open: { type: String },
        close: { type: String }
    }],
    coordinates: {
        lat: { type: String },
        long: { type: String }
    },
    location: { type: String },
    status: { type: String, enum: ['Open', 'Closed', 'Temporarily Closed'] },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'adminUsers' },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('stores', storeSchema);