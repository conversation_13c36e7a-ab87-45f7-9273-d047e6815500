const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require("../services/coating.service");
const db = require("../models");
const slug = require('../util/slug')
const enquiryService = require('../services/lens.enquiries.service')

exports.create = async (req, res) => {
    try {
        let { body } = req;
        body.slug = await slug.createSlug(db.Coating, body?.name, { slug: await slug.generateSlug(body?.name) });
        body.refid = await service.count({}) + 1
        const response = await service.create(body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.COATING.CREATED.error_code,
                error_message: messages.COATING.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.list = async (req, res) => {
    try {
        const response = await service.find({ isDelete: false });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.detail = async (req, res) => {
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        let { body } = req;
        const coatingDetails = await service.findOne({ refid: body?.refid, isDelete: false });
        if (body?.name?.en !== coatingDetails?.name) body.slug = await slug.createSlug(db.Coating, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en) })
        const response = await service.update({ refid: req?.params?.refid, isDelete: false }, body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.COATING.UPDATED.error_code,
                error_message: messages.COATING.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.listCoatings = async (req, res) => {
    try {
        const coatings = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        if (coatings instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            const data = coatings.map(coating => {
                return {
                    _id: coating?._id,
                    name: coating?.name,
                    slug: coating?.slug,
                    image: process.env.DOMAIN + coating?.image,
                    price: coating?.price,
                    currency: coating?.currency
                }
            })
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.lensCoating = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const { brand } = req.params
        const coatings = await service.find({ isDelete: false, isActive: true, brand: brand, storeId: storeid })

        const data = coatings.map(coating => {
            return {
                ...coating?._doc,
                image: coating?.image ? process.env.DOMAIN + coating?.image : null,
                name: coating?.name[language]
            }
        })
        const response = { coatings: data }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        const { refid } = req.params;
        const detail = await service.findOne({ refid: refid, isDelete: false });
        const enquiries = await enquiryService.find({ coating: detail?._id, isDelete: false });
        if (enquiries.length > 0) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Cannot delete coating as it is in used in Products'
            });
        }
        const response = await service.update({ refid: refid, isDelete: false }, { isDelete: true });
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.COATING.DELETED.error_code,
                error_message: messages.COATING.DELETED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}