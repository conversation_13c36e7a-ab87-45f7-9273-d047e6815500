const nodemailer = require("nodemailer");
require('dotenv').config()
// const transporter = nodemailer.createTransport({
//     service: "gmail",
//     auth: {
//         user: "<EMAIL>",
//         pass: "aaoxxrwyibbrvtyb",
//     },
// });
const transporter = nodemailer.createTransport({
    host: "smtp-mail.outlook.com",
    port: 587,
    auth: {
        user: process.env.EMAIL_USER_NAME, // Your GoDaddy email
        pass: process.env.EMAIL_PASS, // Your GoDaddy app password
    },
});
//
module.exports = transporter;