const mongoose = require('mongoose');

const beforeAfterSchema = new mongoose.Schema({
    title: { type: String },
    beforeImage: { type: String, required: true },
    afterImage: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });


module.exports = mongoose.model('BeforeAfter', beforeAfterSchema);
