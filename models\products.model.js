const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
    {
        refid: { type: String, required: true },
        slug: { type: String, required: true },
        name: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        modelName: { type: String },
        modelCode: { type: String },
        colorCode: { type: String },
        banner: { type: String },
        images: [{ type: String }],
        thumbnail: { type: String },
        brand: { type: mongoose.Schema.Types.ObjectId, ref: "brands", required: true },
        subBrand: { type: mongoose.Schema.Types.ObjectId, ref: "brands" },
        category: [{ type: mongoose.Schema.Types.ObjectId, ref: "categories", required: true }],
        subCategory: [{ type: mongoose.Schema.Types.ObjectId, ref: "categories" }],
        position: { type: Number, default: 0 },
        recommendedProducts: [{ type: mongoose.Schema.Types.ObjectId, ref: "products" }],
        boughtTogether: [{ type: mongoose.Schema.Types.ObjectId, ref: "products" }],
        ageGroup: [{ type: mongoose.Schema.Types.ObjectId, ref: "age.groups" }],
        createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
        sku: { type: String, required: true, },
        price: {
            aed: { type: Number, required: true },
            qr: { type: Number },
            sar: { type: Number },
            omr: { type: Number }
        },
        offerPrice: {
            aed: { type: Number },
            qr: { type: Number },
            sar: { type: Number },
            omr: { type: Number }
        },
        offerPercentage: {
            aed: { type: Number },
            qr: { type: Number },
            sar: { type: Number },
            omr: { type: Number }
        },
        likeCount: { type: Number, default: 0 },
        rating: { type: Number, default: 0 },
        description: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        descriptionTwo: {
            en: { type: String, default: "" },
            ar: { type: String, default: "" }
        },
        technicalInfo: [
            {
                title: {
                    en: { type: String },
                    ar: { type: String }
                },
                description: {
                    en: { type: String },
                    ar: { type: String }
                }
            }
        ],
        descriptionImages: [{ type: String }],
        gender: [{ type: String }],
        frameType: { type: mongoose.Schema.Types.ObjectId, ref: "frame.types" },
        frameShape: { type: mongoose.Schema.Types.ObjectId, ref: "frame.shapes" },
        stock: { type: Number, default: 0 },
        productType: { type: String, enum: ["contactLens", "frame", "accessory"] },
        plans: [{ type: mongoose.Schema.Types.ObjectId, ref: "subscribe.plans" }],
        video: { type: String },
        isNewArrival: { type: Boolean, default: false },
        isTryCart: { type: Boolean, default: false },
        isReturnable: { type: Boolean, default: false },
        returnDays: { type: Number },
        isVirtualTry: { type: Boolean, default: false },
        isActive: { type: Boolean, default: true },
        isDelete: { type: Boolean, default: false },
        isChooseLens: { type: Boolean, default: false },
        isAddToCart: { type: Boolean, default: false },
        parent: { type: mongoose.Schema.Types.ObjectId, ref: "product.head" },
        variantDisplayType: { type: String, enum: ["color", "image"], default: 'color' },
        variantImage: { type: String },
        color: { type: mongoose.Schema.Types.ObjectId, ref: "colors" },
        lenseColor: { String },
        size: { type: mongoose.Schema.Types.ObjectId, ref: "sizes" },
        sizeBridge: { type: String },
        templeLength: { type: String },
        contactSize: { type: mongoose.Schema.Types.ObjectId, ref: "contact.sizes" },
        isDefaultVariant: { type: Boolean, default: true },
        label: { type: mongoose.Schema.Types.ObjectId, ref: "labels" },
        weight: { type: Number },
        sph: { type: Boolean },
        cyl: { type: Boolean },
        axis: { type: Boolean },
        sphValues: [{ type: mongoose.Schema.Types.ObjectId, ref: "lens.power" }],
        cylValues: [{ type: mongoose.Schema.Types.ObjectId, ref: "lens.power" }],
        axisValues: [{ type: mongoose.Schema.Types.ObjectId, ref: "lens.power" }],
        addValues: [{ type: mongoose.Schema.Types.ObjectId, ref: "lens.power" }],
        contactSph: [{ type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" }],
        contactAxis: [{ type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" }],
        contactCyl: [{ type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" }],
        multiFocal: { type: Boolean },
        showDiscountPercentage: { type: Boolean },
        isTaxIncluded: { type: Boolean, default: true },
        powerPrice: { type: Number },
        frontMaterial: [{ type: mongoose.Schema.Types.ObjectId, ref: "front.materials" }],
        type: [{ type: mongoose.Schema.Types.ObjectId, ref: "types" }],
        lensMaterial: [{ type: mongoose.Schema.Types.ObjectId, ref: "lens.materials" }],
        supplierSku: { type: String },
        upc: { type: String },
        isCashbackEnabled: { type: Boolean, default: false },
        cashbackPercentage: { type: Number, default: 0 },
        storeId: { type: String, required: true, default: 'ae' },
        seoDetails: {
            title: {
                en: { type: String },
                ar: { type: String }
            },
            description: {
                en: { type: String },
                ar: { type: String }
            },
            keywords: {
                en: { type: String },
                ar: { type: String },
            },
            canonical: {
                en: { type: String },
                ar: { type: String },
            },
            ogImage: { type: String },
        },
    },
    { timestamps: true }
);

productSchema.index({ category: 1 });
productSchema.index({ brand: 1 });
productSchema.index({ refid: 1, }, { unique: true });
productSchema.index({ isActive: 1, isDelete: 1 });

module.exports = mongoose.model("products", productSchema);
