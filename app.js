const express = require('express')
const app = express()
const mongoose = require('mongoose')
const cors = require('cors')
const cronjob = require('node-cron')
const carts = require('./util/cartReminder')
const subscriptions = require('./util/subscriptions')
const orderStatus = require('./util/orderStatus')
const { EventEmitter } = require('events');
const db = require("./models/index");
require('dotenv').config()
const axios = require("axios")

const contants = require('./config/constants')
const port = contants.common.PORT
const dbCon = require('./config/db_config/database')
const routes = require('./config/constants/routes')
const fs = require("fs")
const path = require("path")
const jwt = require("jsonwebtoken");
const { KEYS } = require("./config/constants/key");

const emailHelper = require("./util/emailHelper");
// const { getToken } = require('./controllers/customer.controller')
const { loyaltySchedule, couponSchedule } = require('./controllers/cart.controller')
const { CRM_TOKEN, CRM_ORG_ID, CRM_USERNAME } = require('./config/constants/crm')
const { generateXmlFeed } = require('./controllers/products.controller')
// const { findEmptyImage } = require('./help')
// const { catMod } = require('./help')

const sharp = require('sharp');

mongoose.connect(dbCon.mongoConf.conString, dbCon.mongoConf.options)
    .then(async (res) => {
        console.log("MongoDB connection established");
    })
    .catch((err) => { console.log("Couldnt connect to MongoDB" + err); })

app.use(express.static("public"));
app.use("/uploads", express.static("uploads"));
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true }));

const bus = new EventEmitter();
bus.setMaxListeners(20);

app.use(cors())

// cronjob.schedule('30 3 * * *', async () => {
//     console.log('running a task every day at 23:59:59');
//     await carts.cartReminder();
// }, { scheduled: true });

cronjob.schedule('30 2 * * *', async () => {
    await subscriptions.subscriptionPayment();
}, { scheduled: true });

cronjob.schedule('0 0 * * *', async () => {
    console.log('running a task every minute');
    await orderStatus.orderStatusChange();
}, { scheduled: true });

cronjob.schedule('* * * * *', async () => {
    console.log('running a task every minute');
    await generateXmlFeed()
}, { scheduled: true });

cronjob.schedule('0 * * * *', async () => {
    console.log('running a task every hour');
    await loyaltySchedule()
}, { scheduled: true });

cronjob.schedule('0 * * * *', async () => {
    console.log('running a task every hour');
    await couponSchedule()
}, { scheduled: true });

app.use('/api/backend', routes.backendRouteV1());
app.use('/api/web', routes.webRouteV1());


app.get('/', (req, res, next) => {
    res.send("Hello from server");
})

app.get('/image/:id', async (req, res) => {
    const { id } = req.params;

    try {
        // Construct the file path (you may change the source depending on where the images are stored)
        const filePath = path.join(__dirname, 'uploads/products/', `${id}`);

        // Check if the image exists
        if (!fs.existsSync(filePath)) {
            return res.status(404).send('Image not found');
        }

        // Use sharp to manipulate the image
        const image = sharp(filePath);

        // Get metadata to adjust for resizing
        const metadata = await image.metadata();

        // Calculate new dimensions to zoom out a bit
        const zoomOutFactor = 0.8; // Reduces the image size to 90% (you can adjust this)
        const newWidth = Math.floor(metadata.width * zoomOutFactor);
        const newHeight = Math.floor(metadata.height * zoomOutFactor);

        // Resize to a 1:1 aspect ratio (square)
        const size = Math.min(newWidth, newHeight);

        // Process the image with sharp
        const modifiedImage = await image
            .resize(size, size, { fit: 'contain', background: "white" }) // Resize to a square (1:1 aspect ratio)
            .toBuffer(); // Convert to buffer to send in response

        // Send the modified image as a response
        res.set('Content-Type', 'image/jpeg');
        res.send(modifiedImage);
    } catch (error) {
        console.error('Error processing image:', error);
        res.status(500).send('Internal Server Error');
    }
});

const apiKey = process.env.API_KEY;
const checkApi = (req, res, next) => {
    const key = req.header('yateem-api-key');
    if (key && key === apiKey) {
        next();
    } else {
        res.status(403).json({ message: 'Forbidden: Invalid API Key' });
    }
}

app.put('/order/:orderId/status', checkApi, async (req, res) => {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!status) {
        return res.status(400).json({ message: 'Status is required' });
    }

    try {
        const order = await db.Order.findOneAndUpdate(
            { orderNo: orderId },
            { orderStatus: status },
        );

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        res.json({ message: 'Order status updated', order });
    } catch (error) {
        res.status(500).json({ message: 'Server error', error });
    }
});

app.listen(port, async () => {
    console.log(`app listening at http://localhost:${port}`);

    // catMod()
    // getToken()
    // findEmptyImage()

    // const res = await db.ProductHead.updateMany({}, { storeId: "ae" })
    // console.log(res)

    // try {
    //   const results = await Promise.all([
    //     db.ProductHead.updateMany({}, { storeId: "ae" }),
    //     db.NonList.updateMany({}, { storeId: "ae" }),
    //     db.ContactLensPower.updateMany({}, { storeId: "ae" }),
    //     db.LensMaterial.updateMany({}, { storeId: "ae" }),
    //     db.FrontMaterial.updateMany({}, { storeId: "ae" }),
    //     db.Labels.updateMany({}, { storeId: "ae" }),
    //     db.FrameTypes.updateMany({}, { storeId: "ae" }),
    //     db.Carts.updateMany({}, { storeId: "ae" }),
    //     db.Reviews.updateMany({}, { storeId: "ae" }),
    //     db.Newsletter.updateMany({}, { storeId: "ae" }),
    //     db.LensEnquiries.updateMany({}, { storeId: "ae" }),
    //     db.ProductEnquiry.updateMany({}, { storeId: "ae" }),
    //     db.InsuranceEnquiry.updateMany({}, { storeId: "ae" }),
    //     db.LensPower.updateMany({}, { storeId: "ae" }),
    //     db.LensBrand.updateMany({}, { storeId: "ae" }),
    //     db.InsuranceProvider.updateMany({}, { storeId: "ae" }),
    //     db.ReturnPolicy.updateMany({}, { storeId: "ae" }),
    //     db.ShippingPolicy.updateMany({}, { storeId: "ae" }),
    //     db.RefundPolicy.updateMany({}, { storeId: "ae" }),
    //     db.PrivacyPolicy.updateMany({}, { storeId: "ae" }),
    //     db.Terms.updateMany({}, { storeId: "ae" }),
    //     db.Faq.updateMany({}, { storeId: "ae" }),
    //     db.ContactLens.updateMany({}, { storeId: "ae" }),
    //     db.InsuranceContent.updateMany({}, { storeId: "ae" }),
    //     db.ContactBanner.updateMany({}, { storeId: "ae" }),
    //     db.TryCart.updateMany({}, { storeId: "ae" }),
    //     db.VMPolicy.updateMany({}, { storeId: "ae" }),
    //     db.LensPower.updateMany({}, { storeId: "ae" }),
    //     db.PaymentMethodFee.updateMany({}, { storeId: "ae" }),
    //     db.Footer.updateMany({}, { storeId: "ae" }),
    //     db.FrameShapes.updateMany({}, { storeId: "ae" }),
    //     db.DashboardSettings.updateMany({}, { storeId: "ae" }),
    //     db.HeaderMenu.updateMany({}, { storeId: "ae" }),
    //     db.GeneralSettings.updateMany({}, { storeId: "ae" }),
    //     db.StoreCms.updateMany({}, { storeId: "ae" }),
    //     db.InsuranceContent.updateMany({}, { storeId: "ae" }),
    //     db.Collections.updateMany({}, { storeId: "ae" }),
    //     db.Sizes.updateMany({}, { storeId: "ae" }),
    //     db.Stores.updateMany({}, { storeId: "ae" }),
    //     db.Address.updateMany({}, { storeId: "ae" }),
    //     db.Colors.updateMany({}, { storeId: "ae" }),
    //     db.MetaTags.updateMany({}, { storeId: "ae" }),
    //     db.Order.updateMany({}, { storeId: "ae" }),
    //     db.Texts.updateMany({}, { storeId: "ae" }),
    //     db.Brands.updateMany({}, { storeId: "ae" }),
    // db.Products.updateMany({}, { storeId: "ae" }),
    //     db.Attributes.updateMany({}, { storeId: "ae" }),
    //     db.PrivacyPolicy.updateMany({}, { storeId: "ae" }),
    //     db.AgeGroups.updateMany({}, { storeId: "ae" }),
    //     db.BeforeAfter.updateMany({}, { storeId: "ae" }),
    //     db.CookiePolicy.updateMany({}, { storeId: "ae" }),
    //     db.ContactSize.updateMany({}, { storeId: "ae" }),
    //     db.About.updateMany({}, { storeId: "ae" }),
    //     db.SubCategory.updateMany({}, { storeId: "ae" }),
    //     db.Category.updateMany({}, { storeId: "ae" }),
    //     db.Roles.updateMany({}, { storeId: "ae" }),
    //     db.AdminUser.updateMany({}, { storeId: "ae" }),
    //     db.Banners.updateMany({}, { storeId: "ae" }),
    //     db.Compare.updateMany({}, { storeId: "ae" }),
    //     db.ContactUs.updateMany({}, { storeId: "ae" }),
    //     db.Coupons.updateMany({}, { storeId: "ae" }),
    //     db.Blogs.updateMany({}, { storeId: "ae" }),
    //     db.Terms.updateMany({}, { storeId: "ae" }),
    //     db.Prescriptions.updateMany({}, { storeId: "ae" }),
    //     db.Coating.updateMany({}, { storeId: "ae" }),
    //     db.Type.updateMany({}, { storeId: "ae" }),
    //     db.ImageMap.updateMany({}, { storeId: "ae" }),
    //     db.SubscribeSummary.updateMany({}, { storeId: "ae" })
    //   ]);

    //   console.log("All collections updated with storeId: 'ae'");
    //   console.log("Update results:", results);
    //   return results;
    // } catch (error) {
    //   console.error("Error updating collections:", error);
    //   throw error;
    // }

    // async function createProductHead(product, key) {
    //     const variants = [product?._id];
    //     if (product?.variants?.length > 0) {
    //         for (let variant of product?.variants) {
    //             variants.push(variant);
    //         }
    //     }

    //     const data = {
    //         refid: key + 1,
    //         name: product?.name?.en,
    //         sku: product?.sku,
    //         type: product?.productType,
    //         storeId: "ae",
    //         products: variants
    //     }

    //     const head = await db.ProductHead.create(data)
    //     return { head, variants }
    // }
    // const headPromis = []
    // try {
    //     const products = await db.Products.find({ isDelete: false, isDefaultVariant: true }, { variants: 1, sku: 1, name: 1, productType: 1 });
    //     for (let [key, product] of products.entries()) {
    //         headPromis.push(createProductHead(product, key))
    //     }
    //     const heads = await Promise.all(headPromis)
    //     console.log(heads)
    //     let productPromis = []
    //     for (let head of heads) {
    //         for (let product of head?.variants) {
    //             productPromis.push(db.Products.updateOne({ _id: product }, { parent: head?.head?._id }))
    //         }
    //     }
    //     await Promise.all(productPromis)
    // } catch (error) {
    //     console.log(error)
    // }

    let test = {}
    let productObj = {}
    const products = await db.Products.find({ isDelete: false, storeId: "sa"}, { slug: 1, refid: 1 });
    for (let product of products) {
        if (test[product?.slug]) {
            test[product?.slug].push(product?.refid)
        }else{
            test[product?.slug] = [product?.refid]
        }
    }
    for (let product of Object.keys(test)) {
        if(test[product].length > 1){
            productObj[product] = test[product]
        }
    }
    console.log(productObj)

});
