const express = require('express')
const app = express()
const mongoose = require('mongoose')
const cors = require('cors')
const cronjob = require('node-cron')
const carts = require('./util/cartReminder')
const subscriptions = require('./util/subscriptions')
const orderStatus = require('./util/orderStatus')
const { EventEmitter } = require('events');
const db = require("./models/index");
require('dotenv').config()
const axios = require("axios")

const contants = require('./config/constants')
const port = contants.common.PORT
const dbCon = require('./config/db_config/database')
const routes = require('./config/constants/routes')
const fs = require("fs")
const path = require("path")
const jwt = require("jsonwebtoken");
const { KEYS } = require("./config/constants/key");

const emailHelper = require("./util/emailHelper");
// const { getToken } = require('./controllers/customer.controller')
const { loyaltySchedule, couponSchedule } = require('./controllers/cart.controller')
const { CRM_TOKEN, CRM_ORG_ID, CRM_USERNAME } = require('./config/constants/crm')
const { generateXmlFeed } = require('./controllers/products.controller')
// const { findEmptyImage } = require('./help')
// const { catMod } = require('./help')

const sharp = require('sharp');
const generateUniqueNumber = require('./util/getRefid')

mongoose.connect(dbCon.mongoConf.conString, dbCon.mongoConf.options)
    .then(async (res) => {
        console.log("MongoDB connection established");
    })
    .catch((err) => { console.log("Couldnt connect to MongoDB" + err); })

app.use(express.static("public"));
app.use("/uploads", express.static("uploads"));
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true }));

const bus = new EventEmitter();
bus.setMaxListeners(20);

app.use(cors())

// cronjob.schedule('30 3 * * *', async () => {
//     console.log('running a task every day at 23:59:59');
//     await carts.cartReminder();
// }, { scheduled: true });

cronjob.schedule('30 2 * * *', async () => {
    await subscriptions.subscriptionPayment();
}, { scheduled: true });

cronjob.schedule('0 0 * * *', async () => {
    console.log('running a task every minute');
    await orderStatus.orderStatusChange();
}, { scheduled: true });

cronjob.schedule('* * * * *', async () => {
    console.log('running a task every minute');
    await generateXmlFeed()
}, { scheduled: true });

cronjob.schedule('0 * * * *', async () => {
    console.log('running a task every hour');
    await loyaltySchedule()
}, { scheduled: true });

cronjob.schedule('0 * * * *', async () => {
    console.log('running a task every hour');
    await couponSchedule()
}, { scheduled: true });

app.use('/api/backend', routes.backendRouteV1());
app.use('/api/web', routes.webRouteV1());


app.get('/', (req, res, next) => {
    res.send("Hello from server");
})

app.get('/image/:id', async (req, res) => {
    const { id } = req.params;

    try {
        // Construct the file path (you may change the source depending on where the images are stored)
        const filePath = path.join(__dirname, 'uploads/products/', `${id}`);

        // Check if the image exists
        if (!fs.existsSync(filePath)) {
            return res.status(404).send('Image not found');
        }

        // Use sharp to manipulate the image
        const image = sharp(filePath);

        // Get metadata to adjust for resizing
        const metadata = await image.metadata();

        // Calculate new dimensions to zoom out a bit
        const zoomOutFactor = 0.8; // Reduces the image size to 90% (you can adjust this)
        const newWidth = Math.floor(metadata.width * zoomOutFactor);
        const newHeight = Math.floor(metadata.height * zoomOutFactor);

        // Resize to a 1:1 aspect ratio (square)
        const size = Math.min(newWidth, newHeight);

        // Process the image with sharp
        const modifiedImage = await image
            .resize(size, size, { fit: 'contain', background: "white" }) // Resize to a square (1:1 aspect ratio)
            .toBuffer(); // Convert to buffer to send in response

        // Send the modified image as a response
        res.set('Content-Type', 'image/jpeg');
        res.send(modifiedImage);
    } catch (error) {
        console.error('Error processing image:', error);
        res.status(500).send('Internal Server Error');
    }
});

const apiKey = process.env.API_KEY;
const checkApi = (req, res, next) => {
    const key = req.header('yateem-api-key');
    if (!key) {
        return res.status(401).json({ message: 'Unauthorized: API Key is missing' });
    }
    if (key === apiKey) {
        next();
    } else {
        res.status(403).json({ message: 'Forbidden: Invalid API Key' });
    }
}

app.put('/order/:orderId/status', checkApi, async (req, res) => {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!status) {
        return res.status(400).json({ message: 'Status is required' });
    }

    try {
        const order = await db.Order.findOneAndUpdate(
            { orderNo: orderId },
            { orderStatus: status },
        );

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        res.json({ message: 'Order status updated', order });
    } catch (error) {
        res.status(500).json({ message: 'Server error', error });
    }
});

app.post('/update-stock', checkApi, async (req, res) => {
    let { stock, storeId, sku } = req.body;

    if (!sku) {
        return res.status(400).json({ message: 'SKU is required' });
    }

    if (!stock) {
        return res.status(400).json({ message: 'Stock is required' });
    }

    if (isNaN(stock)) {
        return res.status(400).json({ message: 'Stock must be a number' });
    }

    if (!storeId) {
        return res.status(400).json({ message: 'Store ID is required' });
    }

    try {
        const product = await db.Products.findOneAndUpdate(
            { sku: sku, storeId: storeId, isDelete: false },
            { stock: stock },
        );

        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }

        res.json({ message: 'Product stock updated', product });
    } catch (error) {
        res.status(500).json({ message: 'Server error', error });
    }
});


app.listen(port, async () => {
    console.log(`app listening at http://localhost:${port}`);

    // catMod()
    // getToken()
    // findEmptyImage()

    // const res = await db.SubscribePlans.updateMany({}, { storeId: "ae" })
    // console.log(res)

    // multi store initialization

    // try {
    //   const results = await Promise.all([
    // db.SubscribePlans.updateMany({}, { storeId: "ae" })
    // db.ProductHead.updateMany({}, { storeId: "ae" }),
    // db.NonList.updateMany({}, { storeId: "ae" }),
    // db.ContactLensPower.updateMany({}, { storeId: "ae" }),
    // db.LensMaterial.updateMany({}, { storeId: "ae" }),
    // db.FrontMaterial.updateMany({}, { storeId: "ae" }),
    // db.Labels.updateMany({}, { storeId: "ae" }),
    // db.FrameTypes.updateMany({}, { storeId: "ae" }),
    // db.Carts.updateMany({}, { storeId: "ae" }),
    // db.Reviews.updateMany({}, { storeId: "ae" }),
    // db.Newsletter.updateMany({}, { storeId: "ae" }),
    // db.LensEnquiries.updateMany({}, { storeId: "ae" }),
    // db.ProductEnquiry.updateMany({}, { storeId: "ae" }),
    // db.InsuranceEnquiry.updateMany({}, { storeId: "ae" }),
    // db.LensPower.updateMany({}, { storeId: "ae" }),
    // db.LensBrand.updateMany({}, { storeId: "ae" }),
    // db.InsuranceProvider.updateMany({}, { storeId: "ae" }),
    // db.ReturnPolicy.updateMany({}, { storeId: "ae" }),
    // db.ShippingPolicy.updateMany({}, { storeId: "ae" }),
    // db.RefundPolicy.updateMany({}, { storeId: "ae" }),
    // db.PrivacyPolicy.updateMany({}, { storeId: "ae" }),
    // db.Terms.updateMany({}, { storeId: "ae" }),
    // db.Faq.updateMany({}, { storeId: "ae" }),
    // db.ContactLens.updateMany({}, { storeId: "ae" }),
    // db.InsuranceContent.updateMany({}, { storeId: "ae" }),
    // db.ContactBanner.updateMany({}, { storeId: "ae" }),
    // db.TryCart.updateMany({}, { storeId: "ae" }),
    // db.VMPolicy.updateMany({}, { storeId: "ae" }),
    // db.LensPower.updateMany({}, { storeId: "ae" }),
    // db.PaymentMethodFee.updateMany({}, { storeId: "ae" }),
    // db.Footer.updateMany({}, { storeId: "ae" }),
    // db.FrameShapes.updateMany({}, { storeId: "ae" }),
    // db.DashboardSettings.updateMany({}, { storeId: "ae" }),
    // db.HeaderMenu.updateMany({}, { storeId: "ae" }),
    // db.GeneralSettings.updateMany({}, { storeId: "ae" }),
    // db.StoreCms.updateMany({}, { storeId: "ae" }),
    // db.InsuranceContent.updateMany({}, { storeId: "ae" }),
    // db.Collections.updateMany({}, { storeId: "ae" }),
    // db.Sizes.updateMany({}, { storeId: "ae" }),
    // db.Stores.updateMany({}, { storeId: "ae" }),
    // db.Address.updateMany({}, { storeId: "ae" }),
    // db.Colors.updateMany({}, { storeId: "ae" }),
    // db.MetaTags.updateMany({}, { storeId: "ae" }),
    // db.Order.updateMany({}, { storeId: "ae" }),
    // db.Texts.updateMany({}, { storeId: "ae" }),
    // db.Brands.updateMany({}, { storeId: "ae" }),
    // db.Products.updateMany({}, { storeId: "ae" }),
    // db.Attributes.updateMany({}, { storeId: "ae" }),
    // db.PrivacyPolicy.updateMany({}, { storeId: "ae" }),
    // db.AgeGroups.updateMany({}, { storeId: "ae" }),
    // db.BeforeAfter.updateMany({}, { storeId: "ae" }),
    // db.CookiePolicy.updateMany({}, { storeId: "ae" }),
    // db.ContactSize.updateMany({}, { storeId: "ae" }),
    // db.About.updateMany({}, { storeId: "ae" }),
    // db.SubCategory.updateMany({}, { storeId: "ae" }),
    // db.Category.updateMany({}, { storeId: "ae" }),
    // db.Roles.updateMany({}, { storeId: "ae" }),
    // db.AdminUser.updateMany({}, { storeId: "ae" }),
    // db.Banners.updateMany({}, { storeId: "ae" }),
    // db.Compare.updateMany({}, { storeId: "ae" }),
    // db.ContactUs.updateMany({}, { storeId: "ae" }),
    // db.Coupons.updateMany({}, { storeId: "ae" }),
    // db.Blogs.updateMany({}, { storeId: "ae" }),
    // db.Terms.updateMany({}, { storeId: "ae" }),
    // db.Prescriptions.updateMany({}, { storeId: "ae" }),
    // db.Coating.updateMany({}, { storeId: "ae" }),
    // db.Type.updateMany({}, { storeId: "ae" }),
    // db.ImageMap.updateMany({}, { storeId: "ae" }),
    // db.SubscribeSummary.updateMany({}, { storeId: "ae" })
    //   ]);

    //   console.log("All collections updated with storeId: 'ae'");
    //   console.log("Update results:", results);
    //   return results;
    // } catch (error) {
    //   console.error("Error updating collections:", error);
    //   throw error;
    // }

    // Product head creation

    // async function createProductHead(product, key) {
    //     const variants = [product?._id];
    //     if (product?.variants?.length > 0) {
    //         for (let variant of product?.variants) {
    //             const product = await db.Products.findOne({ _id: variant }, { isDelete: 1 });
    //             if (product?.isDelete) continue;
    //             variants.push(variant);
    //         }
    //     }

    //     const data = {
    //         refid: generateUniqueNumber(),
    //         name: product?.name?.en,
    //         sku: product?.sku,
    //         type: product?.productType,
    //         storeId: "ae",
    //         products: variants
    //     }

    //     const head = await db.ProductHead.create(data)
    //     return { head, variants }
    // }
    // const headPromis = []
    // try {
    //     const products = await db.Products.find({ isDelete: false, isDefaultVariant: true, storeId: "ae" }, { variants: 1, sku: 1, name: 1, productType: 1 });
    //     for (let [key, product] of products.entries()) {
    //         headPromis.push(createProductHead(product, key))
    //     }
    //     const heads = await Promise.all(headPromis)
    //     console.log(heads)
    //     let productPromis = []
    //     for (let head of heads) {
    //         for (let product of head?.variants) {
    //             productPromis.push(db.Products.updateOne({ _id: product }, { parent: head?.head?._id }))
    //         }
    //     }
    //     await Promise.all(productPromis)
    //     console.log("Product heads created")
    // } catch (error) {
    //     console.log(error)
    // }

    // size, price, offerPrice, stock correction 

    // const products = await db.Products.find({ $or: [{ price: { $exists: false } }, { "price.aed": null }, { size: { $exists: false } }, { size: null }], productType: "frame", isDelete: false },);
    // // const products = await db.Products.find({ $or: [{ price: { $exists: false } }, { "price.aed": null }], productType: "contactLens", isDelete: false },);
    // const promises = []
    // console.log(products.length)
    // let zero = [], one = [], more = [];
    // for (let product of products) {
    //     if (product?.sizes?.length == 1) {
    //         one.push(product)
    //         promises.push(db.Products.updateOne({ _id: product?._id }, { price: { aed: product?.sizes[0]?.price }, offerPrice: { aed: product?.sizes[0]?.offerPrice }, stock: product?.sizes[0]?.stock, size: product?.sizes[0]?.size }))
    //     }
    //     if (product?.sizes?.length < 1) {
    //         zero.push(product)
    //     }
    //     if (product?.sizes?.length > 1) {
    //         more.push(product)
    //     }
    // }
    // console.log("Zeros: ", zero.length)
    // console.log("Ones: ", one.length)
    // console.log("Mores: ", more.length)
    // const res = await Promise.all(promises)
    // console.log(res)

    // // fix colors
    // const products = await db.Products.find({ $or: [{ color: { $exists: false } }, { color: null }], isDelete: false}, { color: 1, name: 1, sku: 1 });
    // console.log(products.length)
    // const promises = []
    // for (let product of products) {
    //       console.log(product.sku)
    // }
    // const res = await Promise.all(promises)

    // // fix duplicate colors
    // const colors = await db.Colors.find({ isDelete: false }, { name: 1 });
    // const promises = []
    // const colorsRef = {}
    // for (let color of colors) {
    //     if(colorsRef[color?.name?.en]){
    //         colorsRef[color?.name?.en].push(color?._id)
    //     }else{
    //         colorsRef[color?.name?.en] = [color?._id]
    //     }
    // }
    // for (let color of Object.keys(colorsRef)) {
    //     if(colorsRef[color]?.length > 1){
    //         const mainColor = await db.Colors.findOne({ "name.en": color, color: { $exists: true, $ne: null } , isDelete: false }, { name: 1 });
    //         const otherColor = colorsRef[color].filter(i => i?.toString() != mainColor?._id?.toString())
    //         await db.Products.updateMany({ color: { $in: otherColor } }, { color: mainColor?._id })
    //         await db.Colors.updateMany({ _id: { $in: otherColor } }, { isDelete: true })
    //         console.log(color, "Corrected")
    //     }
    // }

    //brand poster fix
    // const brands = await db.Brands.find({ isDelete: false, storeId: "ae" });

    // for (let brand of brands) {
    //     console.log(brand?.name?.en)
    //     const page = brand?.page ?? []
    //     const poster = page.find(item => item?.type == "banner") ?? -1
    //     if (poster == -1){
    //         page.push({
    //             type: "banner",
    //             bannerType: "image",
    //             title: "Banner/Video",
    //             banner: {
    //                 en: brand?.banner?.en,
    //                 ar: brand?.banner?.ar,
    //             }
    //         })
    //     }else{
    //         page[poster] = {
    //             type: "banner",
    //             bannerType: "image",
    //             title: "Banner/Video",
    //             banner: {
    //                 en: brand?.banner?.en,
    //                 ar: brand?.banner?.ar,
    //             }
    //         }
    //     }
    //     await db.Brands.updateOne({ _id: brand?._id }, { page })
    // }
    // console.log("================end===============")

    //fix category
    // const res = await db.Products.updateMany({category: '663869b27f5377cf4b622238', storeId: 'ae', isDelete: false}, { productType: "accessory" })
    // console.log(res)

});
