const mongoose = require('mongoose')

const newsletterSchema = new mongoose.Schema({
    email: { type: String, required: true },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('newsletter', newsletterSchema)