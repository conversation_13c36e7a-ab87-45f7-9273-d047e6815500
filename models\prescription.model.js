const mongoose = require('mongoose');

const prescriptionSchema = new mongoose.Schema({
    file: { type: String, required: true },
    title: { type: String, required: true },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers" },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('prescriptions', prescriptionSchema);