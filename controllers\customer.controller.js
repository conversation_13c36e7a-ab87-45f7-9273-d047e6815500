const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/customer.service");
const db = require("../models");
const slug = require("../util/slug");
const addressService = require("../services/address.service");
const jwt = require("jsonwebtoken");
const { KEYS } = require("../config/constants/key");
const productService = require("../services/products.service");
const cartService = require("../services/cart.service");
const tryCartService = require("../services/trycart.service");
const orderService = require("../services/order.service");
const sizeService = require("../services/size.service");
const { default: axios } = require("axios");
const moment = require("moment");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const fs = require("fs");
const lensEnquiryService = require("../services/lens.enquiries.service");
const csv = require("csv-parser");
const firebaseCreds = require("../config/db_config/firebase");
const firebase = require("firebase-admin");
const { getAuth } = require("firebase-admin/auth");
firebase.initializeApp({ credential: firebase.credential.cert(firebaseCreds["firebaseConfig"]) });
const contactLensPowerService = require("../services/contactLens.power.service");
const { CRM_USERNAME, CRM_TOKEN, CRM_ORG_ID } = require("../config/constants/crm");

exports.validate = (method) => {
    switch (method) {
        case "create": {
            return [body("name", "Name is required").exists(), body("email", "Email is required").exists(), body("mobile", "Mobile is required").exists()];
        }
        case "add-address": {
            return [body("customer", "Customer is required").exists(), body("name", "Name is required").exists()];
        }
        case "add-try-cart": {
            return [body("product", "Product is required").exists()];
        }
    }
};

exports.create = async (req, res) => {
    try {
        const { body } = req;
        const existingCustomer = await service.findOne({
            $or: [{ mobile: body?.mobile }, { email: body?.email }],
            isDelete: false,
        });

        if (existingCustomer) {
            const errorMessage = existingCustomer.mobile === body?.mobile ? "Customer with this mobile number already exists" : "Customer with this email already exists";
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: 1,
                    error_message: errorMessage,
                }
            );
        }

        body.slug = await slug.createSlug(db.Customers, body?.name, {
            slug: await slug.generateSlug(body?.name),
        });
        body.refid = (await service.count({})) + 1;
        body.isGuest = false;
        const response = await service.create(body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.CUSTOMERS.CREATED.error_code,
            error_message: messages.CUSTOMERS.CREATED.error_message,
        });
    } catch (error) {
        console.error("error :: ", error);
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getCustomers = async (req, res) => {
    try {
        let { page, limit, sort } = req.query;

        page = page ?? 0
        limit = limit ?? 0

        console.log(page, limit)

        const { body } = req;
        const filters = body?.filters ?? {}
        let query = { isDelete: false, ...filters };
        if (body?.isActive) query.isActive = body?.isActive;
        if (body?.fromDate) {
            query.createdAt = { $gte: new Date(body.fromDate) };
        }

        // Check for toDate
        if (body?.toDate) {
            if (query.createdAt) {
                // If fromDate is present, set the upper bound
                query.createdAt.$lte = new Date(body.toDate);
            } else {
                // If only toDate is present, get customers before that date
                query.createdAt = { $lte: new Date(body.toDate) };
            }
        }

        if (body?.keyword) {
            query["$or"] = [
                { "name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "mobile": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "email": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
            ]
        }

        const [customers, total] = await Promise.all([
            service.find(query, {}, page, limit),
            service.count(query)
        ])

        return helper.deliverResponse(res, 200, {
            customers,
            total
        }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.customerDetails = async (req, res) => {
    try {
        const customerDetails = await service.findOne({ refid: req?.params?.refid, isDelete: false });
        const addresses = await addressService.find({
            customer: customerDetails?._id,
            isDelete: false,
        });
        return helper.deliverResponse(res, 200, { customerDetails, addresses }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.update = async (req, res) => {
    try {
        const { body } = req;
        const existingCustomer = await service.findOne({
            $or: [{ mobile: body?.mobile, countryCode: body?.countryCode, isDelete: false }, { email: body?.email, isDelete: false }],
            refid: { $ne: body?.refid },
        });

        if (existingCustomer) {
            const errorMessage = existingCustomer.mobile === body?.mobile ? "Customer with this mobile number already exists" : "Customer with this email already exists";
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: 1,
                    error_message: errorMessage,
                }
            );
        }

        const customerDetails = await service.findOne({ refid: body?.refid, isDelete: false });
        if (body?.name !== customerDetails?.name)
            body.slug = await slug.createSlug(db.Customers, body?.name, {
                slug: await slug.generateSlug(body?.name),
            });
        body.isGuest = false;
        const response = await service.update({ refid: body?.refid, isDelete: false }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.CUSTOMERS.UPDATED.error_code,
            error_message: messages.CUSTOMERS.UPDATED.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addAddress = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
        }
        let { body } = req;
        const { customerId } = res?.locals?.user;
        const customerDetails = await service.findOne({ refid: customerId, isDelete: false });
        body.customer = customerDetails?._id;
        body.refid = (await addressService.count({})) + 1;
        body.storeId = storeid
        if (body?.isDefault == true) {
            const projection = {
                __v: 0,
                createdAt: 0,
                updatedAt: 0,
                _id: 0,
                isActive: 0,
                isDelete: 0,
            };
            const response = await addressService.find({ customer: customerDetails?._id, isActive: true, isDelete: false, storeId: storeid }, projection);
            if (response.length > 0) {
                for (let address of response) if (address?.isDefault) await addressService.update({ refid: address?.refid }, { isDefault: false });
            }
            body.isDefault = true;
        }
        const response = await addressService.create(body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.ADDRESS.CREATED.error_code,
            error_message: messages.ADDRESS.CREATED.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getAddress = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { customerId } = res?.locals?.user;
        const customerDetails = await service.findOne({ refid: customerId, isDelete: false });
        const response = await addressService.find({
            customer: customerDetails?._id,
            isDelete: false,
            storeId: storeid
        });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addressDetails = async (req, res) => {
    try {
        let { body } = req;
        const response = await addressService.findOne({ refid: body?.refid, isDelete: false });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.updateAddress = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        let { refid } = req.params;
        const { body } = req;
        if (body?.isDefault == true) {
            const defaultAddress = await addressService.update(
                {
                    customer: body?.customer,
                    isDefault: true,
                    isDelete: false,
                    storeId: storeid
                },
                { isDefault: false }
            );
        }
        const response = await addressService.update({ refid: refid, isDelete: false, storeId: storeid }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.ADDRESS.UPDATED.error_code,
            error_message: messages.ADDRESS.UPDATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.deleteAddress = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const response = await addressService.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, { isDelete: true });
        if (!response) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.ADDRESS.NOT_FOUND.error_code,
                    error_message: messages.ADDRESS.NOT_FOUND.error_message,
                }
            );
        }
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.ADDRESS.DELETED.error_code,
            error_message: messages.ADDRESS.DELETED.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getOtp = async (req, res) => {
    try {
        let { body } = req;
        if (body?.mobile) {
            const mobile = body?.mobile[0] == "0" ? body?.mobile.slice(1) : body?.mobile;
            const countryCode = body?.countryCode;
            const isTest = mobile == "555544444" ? true : false;
            const otp = isTest ? 888888 : Math.floor(100000 + Math.random() * 900000);
            const otpData = new db.Otp({ mobile, countryCode, otp });
            await otpData.save();
            const customer = await service.findOne({ mobile, countryCode, isActive: false });
            if (customer) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Customer is blocked",
                    }
                );
            }
            const response = await axios.post('https://myinboxmedia.in/api/sms/SendSMS', {
                userid: process.env.MIM_USERID,
                pwd: process.env.MIM_PWD,
                mobile: countryCode?.trim() + mobile?.trim(),
                sender: "YATEEM",
                msg: `Your Yateem.com verification code is ${otp}. This code will expire in 10 minutes.`,
                msgtype: 16
            })
            console.log(response.data)
            console.log("otp: " + otp)
            helper.deliverResponse(
                res,
                200,
                {},
                {},
                {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: messages.SUCCESS_RESPONSE.error_message,
                }
            );
        } else {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: 1,
                    error_message: "Mobile number is required",
                }
            );
        }
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

function isLensPriceable(lensData) {
    if (lensData?.name != "0.00" && lensData?.name != "None" && lensData?.name != "none" && lensData?.name != "NONE") {
        return true;
    }
}

exports.verifyOtp = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { body } = req;
        const { devicetoken } = req?.headers;
        // getAuth().getUser(body['uuid']).then(async (response) => {
        //if(body?.countryCode + body?.mobile == response?.phoneNumber)
        if (body?.otp && body?.mobile && body?.countryCode) {
            const storedOtp = await db.Otp.findOne({
                mobile: body?.mobile[0] == "0" ? body?.mobile.slice(1) : body?.mobile,
                otp: body?.otp,
                countryCode: body?.countryCode
            });
            console.log(body?.otp, body?.mobile, body?.countryCode, storedOtp)
            if (storedOtp) {
                if (storedOtp["otp"] == body?.otp) {
                    if (Date.now() > storedOtp["expiry"]) {
                        await db.Otp.deleteOne({
                            mobile: body?.mobile[0] == "0" ? body?.mobile.slice(1) : body?.mobile,
                            otp: body?.otp,
                            countryCode: body?.countryCode
                        });
                        return helper.deliverResponse(
                            res,
                            200,
                            {},
                            {
                                error_code: messages.CUSTOMERS.OTP_EXPIRED.error_code,
                                error_message: messages.CUSTOMERS.OTP_EXPIRED.error_message
                            }
                        );
                    }
                    const customer = await service.findOne({
                        mobile: storedOtp["mobile"],
                        countryCode: storedOtp["countryCode"],
                        isDelete: false,
                        isGuest: false
                    });
                    if (customer) {
                        if (!customer?.loyaltyNumber) {
                            const loyalty = await axios.post("https://app.optculture.com/subscriber/updateContactsOPT.mqrm", {
                                header: {
                                    requestId: customer?._id || Math.random(),
                                    requestDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                                    contactSource: "eComm",
                                    contactList: "POS",
                                },
                                customer: {
                                    homeStore: "1",
                                    customerId: customer?._id,
                                    firstName: customer?.name,
                                    emailAddress: customer?.email,
                                    phone: customer?.mobile,
                                    creationDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                                    loyalty: {
                                        enrollCustomer: "Y",
                                    },
                                },
                                user: {
                                    // userName: "webcastle",
                                    // organizationId: "webcastle",
                                    // token: "0J3RIVME081FARD3",
                                    userName: CRM_USERNAME, // Mandatory field
                                    token: CRM_TOKEN, // Mandatory field
                                    organizationId: CRM_ORG_ID, // Mandatory field
                                },

                            });

                            if (loyalty.data.status?.errorCode == "0") {
                                await service.update(
                                    { refid: customer?.refid, isDelete: false },
                                    { loyaltyNumber: loyalty?.data?.membership?.cardNumber }
                                );
                            }
                        } else {
                            console.log("customer is enrolled")
                        }
                        const payload = {
                            customerId: customer?.refid,
                            mobile: customer?.mobile
                        };
                        const token = jwt.sign(payload, KEYS.DEV.JWTSECRET, {
                            expiresIn: KEYS.DEV.JWT_EXPIRE
                        });
                        let tokens = customer?.tokens ? customer?.tokens : [];
                        tokens.push(token);
                        await service.update(
                            { refid: customer?.refid, isDelete: false },
                            { tokens: tokens }
                        );
                        const response = {
                            token: token,
                            customerId: customer?.refid,
                            isExisting: true,
                            customer: {
                                name: customer?.name
                            }
                        };
                        await db.Otp.deleteOne({
                            mobile: body?.mobile[0] == "0" ? body?.mobile.slice(1) : body?.mobile,
                            otp: body?.otp,
                            countryCode: body?.countryCode
                        });
                        const cart = await cartService.findOne({
                            devicetoken: devicetoken,
                            isDelete: false,
                            isPurchased: false,
                            isActive: true,
                            storeid
                        });
                        const localCarts = await cartService.find({
                            devicetoken: devicetoken,
                            isDelete: false,
                            isPurchased: false,
                            isActive: true,
                            customer: { $exists: false }
                        });

                        for (let cart of localCarts) {
                            const customerCart = await cartService.findOne({
                                customer: customer?._id,
                                isDelete: false,
                                isPurchased: false,
                                isActive: true,
                                storeId: cart?.storeId
                            });

                            if (!customerCart) {
                                await cartService.update(
                                    { _id: cart?._id },
                                    { customer: customer?._id }
                                );
                                return;
                            }

                            const cartProducts = cart?.products ? cart?.products : [];
                            const customerCartProducts = customerCart?.products
                                ? customerCart?.products
                                : [];
                            const allProducts = [...cartProducts, ...customerCartProducts];
                            let productMap = new Map();
                            let baseTotal = 0

                            for (let product of allProducts) {
                                const productDetails = await productService.findOne({
                                    _id: product?.product?._id,
                                    isDelete: false,
                                    storeId: cart?.storeId
                                });
                                if (product?.contactLensHash) {
                                    if (!productMap.get(product.contactLensHash)) {
                                        flag = false;
                                        if (product.contactLens?.sphLeft) {
                                            const leftSphData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.sphLeft
                                            })
                                            if (leftSphData) {
                                                leftSph = leftSphData?.name
                                                flag = isLensPriceable(leftSphData)
                                            }
                                        }

                                        if (product.contactLens?.sphRight) {
                                            const rightSphData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.sphRight
                                            })
                                            if (rightSphData) {
                                                rightSph = rightSphData?.name
                                                flag = isLensPriceable(rightSphData)
                                            }
                                        }

                                        if (product.contactLens?.cylLeft) {
                                            const leftCylData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.cylLeft
                                            })
                                            if (leftCylData) {
                                                leftCyl = leftCylData?.name
                                                flag = isLensPriceable(leftCylData)
                                            }
                                        }

                                        if (product.contactLens?.cylRight) {
                                            const rightCylData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.cylRight
                                            })
                                            if (rightCylData) {
                                                rightCyl = rightCylData?.name
                                                flag = isLensPriceable(rightCylData)
                                            }
                                        }

                                        if (product.contactLens?.axisLeft) {
                                            const leftAxisData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.axisLeft
                                            })
                                            if (leftAxisData) {
                                                leftAxis = leftAxisData?.name
                                                flag = isLensPriceable(leftAxisData)
                                            }
                                        }

                                        if (product.contactLens?.axisRight) {
                                            const rightAxisData = await contactLensPowerService.findOne({
                                                _id: product.contactLens?.axisRight
                                            })
                                            if (rightAxisData) {
                                                rightAxis = rightAxisData?.name
                                                flag = isLensPriceable(rightAxisData)
                                            }
                                        }

                                        const productPrice = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                                            ? productDetails?.offerPrice?.aed
                                            : productDetails?.price?.aed;
                                        const powerPrice = flag ? productDetails?.powerPrice ? productDetails?.powerPrice : 0 : 0;
                                        const unitPrice = productPrice + (powerPrice ?? 0);
                                        baseTotal += (unitPrice * (product?.contactLens?.multiple ? 2 * product?.quantity : product?.quantity));
                                        productMap.set(product.contactLensHash, product)
                                    }
                                } else {
                                    if (productMap.get(product?.product?._id?.toString())) {
                                        if (productMap.get(product?.product?._id?.toString()).size?.toString() !== product?.size?.toString()) {
                                            let price = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                                                ? productDetails?.offerPrice?.aed
                                                : productDetails?.price?.aed;
                                            let totalPrice = price * product?.quantity;
                                            console.log("totalPrice", totalPrice)
                                            baseTotal += totalPrice

                                            productMap.set(product?.product?._id?.toString(), product)
                                        }
                                    } else {
                                        let price = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                                            ? productDetails?.offerPrice?.aed
                                            : productDetails?.price?.aed;
                                        let totalPrice = price * product?.quantity;
                                        baseTotal += totalPrice

                                        productMap.set(product?.product?._id?.toString(), product)
                                    }
                                }
                            }

                            await cartService.update(
                                {
                                    customer: customer?._id,
                                    isDelete: false,
                                    isPurchased: false,
                                    isActive: true,
                                    storeId: cart?.storeId
                                },
                                { products: Array.from(productMap.values()), baseTotal, total: baseTotal }
                            );
                            await cartService.update(
                                {
                                    _id: cart?._id
                                },
                                { products: [], baseTotal: 0, total: 0 }
                            );

                        }

                        const customerCart = await cartService.findOne({
                            customer: customer?._id,
                            isDelete: false,
                            isPurchased: false,
                            isActive: true
                        });
                        const cartProducts = cart?.products ? cart?.products : [];
                        const customerCartProducts = customerCart?.products
                            ? customerCart?.products
                            : [];

                        // // console.log("customerCart :: ", customerCart?.products);
                        // // console.log("cart :: ", cart?.products);
                        // let productMap = new Map();
                        // const allProducts = [...cartProducts, ...customerCartProducts];
                        // let baseTotal = 0
                        // // console.log(allProducts)
                        // for (let product of allProducts) {
                        //     const productDetails = await productService.findOne({
                        //         _id: product?.product?._id,
                        //         isDelete: false,
                        //     });
                        //     if (product?.contactLensHash) {
                        //         if (!productMap.get(product.contactLensHash)) {
                        //             flag = false;
                        //             if (product.contactLens?.sphLeft) {
                        //                 const leftSphData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.sphLeft
                        //                 })
                        //                 if (leftSphData) {
                        //                     leftSph = leftSphData?.name
                        //                     flag = isLensPriceable(leftSphData)
                        //                 }
                        //             }

                        //             if (product.contactLens?.sphRight) {
                        //                 const rightSphData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.sphRight
                        //                 })
                        //                 if (rightSphData) {
                        //                     rightSph = rightSphData?.name
                        //                     flag = isLensPriceable(rightSphData)
                        //                 }
                        //             }

                        //             if (product.contactLens?.cylLeft) {
                        //                 const leftCylData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.cylLeft
                        //                 })
                        //                 if (leftCylData) {
                        //                     leftCyl = leftCylData?.name
                        //                     flag = isLensPriceable(leftCylData)
                        //                 }
                        //             }

                        //             if (product.contactLens?.cylRight) {
                        //                 const rightCylData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.cylRight
                        //                 })
                        //                 if (rightCylData) {
                        //                     rightCyl = rightCylData?.name
                        //                     flag = isLensPriceable(rightCylData)
                        //                 }
                        //             }

                        //             if (product.contactLens?.axisLeft) {
                        //                 const leftAxisData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.axisLeft
                        //                 })
                        //                 if (leftAxisData) {
                        //                     leftAxis = leftAxisData?.name
                        //                     flag = isLensPriceable(leftAxisData)
                        //                 }
                        //             }

                        //             if (product.contactLens?.axisRight) {
                        //                 const rightAxisData = await contactLensPowerService.findOne({
                        //                     _id: product.contactLens?.axisRight
                        //                 })
                        //                 if (rightAxisData) {
                        //                     rightAxis = rightAxisData?.name
                        //                     flag = isLensPriceable(rightAxisData)
                        //                 }
                        //             }
                        //             if (product?.size && productDetails?.customizable) {
                        //                 const sizeDetails = productDetails?.contactSizes?.find((size) => size?.size?._id == product?.size);
                        //                 const productPrice =
                        //                     sizeDetails?.offerPrice ?
                        //                         sizeDetails?.offerPrice < sizeDetails?.price ?
                        //                             sizeDetails?.offerPrice
                        //                             : sizeDetails?.price
                        //                         : sizeDetails?.price;

                        //                 const powerPrice = flag ? productDetails?.powerPrice ? productDetails?.powerPrice : 0 : 0;
                        //                 const unitPrice = productPrice + powerPrice;
                        //                 console.log("unitPrice", unitPrice)
                        //                 baseTotal += (unitPrice * (product?.contactLens?.multiple ? 2 * product?.quantity : product?.quantity));
                        //             } else {
                        //                 const productPrice =
                        //                     productDetails?.offerPrice?.aed ?
                        //                         productDetails?.offerPrice?.aed < productDetails?.price?.aed ?
                        //                             productDetails?.offerPrice?.aed
                        //                             : productDetails?.price?.aed
                        //                         : productDetails?.price?.aed;
                        //                 const powerPrice = flag ? productDetails?.powerPrice ? productDetails?.powerPrice : 0 : 0;
                        //                 const unitPrice = productPrice + powerPrice;
                        //                 console.log("unitPrice", unitPrice)
                        //                 baseTotal += (unitPrice * (product?.contactLens?.multiple ? 2 * product?.quantity : product?.quantity));
                        //             }
                        //             productMap.set(product.contactLensHash, product)
                        //         }
                        //     } else {
                        //         if (productMap.get(product?.product?._id?.toString())) {
                        //             if (productMap.get(product?.product?._id?.toString()).size?.toString() !== product?.size?.toString()) {
                        //                 if (product?.size && productDetails?.customizable) {
                        //                     const sizeDetails = productDetails?.sizes?.find((size) => size?.size?._id?.toString() == product?.size?.toString());
                        //                     console.log("sizeDetails", sizeDetails)
                        //                     let totalPrice =
                        //                         sizeDetails?.offerPrice ?
                        //                             sizeDetails?.offerPrice < sizeDetails?.price ?
                        //                                 sizeDetails?.offerPrice * product?.quantity
                        //                                 : sizeDetails?.price * product?.quantity
                        //                             : sizeDetails?.price * product?.quantity;
                        //                     console.log("totalPrice", totalPrice)
                        //                     baseTotal += totalPrice
                        //                 } else {
                        //                     let totalPrice =
                        //                         productDetails?.offerPrice?.aed ?
                        //                             productDetails?.offerPrice?.aed < productDetails?.price?.aed ?
                        //                                 productDetails?.offerPrice?.aed * product?.quantity
                        //                                 : productDetails?.price?.aed * product?.quantity
                        //                             : productDetails?.price?.aed * product?.quantity;
                        //                     console.log("totalPrice", totalPrice)
                        //                     baseTotal += totalPrice
                        //                 }
                        //                 productMap.set(product?.product?._id?.toString(), product)
                        //             }
                        //         } else {
                        //             if (product?.size && productDetails?.customizable) {
                        //                 const sizeDetails = productDetails?.sizes?.find((size) => size?.size?._id?.toString() == product?.size?.toString());
                        //                 let totalPrice =
                        //                     sizeDetails?.offerPrice ?
                        //                         sizeDetails?.offerPrice < sizeDetails?.price ?
                        //                             sizeDetails?.offerPrice * product?.quantity
                        //                             : sizeDetails?.price * product?.quantity
                        //                         : sizeDetails?.price * product?.quantity;
                        //                 console.log("totalPrice", totalPrice)
                        //                 baseTotal += totalPrice
                        //             } else {
                        //                 let totalPrice =
                        //                     productDetails?.offerPrice?.aed ?
                        //                         productDetails?.offerPrice?.aed < productDetails?.price?.aed ?
                        //                             productDetails?.offerPrice?.aed * product?.quantity
                        //                             : productDetails?.price?.aed * product?.quantity
                        //                         : productDetails?.price?.aed * product?.quantity;
                        //                 console.log("totalPrice", totalPrice)
                        //                 baseTotal += totalPrice
                        //             }
                        //             productMap.set(product?.product?._id?.toString(), product)
                        //         }
                        //     }
                        // }
                        // // [...cartProducts, ...customerCartProducts]?.forEach((obj) => {
                        // //     productMap.set(obj.product, obj);
                        // // });
                        // // console.log("productMap :: ", productMap);
                        // // console.log(baseTotal)

                        // if (cart && customerCart)
                        //     await cartService.update(
                        //         {
                        //             customer: customer?._id,
                        //             isDelete: false,
                        //             isPurchased: false,
                        //             isActive: true
                        //         },
                        //         { products: Array.from(productMap.values()), baseTotal, total: baseTotal }
                        //     );
                        // if (cart && !customerCart)
                        //     await cartService.update(
                        //         { _id: cart?._id },
                        //         { customer: customer?._id }
                        //     );
                        return helper.deliverResponse(res, 200, response, {
                            error_code: messages.CUSTOMERS.LOGIN.error_code,
                            error_message: messages.CUSTOMERS.LOGIN.error_message
                        });
                    } else {
                        const customer = await service.create({
                            mobile: storedOtp["mobile"],
                            countryCode: storedOtp["countryCode"],
                            refid: (await service.count({})) + 1
                        });
                        const payload = {
                            customerId: customer?.refid,
                            mobile: customer?.mobile
                        };
                        const token = jwt.sign(payload, KEYS.DEV.JWTSECRET, {
                            expiresIn: KEYS.DEV.JWT_EXPIRE
                        });
                        let tokens = customer?.tokens ? customer?.tokens : [];
                        tokens.push(token);
                        await service.update(
                            { refid: customer?.refid, isDelete: false },
                            { tokens: tokens }
                        );
                        const response = {
                            token: token,
                            customerId: customer?.refid,
                            isExisting: false
                        };
                        await db.Otp.deleteOne({
                            mobile: body?.mobile[0] == "0" ? body?.mobile.slice(1) : body?.mobile,
                            otp: body?.otp,
                            countryCode: body?.countryCode
                        });
                        const carts = await cartService.find({
                            devicetoken: devicetoken,
                            isDelete: false,
                            isPurchased: false,
                            isActive: true,
                            customer: { $exists: false }
                        });
                        for (let cart of carts) {
                            await cartService.update(
                                { _id: cart?._id, storeId: cart?.storeId },
                                { customer: customer?._id }
                            );
                        }

                        return helper.deliverResponse(res, 200, response, {
                            error_code: messages.CUSTOMERS.LOGIN.error_code,
                            error_message: messages.CUSTOMERS.LOGIN.error_message
                        });
                    }
                } else {
                    return helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.CUSTOMERS.INVALID_OTP.error_code,
                            error_message: messages.CUSTOMERS.INVALID_OTP.error_message
                        }
                    );
                }
            } else {
                return helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.CUSTOMERS.OTP_EXPIRED.error_code,
                        error_message: messages.CUSTOMERS.OTP_EXPIRED.error_message
                    }
                );
            }
        } else {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.CUSTOMERS.OTP_REQUIRED.error_code,
                    error_message: messages.CUSTOMERS.OTP_REQUIRED.error_message
                }
            );
        }
        // })
    } catch (error) {
        console.log(error);
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

// exports.verifyOtp = async (req, res) => {
//     try {
//         const { body } = req;
//         const { devicetoken } = req?.headers;
//         getAuth()
//             .getUser(body["uid"])
//             .then(async (response) => {
//                 // console.log("firebase response :: ", response)
//                 if (body?.countryCode + body?.mobile == response?.phoneNumber) {
//                     // if (body?.otp && body?.mobile && body?.countryCode) {
//                     // const storedOtp = await db.Otp.findOne({
//                     //     mobile: body?.mobile,
//                     //     otp: body?.otp,
//                     //     countryCode: body?.countryCode
//                     // });
//                     // if (storedOtp) {
//                     // if (storedOtp["otp"] == body?.otp) {
//                     // if (Date.now() > storedOtp["expiry"]) {
//                     //     await db.Otp.deleteOne({
//                     //         mobile: body?.mobile,
//                     //         otp: body?.otp,
//                     //         countryCode: body?.countryCode
//                     //     });
//                     //     return helper.deliverResponse(
//                     //         res,
//                     //         200,
//                     //         {},
//                     //         {
//                     //             error_code: messages.CUSTOMERS.OTP_EXPIRED.error_code,
//                     //             error_message: messages.CUSTOMERS.OTP_EXPIRED.error_message
//                     //         }
//                     //     );
//                     // }
//                     const customer = await service.findOne({
//                         mobile: body["mobile"],
//                         countryCode: body["countryCode"],
//                         isDelete: false,
//                         isGuest: false,
//                     });
//                     if (customer) {
//                         const payload = {
//                             customerId: customer?.refid,
//                             mobile: customer?.mobile,
//                         };
//                         const token = jwt.sign(payload, KEYS.DEV.JWTSECRET, {
//                             expiresIn: KEYS.DEV.JWT_EXPIRE,
//                         });
//                         let tokens = customer?.tokens ? customer?.tokens : [];
//                         tokens.push(token);
//                         await service.update({ refid: customer?.refid, isDelete: false }, { tokens: tokens });
//                         const response = {
//                             token: token,
//                             customerId: customer?.refid,
//                             isExisting: true,
//                             customer: {
//                                 name: customer?.name,
//                             },
//                         };
//                         await db.Otp.deleteOne({
//                             mobile: body?.mobile,
//                             otp: body?.otp,
//                             countryCode: body?.countryCode,
//                         });
//                         const cart = await cartService.findOne({
//                             devicetoken: devicetoken,
//                             isDelete: false,
//                             isPurchased: false,
//                             isActive: true,
//                         });
//                         const customerCart = await cartService.findOne({
//                             customer: customer?._id,
//                             isDelete: false,
//                             isPurchased: false,
//                             isActive: true,
//                         });
//                         const cartProducts = cart?.products ? cart?.products : [];
//                         const customerCartProducts = customerCart?.products ? customerCart?.products : [];

//                         let productMap = new Map();
//                         [...cartProducts, ...customerCartProducts]?.forEach((obj) => {
//                             productMap.set(obj.product, obj);
//                         });

//                         if (cart && customerCart)
//                             await cartService.update(
//                                 {
//                                     customer: customer?._id,
//                                     isDelete: false,
//                                     isPurchased: false,
//                                     isActive: true,
//                                 },
//                                 { products: Array.from(productMap.values()) }
//                             );
//                         if (cart && !customerCart) await cartService.update({ _id: cart?._id }, { customer: customer?._id });
//                         return helper.deliverResponse(res, 200, response, {
//                             error_code: messages.CUSTOMERS.LOGIN.error_code,
//                             error_message: messages.CUSTOMERS.LOGIN.error_message,
//                         });
//                     } else {
//                         const customer = await service.create({
//                             mobile: body["mobile"],
//                             countryCode: body["countryCode"],
//                             refid: (await service.count({})) + 1,
//                         });
//                         const payload = {
//                             customerId: customer?.refid,
//                             mobile: customer?.mobile,
//                         };
//                         const token = jwt.sign(payload, KEYS.DEV.JWTSECRET, {
//                             expiresIn: KEYS.DEV.JWT_EXPIRE,
//                         });
//                         let tokens = customer?.tokens ? customer?.tokens : [];
//                         tokens.push(token);
//                         await service.update({ refid: customer?.refid, isDelete: false }, { tokens: tokens });
//                         const response = {
//                             token: token,
//                             customerId: customer?.refid,
//                             isExisting: false,
//                         };
//                         await db.Otp.deleteOne({
//                             mobile: body?.mobile,
//                             otp: body?.otp,
//                             countryCode: body?.countryCode,
//                         });
//                         const cart = await cartService.findOne({
//                             devicetoken: devicetoken,
//                             isDelete: false,
//                             isPurchased: false,
//                             isActive: true,
//                         });
//                         if (cart) await cartService.update({ _id: cart?._id }, { customer: customer?._id });

//                         return helper.deliverResponse(res, 200, response, {
//                             error_code: messages.CUSTOMERS.LOGIN.error_code,
//                             error_message: messages.CUSTOMERS.LOGIN.error_message,
//                         });
//                     }
//                     // }
//                     // else {
//                     //     return helper.deliverResponse(
//                     //         res,
//                     //         200,
//                     //         {},
//                     //         {
//                     //             error_code: messages.CUSTOMERS.INVALID_OTP.error_code,
//                     //             error_message: messages.CUSTOMERS.INVALID_OTP.error_message
//                     //         }
//                     //     );
//                     // }
//                     // }
//                     // else {
//                     //     return helper.deliverResponse(
//                     //         res,
//                     //         200,
//                     //         {},
//                     //         {
//                     //             error_code: messages.CUSTOMERS.OTP_EXPIRED.error_code,
//                     //             error_message: messages.CUSTOMERS.OTP_EXPIRED.error_message
//                     //         }
//                     //     );
//                     // }
//                     // }
//                     //  else {
//                     //     return helper.deliverResponse(
//                     //         res,
//                     //         200,
//                     //         {},
//                     //         {
//                     //             error_code: messages.CUSTOMERS.OTP_REQUIRED.error_code,
//                     //             error_message: messages.CUSTOMERS.OTP_REQUIRED.error_message
//                     //         }
//                     //     );
//                     // }
//                 } else {
//                     return helper.deliverResponse(
//                         res,
//                         422,
//                         {},
//                         {
//                             error_code: 1,
//                             error_message: "OTP Verification Failed",
//                         }
//                     );
//                 }
//             });
//     } catch (error) {
//         return helper.deliverResponse(
//             res,
//             422,
//             {},
//             {
//                 error_code: messages.SERVER_ERROR.error_code,
//                 error_message: messages.SERVER_ERROR.error_message,
//             }
//         );
//     }
// };

exports.wishlist = async (req, res) => {
    try {
        let { customerId } = res.locals.user;
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const customerDetails = await service.findOne({ refid: customerId, isDelete: false });
        let wishlist = [];
        let products = [];
        let flag = false
        for (let [key, product] of customerDetails?.wishlist?.entries()) {
            const productDetail = await productService.findOne({ _id: product, isDelete: false });
            if (!productDetail) {
                flag = true
                continue;
            }

            if (productDetail?.storeId != storeid) continue;

            products.push(product);
            let price = productDetail?.price?.aed;

            let offerPrice = productDetail?.offerPrice?.aed

            let offerPercentage = productDetail?.customizable
                ? Math.round(productDetail?.sizes[0]?.offerPercentage)
                : productDetail?.offerPercentage?.aed
                    ? Math.round(productDetail?.offerPercentage?.aed)
                    : productDetail?.offerPercentage
                        ? Math.round(productDetail?.offerPercentage)
                        : null;

            if (!offerPercentage) {
                if (productDetail?.price?.aed && productDetail?.offerPrice?.aed) {
                    offerPercentage = Math.round(((productDetail?.price?.aed - productDetail?.offerPrice?.aed) / productDetail?.price?.aed) * 100);
                }
            }

            wishlist.push({
                [key]: {
                    refid: productDetail?.refid,
                    _id: productDetail?._id,
                    productType: productDetail?.productType,
                    customizable: productDetail?.customizable,
                    name: productDetail?.name[language] ? productDetail?.name[language] : productDetail?.name["en"],
                    brand: productDetail?.brand?.name[language] ? productDetail?.brand?.name[language] : productDetail?.brand?.name["en"],
                    slug: productDetail?.slug,
                    price: price,
                    offerPrice: offerPrice && offerPrice == price ? null : offerPrice || null,
                    offerPercentage: offerPercentage && offerPercentage != 0 ? offerPercentage : null,
                    isWishlisted: true,
                    thumbnail: process.env.DOMAIN + productDetail?.thumbnail,
                    createdAt: productDetail?.createdAt,
                    isAddToCart: productDetail?.isAddToCart,
                    sizes: productDetail?.sizes,
                    color: {
                        ...productDetail?.color?._doc,
                        name: productDetail?.color?.name[language]
                    },
                }
            });
        }

        if (flag) {
            await service.update({ refid: customerId }, { wishlist: products });
        }
        console.log(wishlist)
        wishlist.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        helper.deliverResponse(res, 200, wishlist, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.register = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        const { body } = req;
        body.slug = await slug.createSlug(db.Customers, body?.name, {
            slug: await slug.generateSlug(body?.name),
        });
        body.isGuest = false;
        const customer = await service.update({ refid: customerId, isDelete: false }, body);
        if (customer instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            });
        }

        const loyalty = await axios.post("https://app.optculture.com/subscriber/updateContactsOPT.mqrm", {
            header: {
                requestId: customer?._id || Math.random(),
                requestDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                contactSource: "eComm",
                contactList: "POS",
            },
            customer: {
                homeStore: "1",
                customerId: customer?._id,
                firstName: customer?.name,
                emailAddress: customer?.email,
                phone: customer?.mobile,
                creationDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                loyalty: {
                    enrollCustomer: "Y",
                },
            },
            user: {
                // userName: "webcastle",
                // organizationId: "webcastle",
                // token: "0J3RIVME081FARD3",
                userName: CRM_USERNAME, // Mandatory field
                token: CRM_TOKEN, // Mandatory field
                organizationId: CRM_ORG_ID, // Mandatory field
            },
        });

        console.log(loyalty.data)

        if (loyalty.data.status?.errorCode != "0") {
            // return helper.deliverResponse(res, 422, loyalty.data.status, {
            //     error_code: 1,
            //     error_message: "Failed to enroll loyalty Program",
            // });
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.CUSTOMERS.REGISTERED.error_code,
                    error_message: messages.CUSTOMERS.REGISTERED.error_message,
                }
            );
        }
        console.log("loyalty enrolled")
        await service.update({ refid: customerId, isDelete: false }, { loyaltyNumber: loyalty?.data?.membership?.cardNumber });
        return helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.CUSTOMERS.REGISTERED.error_code,
                error_message: messages.CUSTOMERS.REGISTERED.error_message,
            }
        );
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.profile = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        const customer = await service.findOne({ refid: customerId, isDelete: false }, { tokens: 0, wishlist: 0 });
        const tryCartOrder = await orderService.findOne({
            customer: customer?._id,
            isDelete: false,
            isActive: true,
            isTryCart: true,
        });

        const response = {
            ...customer._doc,
            file: customer?.file ? process.env.DOMAIN + customer?.file : null,
            image: customer?.image ? process.env.DOMAIN + customer?.image : null,
            isTryCart: tryCartOrder ? (tryCartOrder.orderStatus != "RETURNED" ? true : false) : false,
        };
        if (customer instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            });
        }
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.updateProfile = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        let { language } = req.headers;
        language = language ?? language;
        let { body } = req;
        const customerDetails = await service.findOne({ refid: customerId, isDelete: false });
        if (body?.name != customerDetails?.name)
            body.slug = await slug.createSlug(db.Customers, body?.name, {
                slug: await slug.generateSlug(body?.name),
            });

        // if (req?.files?.image) body.image = req?.files?.image[0]?.path;
        // if (req?.files?.file) body.file = req?.files?.file[0]?.path;
        const customer = await service.update({ refid: customerDetails?.refid, isDelete: false }, body);
        if (customer instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: language == "en" ? messages.SERVER_ERROR.error_message : "لقد حدث خطأ ما",
            });
        }
        return helper.deliverResponse(res, 200, customer, {
            error_code: messages.CUSTOMERS.UPDATED.error_code,
            error_message: language == "en" ? messages.CUSTOMERS.UPDATED.error_message : "تم تحديث العميل بنجاح",
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: language == "en" ? messages.SERVER_ERROR.error_message : "لقد حدث خطأ ما",
        });
    }
};

exports.addTryCart = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
        }

        const { customerId } = res.locals.user;
        const { body } = req;

        const customer = await service.findOne({
            refid: customerId,
            isDelete: false,
            isActive: true,
        });
        if (!customer) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
        }

        if (customer.isGuest) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Please Sign up to Yateem to use this feature",
                }
            );
        }

        const product = await productService.findOne({
            _id: body?.product,
            isDelete: false,
            isActive: true,
        });

        if (!product || !product?.isTryCart) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }

        const tryCartOrder = await orderService.findOne({
            customer: customer?._id,
            isDelete: false,
            isActive: true,
            isTryCart: true,
        });

        if (tryCartOrder) {
            if (tryCartOrder?.orderStatus != "RETURNED") {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "You have already placed an order",
                    }
                );
            }
        }

        let tryCart = customer?.tryCart ? customer?.tryCart : [];
        if (tryCart.length == 4) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Try cart limit is 4",
                }
            );
            return;
        }

        if (body?.size) {
            const productExist = tryCart.find((item) => String(item?.product) == String(product?._id) && String(item?.size) == String(body?.size));
            if (productExist) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Already added in try cart",
                    }
                );
            }
        } else {
            const productExist = tryCart.find((item) => String(item?.product) == String(product?._id));
            if (productExist) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Already added in try cart",
                    }
                );
            }
        }
        // if (tryCart.includes(product?._id)) {
        //     return helper.deliverResponse(
        //         res,
        //         422,
        //         {},
        //         {
        //             error_code: 1,
        //             error_message: "Already added in try cart"
        //         }
        //     );
        // }

        tryCart.push({ product: product?._id, size: body?.size ? body?.size : null });

        await service.update({ refid: customerId, isDelete: false }, { tryCart: tryCart });
        helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.CUSTOMERS.ADDED_TO_TRYCART.error_code,
                error_message: messages.CUSTOMERS.ADDED_TO_TRYCART.error_message,
            }
        );
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.getTryCart = async (req, res) => {
    try {
        let { language } = req.headers;
        language = language ? language.split("-")[1] : "en";
        const { customerId } = res.locals.user;
        const customer = await service.findOne({ refid: customerId, isDelete: false });
        const settings = await tryCartService.findOne({ isDelete: false });

        if (!customer) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
        }

        let tryCart = [];
        let summary = [];

        for (let product of customer?.tryCart) {
            const productDetail = await productService.findOne({
                _id: product?.product,
                isDelete: false,
            });

            let productSize = null;
            if (product?.size) {
                productSize = await sizeService.findOne({ _id: product?.size, isDelete: false });
            }

            let productPrice = product?.size
                ? productDetail?.sizes.find((size) => String(size?.size?._id) === String(product?.size))?.offerPrice ||
                productDetail?.sizes.find((size) => String(size?.size?._id) === String(product?.size))?.price
                : productDetail?.offerPrice?.aed || productDetail?.price?.aed;

            tryCart.push({
                _id: productDetail?._id,
                name: productDetail?.name[language],
                slug: productDetail?.slug,
                price: productPrice,
                thumbnail: process.env.DOMAIN + productDetail?.thumbnail,
                color: productDetail?.color?.name,
                size: {
                    name: productSize?.name,
                    _id: productSize?._id,
                },
            });
        }
        summary = [
            {
                text: "Subtotal",
                value: settings?.amount,
                currency: "AED",
            },
            {
                text: "Shipping",
                value: settings?.shipping || 0,
                currency: "AED",
            },
            {
                text: "Total",
                value: settings?.amount + (settings?.shipping || 0),
                currency: "AED",
            },
        ];
        return helper.deliverResponse(
            res,
            200,
            { products: tryCart, summary: summary, count: tryCart.length },
            {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            }
        );
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.removeTryCart = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        const { body } = req;

        const customer = await service.findOne({ refid: customerId, isDelete: false });
        const product = await productService.findOne({
            _id: body?.product,
            isDelete: false,
            isActive: true,
        });

        if (!customer) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
        }
        if (!product) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }

        let tryCart = customer?.tryCart ? customer?.tryCart : [];
        tryCart = tryCart.filter((item) => {
            if (body?.size) return String(item?.product) !== String(product?._id) || String(item?.size) !== String(body?.size);

            return String(item?.product) !== String(product?._id);
        });
        await service.update({ refid: customerId, isDelete: false }, { tryCart: tryCart });
        return helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.CUSTOMERS.REMOVED_FROM_TRYCART.error_code,
                error_message: messages.CUSTOMERS.REMOVED_FROM_TRYCART.error_message,
            }
        );
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.getCounts = async (req, res) => {
    try {
        const { authorization, devicetoken } = req.headers;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) customerId = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await service.findOne({
                refid: customerId,
                isDelete: false,
                isActive: true,
            });

            cartDetails = await cartService.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await cartService.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        }
        console.log(customerDetails)
        // const customer = await service.findOne({ refid: customerId, isDelete: false });
        // if (!customer) {
        //     return helper.deliverResponse(res, 422, {}, {
        //         error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
        //         error_message: messages.CUSTOMERS.NOT_FOUND.error_message
        //     });
        // }
        // const cartDetails = await cartService.findOne({ customer: customer?._id, isActive: true, isDelete: false, isPurchased: false });

        let counts = {};
        counts.cart = cartDetails?.products?.length ? cartDetails?.products?.length : 0;
        // counts.tryCart = customerDetails?.tryCart?.length ? customerDetails?.tryCart?.length : 0;
        counts.tryCart = 0;
        counts.wishlist = customerDetails?.wishlist?.filter((item) => item?.storeId == storeid).length ?
            customerDetails?.wishlist?.filter((item) => item?.storeId == storeid).length
            : 0;

        return helper.deliverResponse(res, 200, counts, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

// exports.revenueReport = async (req, res) => {
//     try {
//         const { body } = req;
//         const customers = await service.find({ isDelete: false, isActive: true });
//         const response = [];
//         for (const customer of customers) {
//             console.log("customer :: ", customer?.name);
//             let totalSpend = 0;
//             let trycartSpend = 0;
//             const order = await orderService.find({
//                 customer: customer?._id,
//                 isDelete: false,
//                 isActive: true,
//                 isTryCart: false,
//                 orderStatus: "DELIVERED"
//             });
//             if (order?.length > 0) {
//                 for (const item of order) {
//                     totalSpend += item?.total;
//                 }
//             }
//             const tryOrder = await orderService.find({
//                 customer: customer?._id,
//                 isDelete: false,
//                 isActive: true,
//                 isTryCart: true,
//                 orderStatus: {$in: ["DELIVERED", "RETURNED"]}
//             });
//             if (tryOrder?.length > 0) {
//                 for (const item of tryOrder) {
//                     trycartSpend += item?.baseTotal;
//                 }
//             }
//             console.log("trycartSpend :: ", trycartSpend);
//             console.log("totalSpend :: ", totalSpend);
//             response.push({
//                 name: customer?.name,
//                 mobile: customer?.mobile,
//                 totalSpend: totalSpend + trycartSpend
//             });
//         }
//          helper.deliverResponse(res, 200, response, {
//             error_code: messages.SUCCESS_RESPONSE.error_code,
//             error_message: messages.SUCCESS_RESPONSE.error_message
//         });
//     } catch (error) {
//         console.log("error :: ", error);
//         helper.deliverResponse(res, 422, error, {
//             error_code: messages.SERVER_ERROR.error_code,
//             error_message: messages.SERVER_ERROR.error_message
//         });
//     }
// };

exports.revenueReport = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { body } = req;
        const { fromDate, toDate } = body;

        const matchStage = {
            $match: {
                isDelete: false,
                isActive: true,
                storeId: storeid,
                $or: [
                    {
                        isTryCart: false,
                        orderStatus: "DELIVERED",
                    },
                    {
                        isTryCart: true,
                        orderStatus: { $in: ["DELIVERED", "RETURNED"] },
                    },
                ],
            },
        };

        // If only fromDate is provided
        if (fromDate && !toDate) {
            matchStage.$match.createdAt = { $gte: new Date(fromDate) };
        }

        // If both fromDate and toDate are provided
        if (fromDate && toDate) {
            matchStage.$match.createdAt = {
                $gte: new Date(fromDate),
                $lte: new Date(toDate),
            };
        }

        // If only toDate is provided
        if (!fromDate && toDate) {
            matchStage.$match.createdAt = { $lte: new Date(toDate) };
        }

        const pipeline = [
            matchStage,
            {
                $group: {
                    _id: "$customer",
                    totalSpend: {
                        $sum: {
                            $cond: [{ $eq: ["$isTryCart", false] }, "$total", "$baseTotal"],
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: "customers",
                    localField: "_id",
                    foreignField: "_id",
                    as: "customer",
                },
            },
            {
                $unwind: {
                    path: "$customer",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    _id: 0,
                    name: "$customer.name",
                    mobile: "$customer.mobile",
                    totalSpend: { $ifNull: ["$totalSpend", 0] },
                },
            },
            {
                $match: {
                    totalSpend: { $gt: 0 },
                },
            },
            {
                $sort: {
                    totalSpend: -1, // Sort by totalSpend in descending order
                },
            },
        ];

        const response = await orderService.aggregate(pipeline);

        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.guestReport = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { body } = req;
        const { fromDate, toDate } = req.body;

        const matchGuestCustomers = {
            isGuest: true,
        };

        const matchOrders = { "orders.storeId": storeid };

        if (fromDate) {
            matchOrders.createdAt = { $gte: new Date(fromDate) };
        }
        if (toDate) {
            matchOrders.createdAt = matchOrders.createdAt || {};
            matchOrders.createdAt.$lte = new Date(toDate);
        }

        const pipeline = [
            {
                $match: matchGuestCustomers,
            },
            {
                $lookup: {
                    from: "orders",
                    localField: "_id",
                    foreignField: "customer",
                    as: "orders",
                },
            },
            {
                $unwind: "$orders",
            },
            {
                $match: matchOrders,
            },
            {
                $sort: {
                    "orders.createdAt": -1, // Sort by order creation date in descending order
                },
            },
            {
                $project: {
                    orderNo: "$orders.orderNo",
                    mobile: "$mobile",
                    totalQuantity: { $sum: "$orders.products.quantity" },
                    totalAmount: "$orders.total",
                },
            },
        ];

        const result = await db.Customers.aggregate(pipeline);
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

// exports.guestReport = async (req, res) => {
//     try {
//         const guest = await service.find({ isDelete: false, isActive: true, isGuest: true });
//         if (guest.length === 0) {
//             return helper.deliverResponse(res, 422, {}, {
//                 error_code: messages.SERVER_ERROR.error_code,
//                 error_message: messages.SERVER_ERROR.error_message
//             });
//         }
//         let guestReport = [];
//         for (let item of guest) {
//             const order = await orderService.find({ isDelete: false, isActive: true, customer: item?._id, orderStatus: "DELIVERED" });
//             if (order.length > 0) {
//                 for (let orderItem of order) {
//                     const orderProducts = await orderProductService.find({ isDelete: false, isActive: true, order: orderItem?._id });
//                     let totalQuantity = 0;
//                     for (let product of orderProducts) {
//                         totalQuantity += product.quantity;
//                     }
//                     guestReport.push({
//                         orderNo: orderItem?.orderNo,
//                         mobile: item?.mobile,
//                         productQuantity: totalQuantity,
//                     });
//                 }
//             }
//         }

//         helper.deliverResponse(res, 200, guestReport, {
//             error_code: messages.SUCCESS_RESPONSE.error_code,
//             error_message: messages.SUCCESS_RESPONSE.error_message
//         });
//     } catch (error) {
//         console.log("error :: ", error);
//         helper.deliverResponse(res, 422, error, {
//             error_code: messages.SERVER_ERROR.error_code,
//             error_message: messages.SERVER_ERROR.error_message
//         });
//     }
// };

exports.customerExport = async (req, res) => {
    try {
        const customers = await service.find({ isDelete: false, isActive: true, isGuest: false });
        const data = [];
        for (let customer of customers) {
            data.push({
                name: customer?.name,
                countryCode: customer?.countryCode,
                mobile: customer?.mobile,
                email: customer?.email,
                emirates: customer?.emirates,
                isInsurance: customer?.isInsurance,
                insuranceId: customer?.insuranceId,
                isActive: customer?.isActive,
            });
        }

        const csvWriter = createCsvWriter({
            path: "customers.csv",
            header: [
                { id: "name", title: "Name" },
                { id: "countryCode", title: "Country Code" },
                { id: "mobile", title: "Mobile" },
                { id: "email", title: "Email" },
                { id: "emirates", title: "Emirates" },
                { id: "isInsurance", title: "Have Insurance" },
                { id: "insuranceId", title: "Insurance Id" },
                { id: "isActive", title: "Status" },
            ],
        });
        csvWriter
            .writeRecords(data)
            .then(() => {
                res.setHeader("Content-Disposition", "attachment; filename=customers.csv");
                res.setHeader("Content-Type", "text/csv");
                res.status(200).download(process.cwd() + "/customers.csv", () => {
                    fs.unlinkSync(process.cwd() + "/customers.csv");
                });
            })
            .catch((err) => {
                res.status(500).send("Internal Server Error");
            });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.customerImport = async (req, res) => {
    try {
        const file = process.cwd() + "/" + req.file.path;
        const customers = [];
        fs.createReadStream(file).pipe(csv(
            {
                mapHeaders: (({ header }) => {
                    if (header.charCodeAt(0) === 0xFEFF) {
                        header = header.substr(1);
                    }
                    return header;
                })
            }
        )).on("data", (data) => customers.push(data))
            .on("end", async () => {
                let lastEmail = "";

                try {
                    for await (const [key, row] of customers?.entries()) {
                        let payload = {
                            refid: (await service.count({})) + 1,
                            name: row.Name,
                            countryCode: row["Country Code"],
                            mobile: row.Mobile,
                            email: row.Email,
                            emirates: row.Emirates,
                            isInsurance: row['Have Insurance']?.trim()?.toLowerCase() == "true" ? true : false,
                            insuranceId: row["Insurance Id"] ? row["Insurance Id"] : null,
                            isActive: row.Status?.toLowerCase() == "true" ? true : false,
                            isGuest: false
                        };
                        console.log(row)
                        const address = {
                            name: row.Name,
                            emirates: row.Emirates,
                            country: row.Country,
                            street: row.Street,
                            suiteUnit: row['Suite Unit'],
                            city: row.City,
                            postalCode: row["Postal Code"],
                            countryCode: row["Country Code"],
                            mobile: row.Mobile,
                            type: row.Type?.toLowerCase() == "home" ? "Home" : row.Type?.toLowerCase() == "work" ? "Work" : "Other",
                        };

                        const existingCustomer = await service.findOne(
                            { email: row.Email },
                        );

                        if (existingCustomer) {
                            // Update the existing customer
                            payload = { ...existingCustomer?._doc, ...payload, refid: existingCustomer?.refid };
                            address.customer = existingCustomer?._id;
                            address.refid = (await addressService.count({})) + 1;
                            await addressService.create(address)

                            if (lastEmail != existingCustomer?.email) {
                                const updatedCustomer = await service.update({ _id: existingCustomer._id }, payload);

                                if (updatedCustomer instanceof Error) {
                                    return helper.deliverResponse(res, 422, error, {
                                        error_code: messages.SERVER_ERROR.error_code,
                                        error_message: messages.SERVER_ERROR.error_message,
                                    });
                                }
                            }

                        } else {
                            // Create a new customer
                            console.log("crreate")
                            const customer = await service.create(payload);
                            address.customer = customer?._id;
                            address.refid = (await addressService.count({})) + 1;
                            await addressService.create(address)
                            if (customer instanceof Error) {
                                return helper.deliverResponse(res, 422, error, {
                                    error_code: messages.SERVER_ERROR.error_code,
                                    error_message: messages.SERVER_ERROR.error_message,
                                });
                            }
                        }
                        lastEmail = row.Email
                    }
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.IMPORTED.error_code,
                            error_message: messages.IMPORTED.error_message,
                        }
                    );
                } catch (error) {
                    console.log(error)
                    helper.deliverResponse(res, 422, error, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                }

            })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.lensEnquiries = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        // const lensEnquiries = await lensEnquiryService.find({ isDelete: false, isActive: true });

        const lensEnquiries = await lensEnquiryService.aggregate([
            { $match: { isDelete: false, isActive: true, storeId: storeid } },
            {
                $lookup: {
                    from: "orders",
                    localField: "_id",
                    foreignField: "products.lensDetails.lensEnquiry",
                    as: "enquiry",
                },
            },
            {
                $addFields: { orderNo: { $arrayElemAt: ["$enquiry.orderNo", 0] } },
            },
            {
                $project: {
                    "enquiry": 0,
                },
            },
            {
                $sort: { createdAt: -1 },
            }
        ]);

        await db.LensEnquiries.populate(lensEnquiries, [
            { path: "brand", select: "name" },
            { path: "customer", select: "name mobile" },
            { path: 'prescription.leftSph', select: 'name' },
            { path: 'prescription.leftCyl', select: 'name' },
            { path: 'prescription.leftAxis', select: 'name' },
            { path: 'prescription.rightSph', select: 'name' },
            { path: 'prescription.rightCyl', select: 'name' },
            { path: 'prescription.rightAxis', select: 'name' },
            { path: 'product', select: 'name' },
        ])
        // await db.LensEnquiries.populate(lensEnquiries, { path: "customer", select: "name mobile" });
        // await db.LensEnquiries.populate(lensEnquiries, { path: "product", select: "name" });

        helper.deliverResponse(res, 200, lensEnquiries, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.lensEnquiryDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        // const lensEnquiries = await lensEnquiryService.find({ isDelete: false, isActive: true });
        console.log(req.params.id)
        const lensEnquiries = await lensEnquiryService.aggregate([
            { $match: { isDelete: false, isActive: true, refid: req?.params?.id, storeId: storeid } },
            {
                $lookup: {
                    from: "orders",
                    localField: "_id",
                    foreignField: "products.lensDetails.lensEnquiry",
                    as: "enquiry",
                },
            },
            {
                $addFields: { orderNo: { $arrayElemAt: ["$enquiry.orderNo", 0] } },
            },
            {
                $project: {
                    "enquiry": 0,
                },
            },
            {
                $sort: { createdAt: -1 },
            }
        ]);

        if (!lensEnquiries.length) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            });
        }

        await db.LensEnquiries.populate(lensEnquiries, [
            { path: "brand", select: "name" },
            { path: "customer", select: "name mobile" },
            { path: 'prescription.leftSph', select: 'name' },
            { path: 'prescription.leftCyl', select: 'name' },
            { path: 'prescription.leftAxis', select: 'name' },
            { path: 'prescription.leftAdd', select: 'name' },
            { path: 'prescription.rightSph', select: 'name' },
            { path: 'prescription.rightCyl', select: 'name' },
            { path: 'prescription.rightAxis', select: 'name' },
            { path: 'prescription.rightAdd', select: 'name' },
            { path: 'product', select: 'name' },
        ])
        // await db.LensEnquiries.populate(lensEnquiries, { path: "customer", select: "name mobile" });
        // await db.LensEnquiries.populate(lensEnquiries, { path: "product", select: "name" });

        helper.deliverResponse(res, 200, lensEnquiries, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.myCashbacks = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        let { language } = req.headers;
        language = language ?? "en";

        const [customer, translation] = await Promise.all([
            service.findOne({ refid: customerId, isDelete: false }, { email: 1, loyaltyNumber: 1, name: 1, mobile: 1 }),
            db.Texts.findOne({})
        ])
        console.log(customer)

        const payload = {
            "header": {
                "requestId": "",
                "requestDate": "",
                "sourceType": "ALL"
            },
            "lookup": {
                "emailAddress": customer?.email,
                "phone": customer?.mobile,
                "membershipNumber": ""
            },
            "report": {
                "source": "All",
                "transactionType": "All",
                "startDate": "2014-01-01 00:00:00",
                "endDate": moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                    "YYYY-MM-DD HH:mm:ss"
                ),
                "offset": "0",
                "maxRecords": "100",
                "store": ""
            },
            "user": {
                // "userName": "webcastle", // Mandatory field
                // "token": "0J3RIVME081FARD3", // Mandatory field
                // "organizationId": "webcastle", // Mandatory field
                "sessionID": "",
                "userName": CRM_USERNAME, // Mandatory field
                "token": CRM_TOKEN, // Mandatory field
                "organizationId": CRM_ORG_ID // Mandatory field
            }
        }
        const URL = "https://app.optculture.com/subscriber/loyaltyTransactionHistory.mqrm"
        const response = await axios.post(URL, payload);

        if (!response?.data?.matchedCustomers || response?.data?.matchedCustomers?.length < 1) {
            return helper.deliverResponse(res, 422, error, {
                error_code: 90,
                error_message: "customer not found",
            });
        }

        const matchedCustomer = response?.data?.matchedCustomers[0];
        if (!matchedCustomer?.transactions || matchedCustomer?.transactions?.length < 1) {
            return helper.deliverResponse(res, 200, {
                history: [],
                cashback: {
                    totalEarned: 0,
                    totalSpent: 0,
                    balance: 0
                }
            }, {
                error_code: 0,
                error_message: "No transactions fond",
            });
        }

        let history = [];
        for (let transaction of matchedCustomer?.transactions) {
            if (transaction?.amount?.type != "Loyalty Issuance") {
                let value = transaction?.amount?.enteredValue;
                if (transaction?.amount?.type == "Reward") {
                    const reward = transaction?.balances?.find((item) => ((item?.type == "Reward" || item?.type == "Adjustment-Add") && item?.valueCode == "Points"));
                    if (reward) {
                        value = reward?.difference
                    }
                }
                history.push({
                    ...transaction?.amount,
                    date: transaction?.date?.split(" ")[0],
                    orderNo: transaction?.receiptNumber,
                    title:
                        transaction?.amount?.type == "Reward" ?
                            (translation?.myAccount?.cashbackEarned[language] ?? "Cashback Earned") :
                            transaction?.amount?.type == "Redemption" ?
                                (translation?.myAccount?.cashbackSpent[language] ?? "Cashback Spent") : transaction?.amount?.type,
                    amount: value,
                    sign:
                        transaction?.amount?.type == "Reward" || transaction?.amount?.type == "Adjustment-Add" ?
                            "+" :
                            transaction?.amount?.type == "Redemption" || transaction?.amount?.type == "Adjustment-Sub" ?
                                "-" : "",

                });
            };
        }

        const totalCashBackSpent = history?.filter((item) => (item?.type == "Redemption" || item?.type == "Adjustment-Sub"))?.reduce((acc, curr) => acc + Number(curr?.enteredValue), 0);
        const balance = Number(matchedCustomer?.transactions[0]?.balances?.find((item) => item?.type == "Reward" && item?.valueCode == "Points")?.amount);
        let totalCashBackEarned = balance + totalCashBackSpent

        helper.deliverResponse(res, 200, {
            history,
            cashback: {
                totalEarned: totalCashBackEarned,
                totalSpent: totalCashBackSpent,
                balance
            }
        },
            {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.getToken = async (req, res) => {
    const payload = {
        customerId: "1256",
        mobile: "565084668",
    };
    const token = jwt.sign(payload, KEYS.DEV.JWTSECRET, {
        expiresIn: KEYS.DEV.JWT_EXPIRE,
    });
    console.log(token)
}