const mongoose = require('mongoose');

const frameShapeSchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    position : { type: Number, default: 0},
    image: { type: String},
    refid: { type: String, required: true },
    slug: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('frame.shapes', frameShapeSchema);