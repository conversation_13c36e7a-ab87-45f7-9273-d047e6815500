const mongoose = require("mongoose");

const lensMaterialSchema = new mongoose.Schema(
  {
    name: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    position: { type: Number, default: 0 },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
  },
  { timestamps: true }
);

module.exports = mongoose.model("lens.materials", lensMaterialSchema);
