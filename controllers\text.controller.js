const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/text.service");
const axios = require('axios');


exports.createTexts = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        }
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.VM_POLICY.CREATED.error_code,
            "error_message": messages.VM_POLICY.CREATED.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getTexts = async (req, res) => {
    try {
        let { storeid } = req.headers
        storeid = storeid ? storeid : "ae";
        const response = await service.find({  storeId: storeid, isDelete: false });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getTextsContent = async (req, res) => {
    try {
        let { language, storeid } = req.headers
        language = language ? language: "en";
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ storeId: storeid, isDelete: false });
        let data
        if (language) {
            data = {
                footer: {
                    top: response?.footer?.top?.map(item => ({
                        title: item?.title[language],
                        description: item?.description[language]
                    })),
                    newsLetter: {
                        text: response?.footer?.newsLetter?.text[language],
                        placeholder: response?.footer?.newsLetter?.placeholder[language]
                    },
                    doYouNeedHelp: response?.footer?.doYouNeedHelp[language],
                    poweredBy: response?.footer?.poweredBy[language],
                    followUs: response?.footer.poweredBy[language]
                },
                formFields: {
                    fullName: response?.formFields?.fullName[language],
                    fullNameRequiredError: response?.formFields?.fullNameRequiredError[language],
                    numericError: response?.formFields?.numericError[language],
                    emailAddress: response?.formFields?.emailAddress[language],
                    emailAddressRequiredError: response?.formFields?.emailAddressRequiredError[language],
                    emailAddressInvalidError: response?.formFields?.emailAddressInvalidError[language],
                    emailAddressYahooError: response?.formFields?.emailAddressYahooError[language],
                    phoneNumber: response?.formFields?.phoneNumber[language],
                    phoneNumberRequiredError: response?.formFields?.phoneNumberRequiredError[language],
                    phoneNumberInvalidError: response?.formFields?.phoneNumberInvalidError[language],
                    storeEnquiry: response?.formFields?.storeEnquiry[language],
                    storeEnquiryRequiredError: response?.formFields?.storeEnquiryRequiredError[language],
                    selectStore: response?.formFields?.selectStore[language],
                    submit: response?.formFields?.submit[language],
                    cancel: response?.formFields?.cancel[language],
                    message: response?.formFields?.message[language],
                    messageRequiredError: response?.formFields?.messageRequiredError[language],
                    nameOfInsurance: response?.formFields?.nameOfInsurance[language],
                    selectInsurance: response?.formFields?.selectInsurance[language],
                    nameOfInsuranceRequiredError: response?.formFields?.nameOfInsuranceRequiredError[language],
                    nationality: response?.formFields?.nationality[language],
                    nationalityRequiredError: response?.formFields?.nationalityRequiredError[language],
                    country: response?.formFields?.country[language],
                    countryRequiredError: response?.formFields?.countryRequiredError[language],
                    emirates: response?.formFields?.emirates[language],
                    selectEmirates: response?.formFields?.selectEmirates[language],
                    emiratesRequiredError: response?.formFields?.emiratesRequiredError[language],
                    emiratesId: response?.formFields?.emiratesId[language],
                    emiratesIdRequiredError: response?.formFields?.emiratesIdRequiredError[language],
                    emiratesIdInvalidError: response?.formFields?.emiratesIdInvalidError[language],
                    uploadEmirates: response?.formFields?.uploadEmirates[language],
                    memberId: response?.formFields?.memberId[language],
                    insuranceId: response?.formFields?.insuranceId[language],
                    streat: response?.formFields?.streat[language],
                    streatRequiredError: response?.formFields?.streatRequiredError[language],
                    city: response?.formFields?.city[language],
                    cityRequiredError: response?.formFields?.cityRequiredError[language],
                    apartment: response?.formFields?.apartment[language],
                    postalCode: response?.formFields?.postalCode[language],
                    address: response?.formFields?.address[language],
                    addressRequired: response?.formFields?.addressRequired[language],
                    addressType: response?.formFields?.addressType[language],
                    addressTypeRequiredError: response?.formFields?.addressTypeRequiredError[language],
                },
                productListing: {
                    sortBy: response?.productListing?.sortBy[language],
                    filterBy: response?.productListing?.filterBy[language],
                    apply: response?.productListing?.apply?.[language],
                    reset: response?.productListing?.reset?.[language],
                    clearAll: response?.productListing?.clearAll[language],
                    showing: response?.productListing?.showing[language],
                    products: response?.productListing?.products[language],
                    category: response?.productListing?.category[language],
                    frameType: response?.productListing?.frameType[language],
                    frameShape: response?.productListing?.frameShape[language],
                    brands: response?.productListing?.brands[language],
                    frameSize: response?.productListing?.frameSize[language],
                    frontMaterial: response?.productListing?.frontMaterial[language],
                    color: response?.productListing?.color[language],
                    price: response?.productListing?.price[language],
                    types: response?.productListing?.types[language],
                    lensType: response?.productListing?.lensType[language],
                    virtualTry: response?.productListing?.virtualTry[language],
                    viewMore: response?.productListing?.viewMore[language],
                    newArrivals: response?.productListing?.newArrivals[language],
                    sale: response?.productListing?.sale[language],
                    exclusive: response?.productListing?.exclusive[language],
                    priceLowToHigh: response?.productListing?.priceLowToHigh[language],
                    priceHighToLow: response?.productListing?.priceHighToLow[language],
                    noProductsFound: response?.productListing?.noProductsFound?.[language],
                },
                myAccount: {
                    myProfile: response?.myAccount?.myProfile[language],
                    myCart: response?.myAccount?.myCart[language],
                    tryCart: response?.myAccount?.tryCart[language],
                    myOrders: response?.myAccount?.myOrders[language],
                    myWishlist: response?.myAccount?.myWishlist[language],
                    myAddressBook: response?.myAccount?.myAddressBook[language],
                    mySubscription: response?.myAccount?.mySubscription[language],
                    myPrescription: response?.myAccount?.myPrescription[language],
                    login: response?.myAccount?.login[language],
                    logout: response?.myAccount?.logout[language],
                    search: response?.myAccount?.search[language],
                    storeLocator: response?.myAccount?.storeLocator[language],
                    personalInformation: response?.myAccount?.personalInformation[language],
                    addressHome: response?.myAccount?.addressHome[language],
                    addressWork: response?.myAccount?.addressWork[language],
                    addressOther: response?.myAccount?.addressOther[language],
                    addressDefault: response?.myAccount?.addressDefault[language],
                    addressEdit: response?.myAccount?.addressEdit[language],
                    addressRemove: response?.myAccount?.addressRemove[language],
                    addressAdd: response?.myAccount?.addressAdd[language],
                    addressSaveCheck: response?.myAccount?.addressSaveCheck[language],
                    setDefaultAddress: response?.myAccount?.setDefaultAddress[language],
                    uploadPrescription: response?.myAccount?.uploadPrescription[language],
                    emptyPrescription: response?.myAccount?.emptyPrescription[language],
                    myCashbacks: response?.myAccount?.myCashbacks[language],
                    totalCashbackEarned: response?.myAccount?.totalCashbackEarned[language],
                    totalCashbackSpent: response?.myAccount?.totalCashbackSpent[language],
                    cashbackBalance: response?.myAccount?.cashbackBalance[language],
                    cashbackSpent: response?.myAccount?.cashbackSpent[language],
                    cashbackEarned: response?.myAccount?.cashbackEarned[language],
                    noCashbackHistory: response?.myAccount?.noCashbackHistory?.[language],
                    notes: response?.myAccount?.notes[language],
                    prescriptionUploadTitle: response?.myAccount?.prescriptionUploadTitle?.[language],
                    prescriptionUploadInputLabel: response?.myAccount?.prescriptionUploadInputLabel?.[language],
                    prescriptionUploadInputPlaceholder: response?.myAccount?.prescriptionUploadInputPlaceholder?.[language],
                    prescriptionUploadInputFile: response?.myAccount?.prescriptionUploadInputFile?.[language],
                    prescriptionUploadFileInfo: response?.myAccount?.prescriptionUploadFileInfo?.[language],
                    prescriptionUploadInfo: response?.myAccount?.prescriptionUploadInfo?.[language],
                    prescriptionUploadSubmit: response?.myAccount?.prescriptionUploadSubmit?.[language],
                    prescriptionUploadError: response?.myAccount?.prescriptionUploadError?.[language],
                    prescriptionCreateSuccess: response?.myAccount?.prescriptionCreateSuccess?.[language],
                    prescriptionDeleteSuccess: response?.myAccount?.prescriptionDeleteSuccess?.[language],
                    prescriptionDeleteWarning: response?.myAccount?.prescriptionDeleteWarning?.[language],
                    prescriptionDeleteYes: response?.myAccount?.prescriptionDeleteYes?.[language],
                    prescriptionDeleteNo: response?.myAccount?.prescriptionDeleteNo?.[language],
                },
                productPage: {
                    compare: response?.productPage?.compare[language],
                    inc: response?.productPage?.inc[language],
                    color: response?.productPage?.color[language],
                    size: response?.productPage?.size[language],
                    addToCart: response?.productPage?.addToCart[language],
                    outOfStock: response?.productPage?.outOfStock[language],
                    productDescription: response?.productPage?.productDescription[language],
                    technicalInfo: response?.productPage?.technicalInfo[language],
                    contactLensPowerTitle: response?.productPage?.contactLensPowerTitle[language],
                    contactLensCheckBox: response?.productPage?.contactLensCheckBox[language],
                    sphere: response?.productPage?.sphere[language],
                    axis: response?.productPage?.axis[language],
                    cyl: response?.productPage?.cyl[language],
                    addition: response?.productPage?.addition[language],
                    subscription: response?.productPage?.subscription[language],
                    frequentlyBoughtWith: response?.productPage?.frequentlyBoughtWith[language],
                    recommendedProducts: response?.productPage?.recommendedProducts[language],
                    leftEye: response?.productPage?.leftEye[language],
                    rightEye: response?.productPage?.rightEye[language],
                    saving: response?.productPage?.saving[language],
                    totalPriceIncludingPower: response?.productPage?.totalPriceIncludingPower[language],
                    select: response?.productPage?.select[language],
                    chooseLens: response?.productPage?.chooseLens[language],
                    buyWithLensTitle: response?.productPage?.buyWithLensTitle[language],
                    buyWithLensText: response?.productPage?.buyWithLensText[language],
                    doYouWantYourBrand: response?.productPage?.doYouWantYourBrand[language],
                    singleVisionTitle: response?.productPage?.singleVisionTitle[language],
                    singleVisionDescription: response?.productPage?.singleVisionDescription[language],
                    progressiveTitle: response?.productPage?.progressiveTitle[language],
                    progressiveDescription: response?.productPage?.progressiveDescription[language],
                    frameOnlyTitle: response?.productPage?.frameOnlyTitle[language],
                    frameOnlyDescription: response?.productPage?.frameOnlyDescription[language],
                    next: response?.productPage?.next[language],
                    uploadPrescription: response?.productPage?.uploadPrescription[language],
                    uploadPhoto: response?.productPage?.uploadPhoto[language],
                    uploadPhotoTxt: response?.productPage?.uploadPhotoTxt[language],
                    enterManually: response?.productPage?.enterManually[language],
                    enterManuallyTxt: response?.productPage?.enterManuallyTxt[language],
                    checkPrescription: response?.productPage?.checkPrescription[language],
                    fileSize: response?.productPage?.fileSize[language],
                    fileType: response?.productPage?.fileType[language],
                    pd: response?.productPage?.pd[language],
                    iKnowPd: response?.productPage?.iKnowPd[language],
                    iDontKnowPd: response?.productPage?.iDontKnowPd[language],
                    userDetails: response?.productPage?.userDetails[language],
                    enquirySuccess: response?.productPage?.enquirySuccess[language],
                    continue: response?.productPage?.continue[language],
                    name: response?.productPage?.name?.[language],
                    brand: response?.productPage?.brand?.[language],
                    gender: response?.productPage?.gender?.[language],
                    men: response?.productPage?.men?.[language],
                    women: response?.productPage?.women?.[language],
                    kids: response?.productPage?.kids?.[language],
                    unisex: response?.productPage?.unisex?.[language],
                },
                cartPage: {
                    cartItems: response?.cartPage?.cartItems[language],
                    orderSummary: response?.cartPage?.orderSummary[language],
                    subtotal: response?.cartPage?.subtotal[language],
                    total: response?.cartPage?.total[language],
                    viewOffers: response?.cartPage?.viewOffers[language],
                    offersTitle: response?.cartPage?.offersTitle[language],
                    noOffers: response?.cartPage?.noOffers[language],
                    discountCode: response?.cartPage?.discountCode[language],
                    discountCodeText: response?.cartPage?.discountCodeText[language],
                    couponCode: response?.cartPage?.couponCode[language],
                    applyCoupon: response?.cartPage?.applyCoupon[language],
                    remove: response?.cartPage?.remove[language],
                    loyalty: response?.cartPage?.loyalty[language],
                    redeemableAmount: response?.cartPage?.redeemableAmount[language],
                    redeemed: response?.cartPage?.redeemed[language],
                    redeem: response?.cartPage?.redeem[language],
                    checkout: response?.cartPage?.checkout[language],
                    addShipping: response?.cartPage?.addShipping[language],
                    continueToDelivery: response?.cartPage?.continueToDelivery[language],
                    shippingDetails: response?.cartPage?.shippingDetails[language],
                    shippingMethods: response?.cartPage?.shippingMethods[language],
                    paymentMethods: response?.cartPage?.paymentMethods[language],
                    doorstepDelivery: response?.cartPage?.doorstepDelivery[language],
                    doorstepDeliveryText: response?.cartPage?.doorstepDeliveryText[language],
                    continueToPayment: response?.cartPage?.continueToPayment[language],
                    card: response?.cartPage?.card[language],
                    cod: response?.cartPage?.cod[language],
                    codText: response?.cartPage?.codText[language],
                    placeOrder: response?.cartPage?.placeOrder[language],
                    loyaltyDiscount: response?.cartPage?.loyaltyDiscount[language],
                    savings: response?.cartPage?.savings[language],
                    tryCartDeduction: response?.cartPage?.tryCartDeduction[language],
                    shipping: response?.cartPage?.shipping[language],
                    items: response?.cartPage?.items[language],
                    couponOffers: response?.cartPage?.couponOffers[language],
                    yourCartIsEmpty: response?.cartPage?.yourCartIsEmpty?.[language],
                    cartEmptyMsg: response?.cartPage?.cartEmptyMsg?.[language],
                    goShop: response?.cartPage?.goShop?.[language],
                    sphereLeft: response?.cartPage?.sphereLeft?.[language],
                    sphereRight: response?.cartPage?.sphereRight?.[language],
                    cylinderLeft: response?.cartPage?.cylinderLeft?.[language],
                    cylinderRight: response?.cartPage?.cylinderRight?.[language],
                    axisRight: response?.cartPage?.axisRight?.[language],
                    axisleft: response?.cartPage?.axisleft?.[language],
                    tamara: response?.cartPage?.tamara?.[language],
                    tamaraDesc: response?.cartPage?.tamaraDesc?.[language],
                    tabby: response?.cartPage?.tabby?.[language],
                    tabbyDesc: response?.cartPage?.tabbyDesc?.[language],
                    giftWrapping: response?.cartPage?.giftWrapping?.[language],

                },
                orderPage: {
                    orderNo: response?.orderPage?.orderNo[language],
                    orderDate: response?.orderPage?.orderDate[language],
                    estimatedDelivery: response?.orderPage?.estimatedDelivery[language],
                    orderStatus: response?.orderPage?.orderStatus[language],
                    paymentMethod: response?.orderPage?.paymentMethod[language],
                    viewDetail: response?.orderPage?.viewDetail[language],
                    customerDetails: response?.orderPage?.customerDetails[language],
                    orderPlaced: response?.orderPage?.orderPlaced[language],
                    confirm: response?.orderPage?.confirm[language],
                    shipped: response?.orderPage?.shipped[language],
                    delivered: response?.orderPage?.delivered[language],
                    cancelled: response?.orderPage?.cancelled[language],
                    invoiceSummary: response?.orderPage?.invoiceSummary[language],
                    paymentStatus: response?.orderPage?.paymentStatus[language],
                    cod: response?.orderPage?.cod[language],
                    online: response?.orderPage?.online[language],
                    pending: response?.orderPage?.pending[language],
                    paid: response?.orderPage?.paid[language],
                    downloadInvoice: response?.orderPage?.downloadInvoice[language],
                },
                popup: {
                    logoutTxt: response?.popup?.logoutTxt?.[language],
                    pleaseLogin: response?.popup?.pleaseLogin?.[language],
                    logoutBtn: response?.popup?.logoutBtn[language],
                    cancel: response?.popup?.cancel?.[language],
                    wishlistAdd: response?.popup?.wishlistAdd?.[language],
                    wishlistRemove: response?.popup?.wishlistRemove?.[language],
                    cartAdd: response?.popup?.cartAdd?.[language],
                    cartRemove: response?.popup?.cartRemove?.[language],
                    cartUpdate: response?.popup?.cartUpdate?.[language],
                    basketSubtotal: response?.popup?.basketSubtotal?.[language],
                    viewCart: response?.popup?.viewCart?.[language],
                    fileUploadSuccess: response?.popup?.fileUploadSuccess?.[language],
                    checkoutTitle: response?.popup?.checkoutTitle?.[language],
                    checkoutDescription: response?.popup?.checkoutDescription?.[language],
                    continueShopping: response?.popup?.continueShopping?.[language],
                    viewOrder: response?.popup?.viewOrder?.[language],
                    viewOrders: response?.popup?.viewOrders?.[language],
                    selectAccountType: response?.popup?.selectAccountType?.[language],
                    otpSentSuccess: response?.popup?.otpSentSuccess?.[language],
                    somethingWentWrong: response?.popup?.somethingWentWrong[language],
                    invalidOTP: response?.popup?.invalidOTP[language],
                    welcomeBack: response?.popup?.welcomeBack[language],
                    loggingOut: response?.popup?.loggingOut[language],
                    logoutSuccess: response?.popup?.logoutSuccess[language],
                    newsLetterSubscribedTitle: response?.popup?.newsLetterSubscribedTitle?.[language],
                    newsLetterSubscribedDescription: response?.popup?.newsLetterSubscribedDescription?.[language],
                    newsLetterSubscribedButton: response?.popup?.newsLetterSubscribedButton?.[language],
                    enquirySubmitTitle: response?.popup?.enquirySubmitTitle?.[language] ?? null,
                    enquirySubmitDescription: response?.popup?.enquirySubmitDescription?.[language] ?? null,
                    enquirySubmitButton: response?.popup?.enquirySubmitButton?.[language] ?? null,
                },
                login: {
                    welcomeTitle: response?.login?.welcomeTitle[language],
                    enterPhoneNumber: response?.login?.enterPhoneNumber[language],
                    pleaseWait: response?.login?.pleaseWait[language],
                    signup: response?.login?.signup[language],
                    signupTxt: response?.login?.signupTxt[language],
                    accountTxt: response?.login?.accountTxt[language],
                    signupToYateem: response?.login?.signupToYateem[language],
                    continueAsGuest: response?.login?.continueAsGuest[language],
                    haveInsurance: response?.login?.haveInsurance[language],
                    yes: response?.login?.yes[language],
                    no: response?.login?.no[language],
                    verifyTitle: response?.login?.verifyTitle[language],
                    verifyTxt: response?.login?.verifyTxt[language],
                    confirmationCode: response?.login?.confirmationCode[language],
                    didntReceiveCode: response?.login?.didntReceiveCode[language],
                    sendAgain: response?.login?.sendAgain[language],
                    verify: response?.login?.verify[language],
                    verifying: response?.login?.verifying[language],
                },
                search: {
                    result: response?.search?.result[language],
                    noResult: response?.search?.noResult[language],
                    popular: response?.search?.popular[language],
                    topCategory: response?.search?.topCategory[language],
                    search: response?.search?.search[language],
                },
                breadCrump: {
                    brands: response?.breadCrump?.brands?.[language],
                    home: response?.breadCrump?.home?.[language],
                    products: response?.breadCrump?.products?.[language],
                    insurance: response?.breadCrump?.insurance?.[language],
                    storeLocator: response?.breadCrump?.storeLocator?.[language],
                    myAccount: response?.breadCrump?.myAccount?.[language],
                    myCart: response?.breadCrump?.myCart?.[language],
                    contactUs: response?.breadCrump?.contactUs?.[language],
                    aboutUs: response?.breadCrump?.aboutUs?.[language],
                    privacyPolicy: response?.breadCrump?.privacyPolicy?.[language],
                    termsAndCondition: response?.breadCrump?.termsAndCondition?.[language],
                    shippingPolicy: response?.breadCrump?.shippingPolicy?.[language],
                    returnPolicy: response?.breadCrump?.returnPolicy?.[language],
                    cookiePolicy: response?.breadCrump?.cookiePolicy?.[language],
                    refundPolicy: response?.breadCrump?.refundPolicy?.[language],
                    allProducts: response?.breadCrump?.allProducts?.[language],
                    blogs: response?.breadCrump?.blogs?.[language],
                    category: response?.breadCrump?.category?.[language],
                },
                policy: {
                    privacyPolicy: response?.policy?.privacyPolicy?.[language],
                    termsAndCondition: response?.policy?.termsAndCondition?.[language],
                    shippingPolicy: response?.policy?.shippingPolicy?.[language],
                    returnPolicy: response?.policy?.returnPolicy?.[language],
                    cookiePolicy: response?.policy?.cookiePolicy?.[language],
                    refundPolicy: response?.policy?.refundPolicy?.[language],
                },
                homeTry: {
                    comingSoonTitle: response?.homeTry?.comingSoonTitle?.[language],
                    comingSoonText: response?.homeTry?.comingSoonText?.[language],
                    comingSoonBtn: response?.homeTry?.comingSoonBtn?.[language],
                },
                compare: {
                    compareBtn: response?.compare?.compareBtn?.[language],
                    removeAll: response?.compare?.removeAll?.[language],
                    title: response?.compare?.title?.[language],
                    addProduct: response?.compare?.addProduct?.[language],
                    sku: response?.compare?.sku?.[language],
                    price: response?.compare?.price?.[language],
                    rating: response?.compare?.rating?.[language],
                    color: response?.compare?.color?.[language],
                    sizesAvailable: response?.compare?.sizesAvailable?.[language],
                    description: response?.compare?.description?.[language],
                    addToCart: response?.compare?.addToCart?.[language],
                },
                other: {
                    home: response?.other?.home[language],
                    category: response?.other?.category[language],
                    account: response?.other?.account[language],
                    cart: response?.other?.cart[language],
                    storeLocator: response?.other?.storeLocator[language],
                    storeLocatorText: response?.other?.storeLocatorText[language],
                    share: response?.other?.share?.[language] ?? null,
                    ourBrands: response?.other?.ourBrands?.[language] ?? null,
                    nothingMoreToLoad: response?.other?.nothingMoreToLoad?.[language]?? null,
                    loading: response?.other?.loading?.[language]?? null,
                    enquireWithUs: response?.other?.enquireWithUs?.[language] ?? null,
                    availableStores: response?.other?.availableStores?.[language] ?? null,
                    download: response?.other?.download?.[language] ?? null,
                }
            }

        } else {
            data = response
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.updateTexts = async (req, res) => {
    try {
        let { storeid } = req.headers
        let { body } = req;
        storeid = storeid ? storeid : "ae";
        const response = await service.update({ storeId: storeid, isDelete: false }, body);
        await axios.post(process.env.REVALIDATE, { tag: "texts" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}