const mongoose = require("mongoose");

const attributeValueSchema = mongoose.Schema({
   refid: { type: String, required: true },
   value: { type: String, required: true },
   attribute: {type: mongoose.Types.ObjectId, ref: 'attributes'},
   // product: { type: mongoose.Types.ObjectId, ref: 'products' },
   isActive: { type: Boolean, default: true },
   isDelete: { type: Boolean, default: false },
   storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model("attributes.values", attributeValueSchema);