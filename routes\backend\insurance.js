const express = require('express')
const router = express.Router()
const controller = require('../../controllers/insurance.controller')
const upload = require('../../util/upload')

module.exports = () => {
    router.get('/insurance-enquiries', controller.getEnquiry)

    router.post('/add-insurance-provider', upload.single('logo'), controller.addProvider)
    router.get('/get-insurance-providers', controller.getProviders)
    router.get('/provider-details/:refid', controller.providerDetails)
    router.put('/update-provider/:refid', upload.single('logo'), controller.updateProvider)
    router.put('/delete-provider/:refid', controller.deleteProvider)

    router.post('/add-insurance-content', upload.single('ogImage'), controller.addContent)
    router.get('/get-insurance-content', controller.getContent)
    router.post('/update-insurance-content', upload.single('ogImage'), controller.updateContent)

    return router;
}