const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/category.service");
const db = require("../models");
const slug = require("../util/slug");
const axios = require("axios");
const productService = require("../services/products.service");
const fs = require("fs");
const path = require("path");
const AdmZip = require("adm-zip");
const csv = require("csv-parser");
const { promisify } = require("util");
const copyFile = promisify(fs.copyFile);
const mkdir = promisify(fs.mkdir);
const rm = promisify(fs.rm);
const dashboardSettingsService = require("../services/dashboard.settings.service");
const { uploadWebp } = require("../util/uploadWebp");
const generateUniqueNumber = require("../util/getRefid");

exports.validate = (method) => {
    switch (method) {
        case "create": {
            return [body("name", "Name is required").exists()];
        }
    }
};

async function uploadImage(req, body, categoryId) {
    if (req?.files?.image) {
        const name = `category/${categoryId}/image/${Date.now()}-${req?.files["image"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["image"][0]?.path, name)
        body.image = key;
    }
}

async function uploadBanner(req, body, categoryId) {
    if (req?.files?.banner) {
        const name = `category/${categoryId}/banner/${Date.now()}-${req?.files["banner"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["banner"][0]?.path, name)
        body.banner = key;
    }
}

async function uploadHoverImage(req, body, categoryId) {
    if (req?.files?.banner) {
        const name = `category/${categoryId}/hoverImage/${Date.now()}-${req?.files["hoverImage"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["hoverImage"][0]?.path, name)
        body.hoverImage = key;
    }
}

async function uploadSeoImage(req, body, categoryId) {
    if (req?.files?.ogImage){
        const name = `category/${categoryId}/ogImage/${Date.now()}-${req?.files["ogImage"][0]?.originalname}.webp`
        const {key} = await uploadWebp(req?.files["ogImage"][0]?.path, name)
        body.seoDetails.ogImage = key;
    }
}

exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        body.slug = await slug.createSlug(db.Category, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        body.refid = generateUniqueNumber();
        body.categoryId = `CAT-${body.refid}`
        body.storeId = storeid;

        await Promise.all([
            uploadImage(req, body, body.categoryId),
            uploadBanner(req, body, body.categoryId),
            uploadHoverImage(req, body, body.categoryId),
            uploadSeoImage(req, body, body.categoryId),
        ])

        const response = await service.create(body);
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        return helper.deliverResponse(res, 200, response, messages.CATEGORY.CREATED);
    } catch (error) {
        console.log(error);
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.getCategories = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ isDelete: false, isRoot: true, storeId: storeid });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.getChildCategories = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ isDelete: false, parent: req.params.id, storeId: storeid });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.getSubCategories = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findSub({ isDelete: false, parentRefId: req?.params?.refId, storeId: storeid });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.categoryDetails = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.subCategoryDetails = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOneSub({ _id: req?.params?.id, isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const categoryDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.name?.en !== categoryDetails?.name?.en) body.slug = await slug.createSlug(db.Category, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });

        if (body.image) delete body.image;
        if (body.banner) delete body.banner;
        if (body.hoverImage) delete body.hoverImage;
        if (body.ogImage) delete body.ogImage;
        await Promise.all([
            uploadImage(req, body, body.categoryId),
            uploadBanner(req, body, body.categoryId),
            uploadHoverImage(req, body, body.categoryId),
            uploadSeoImage(req, body, body.categoryId),
        ])

        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        return helper.deliverResponse(res, 200, response, messages.CATEGORY.UPDATED);
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.updateSubCategory = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const categoryDetails = await service.findOneSub({ _id: body?.id, isDelete: false, storeId: storeid });
        if (body?.name?.en !== categoryDetails?.name?.en) body.slug = await slug.createSlug(db.SubCategory, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        const response = await service.updateSub({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        return helper.deliverResponse(res, 200, response, messages.CATEGORY.UPDATED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};


exports.parentCategory = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const { type } = req.params;
        let query = { isDelete: false, isActive: true, isRoot: true, storeId: storeid };
        if (type == "home") query.inHome = true;
        const response = await service.find(query);
        if (type === "home") {
            response.sort((a, b) => a.order - b.order);
        }
        let data = {};
        if (language) {
            const dashboardSettings = await dashboardSettingsService.findOne({ storeId: storeid });
            const type = dashboardSettings.items.find((item) => item.type == "categories");
            data.title = type?.items[0]?.title?.[language];
            const defaultImagePath = process.env.DOMAIN + "/default.jpg";
            data.categories = response.map((item) => {
                return {
                    ...item?._doc,
                    name: item.name[language],
                    banner: item?.banner ? process.env.DOMAIN + item.banner : null,
                    image: item?.image ? process.env.DOMAIN + item.image : null,
                    hoverImage: item?.hoverImage ? process.env.DOMAIN + item.hoverImage : null,
                };
            });
        } else {
            data = response;
        }
        return helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.updateInHome = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const { refid } = req.params;
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CATEGORY.UPDATED.error_code,
            error_message: messages.CATEGORY.UPDATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params;
        async function recursiveDelete(refid) {
            const category = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
            const products = await productService.find({ category: category?._id, isDelete: false, storeId: storeid });
            // console.log(products)
            if (products?.length > 0) {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: `Category ${category?.name?.en} is used in products`,
                    }
                );
                return { error: true };
            }
            const childs = await service.find({ parent: category?._id, isDelete: false, storeId: storeid });
            if (childs && childs?.length > 0) {
                for (let child of childs) {
                    const response = await recursiveDelete(child.refid);
                    if (response?.error) return { error: true };
                }
            }
            await service.update({ parent: category?._id, isDelete: false, storeId: storeid }, { isDelete: true });
            const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
            return response;
        }
        const response = await recursiveDelete(refid);
        if (response?.error) return
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CATEGORY.DELETED.error_code,
            error_message: messages.CATEGORY.DELETED.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.deleteSubCatgory = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { id } = req.params;

        const response = await service.updateSub({ _id: id, isDelete: false, storeId: storeid }, { isDelete: true });
        if (req?.body?.isChild) {
            await db.SubCategory.findOneAndUpdate({ _id: req?.body?.subParent, isDelete: false, storeId: storeid }, { $pull: { childs: response?._id } });
        } else {
            if (req?.body?.parentRefId) {
                const res = await db.Category.findOneAndUpdate({ refid: req?.body?.parentRefId, storeId: storeids }, { $pull: { subCategory: response?._id } });
            }
        }
        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CATEGORY.DELETED.error_code,
            error_message: messages.CATEGORY.DELETED.error_message,
        });
    } catch (error) {
        console.log(error);
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.bulkImport = async (req, res) => {
    try {
        const zip = new AdmZip(req.file.path);
        const zipEntries = zip.getEntries();

        const csvDir = "tmp/csv";
        const imagesDir = "tmp/images";
        await mkdir(csvDir, { recursive: true });
        await mkdir(imagesDir, { recursive: true });

        zipEntries.forEach((entry) => {
            const fileName = entry.entryName;
            const targetDir = fileName.endsWith(".csv") ? csvDir : imagesDir;
            zip.extractEntryTo(fileName, targetDir, false, true);
        });

        await processCSVs(req, csvDir, imagesDir, res);
    } catch (error) {
        console.error(error, " :: Error caught in category import");
        res.status(422).json({
            error_code: "90",
            error_message: "An error occurred while processing the import.",
        });
    }
};

const processCSVs = async (req, csvDir, imagesDir, res) => {
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.flushHeaders();

    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";

    try {
        const categoriesFilePath = `${csvDir}/categories.csv`;
        const categories = await readCSV(categoriesFilePath);
        const totalCategories = categories.length;
        let processedCategories = 0;

        const categoryImagesDir = "uploads/categories/";
        await mkdir(categoryImagesDir, { recursive: true });

        const createdCategories = [];

        for (const category of categories) {
            let categoryCounter = await service.count({});
            const categoryDetail = await service.findOne({
                "name.en": category.name,
                isDelete: false,
                storeId: storeid
            });
            console.log(category.name)
            
            console.log("done 1")
            const categoryImageDestination = path.join(categoryImagesDir, category.category_image);
            const hoverImageDestination = path.join(categoryImagesDir, category.hover_image);
            console.log("done 2")
            
            await copyFile(path.join(imagesDir, category.category_image), categoryImageDestination);
            await copyFile(path.join(imagesDir, category.hover_image), hoverImageDestination);
            console.log("done 3")
            
            const payload = await createCategoryPayload(category, categoryDetail, categoryCounter, storeid);
            try {
                const newCategory = categoryDetail ? await service.update({ _id: categoryDetail?._id, storeId: storeid }, payload) : await service.create(payload);
                console.log(newCategory)
                
                console.log("done 4")
                createdCategories.push(newCategory);
                processedCategories++;
                res.write(`${processedCategories}/${totalCategories}\n\n`);
            } catch (error) {
                console.log(error)
            }
        }

        await createdCategoriesUpdate(createdCategories, categories, csvDir);
        res.write(`event: message\ndata: Categories imported successfully\n\n`);
        setTimeout(() => {
            cleanup(req.file.path, csvDir, imagesDir);
        }, 5000);
        res.end();
    } catch (error) {
        if (!res.headersSent) {
            res.status(422).json({
                error_code: "90",
                error_message: "An error occurred while processing the import.",
            });
        }
    }
};

const readCSV = async (filePath) => {
    const categories = [];
    return new Promise((resolve, reject) => {
        fs.createReadStream(filePath)
            .pipe(csv())
            .on("data", (data) => categories.push(data))
            .on("end", () => resolve(categories))
            .on("error", reject);
    });
};

const createCategoryPayload = async (category, categoryDetail, categoryCounter, storeId) => {
    // Implement payload creation logic here
    let payload = {
        name: {
            en: category?.name,
            ar: category?.name,
        },
        refid: categoryDetail ? categoryDetail?.refid : generateUniqueNumber(),
        slug:
            categoryDetail?.name != category?.name
                ? await slug.createSlug(db.Category, category?.name, {
                    slug: await slug.generateSlug(category?.name),
                    storeId,
                })
                : categoryDetail?.slug,

        hoverImage: `uploads/categories/${category.hover_image}`,
        image: `uploads/categories/${category.category_image}`,
        isRoot: category?.is_root_category == "1" ? true : false,
        isActive: category?.status == "1" ? true : false,
        storeId,
        categoryId: `CAT-00${categoryDetail ? categoryDetail?.refid : categoryCounter + 1}`
    };
    return payload;
};

const cleanup = async (uploadedFilePath, csvDir, imagesDir) => {
    try {
        await rm(csvDir, { recursive: true, force: true });
        await rm(imagesDir, { recursive: true, force: true });
    } catch (error) {
        console.error("Error during cleanup:", error);
    }
};

const createdCategoriesUpdate = async (createdCategories, categories, csvDir) => {
    // Implement category update logic here
    try {
        for (const category of createdCategories) {
            const csvRow = categories.find((row) => row.name === category.name.en);
            if (csvRow) {
                if (!category.isRoot && csvRow.parent_category) {
                    category.parent = category?._id;
                    await product.save();
                }
            }
        }
    } catch (error) {
        console.error("Error in createdCategoriesUpdate:", error);
    }
};

exports.updateCategoryOrder = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        if (body?.categories) {
            for (const category of body?.categories) {
                await service.update({ _id: category?._id, storeId: storeid }, { order: category?.order });
            }
        }
        return helper.deliverResponse(res, 200, {}, "Category order updated successfully");
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.createSubCategory = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        body.slug = await slug.createSlug(db.SubCategory, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        body.refid = generateUniqueNumber();
        body.categoryId = `CAT-${body.refid}`
        body.storeId = storeid;
        const response = await service.createSubCategory(body);
        if (body?.isChild) {
            await db.SubCategory.findOneAndUpdate({ _id: body?.subParentId, storeId: storeid }, { $push: { childs: response?._id } });
        } else {
            if (body?.parentRefId) {
                const res = await db.Category.findOneAndUpdate({ refid: body?.parentRefId, storeId: storeid }, { $push: { subCategory: response?._id } });
            }
        }

        await axios.post(process.env.REVALIDATE, { tag: "categories" });
        return helper.deliverResponse(res, 200, response, messages.CATEGORY.CREATED);
    } catch (error) {
        console.log(error);
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
};
