const express = require('express');
const router = express.Router();
const controller = require('../../controllers/faq.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/create-faq', authorize.verifyToken, controller.create);
    router.get('/get-faqs/:type', controller.getFaqs);
    router.post('/update-faq', controller.update);

    return router;
}