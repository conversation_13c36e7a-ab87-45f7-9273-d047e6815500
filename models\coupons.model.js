const mongoose = require('mongoose')

const couponSchema = new mongoose.Schema({
    title: { type: String, required: true },
    code: { type: String, required: true },
    discountType: { type: String, required: true, enum: ['price', 'percentage'] },
    discountValue: { type: Number, required: true },
    maxDiscount: { type: Number },
    minValue: { type: Number },
    validFrom: { type: Date },
    validTo: { type: Date },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    refid: { type: String, required: true },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'admin.users' },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('coupons', couponSchema)