const messages = require("../config/constants/messages");
const service = require("../services/header.menu.service");
const helper = require("../util/responseHelper");
const adminService = require("../services/admin.users.service");
const generalSettingsService = require("../services/general.settings.service");
const axios = require("axios");
const { uploadWebp } = require("../util/uploadWebp");

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { data } = req.body;
        data = JSON.parse(data);

        const { email } = res?.locals?.user;
        const adminDetails = await adminService.findOne({ email: email, isDelete: false });

        data.created_by = adminDetails?._id;
        data.refid = (await service.count({})) + 1;
        data.storeId = storeid;

        let result = await service.create(data);
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.HEADER_MENU.CREATED.error_code,
                error_message: messages.HEADER_MENU.CREATED.error_message
            });
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "header" });
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.HEADER_MENU.CREATED.error_code,
                error_message: messages.HEADER_MENU.CREATED.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let result = await service.find({ isDelete: false, storeId: storeid });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.detail = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        let result = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        if(!result) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        let { data } = req.body;
        data = JSON.parse(data);

        if (req?.files?.length > 0) {
            for (let i = 0; i < req?.files?.length; i++) {
                const file = req.files[i];
                data.submenus = data.submenus.map(async (submenu, index) => {
                    const file = req.files[`submenus[${index}].image`]?.[0];
                    const {key} = await uploadWebp(file?.path, `header/submenu/image/${Date.now()}-${file?.originalname}.webp`);
                    return {
                        ...submenu,
                        image: key ? key : submenu.image
                    };
                });
            }
        }

        let result = await service.update({ refid: refid, storeId: storeid }, data);
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.HEADER_MENU.UPDATED.error_code,
                error_message: messages.HEADER_MENU.UPDATED.error_message
            });
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "header" });
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.HEADER_MENU.UPDATED.error_code,
                error_message: messages.HEADER_MENU.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.headers = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        console.log(language, storeid);
        let response = [];
        let result = await service.find({ isDelete: false, storeId: storeid });

        let generalSettings = await generalSettingsService.findOne({ isDelete: false, storeId: storeid });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                error_code: messages.HEADER_MENU.HEADER.error_code,
                error_message: messages.HEADER_MENU.HEADER.error_message
            });
        } else {
            response = result.map((item) => {
                let updatedMenuType = item?.menuType;
                if (
                    item?.menuType == "category" ||
                    item?.menuType == "collection" ||
                    item?.menuType == "brand"
                ) {
                    updatedMenuType = "listing";
                }
                return {
                    ...item?._doc,
                    title: item?.title[language],
                    link: item?.link,
                    menuType: updatedMenuType,
                    submenus: item?.submenus.map((submenu) => {
                        return {
                            ...submenu?._doc,
                            title: submenu?.title[language],
                            menuType:
                                submenu?.menuType == "category" ||
                                submenu?.menuType == "collection" ||
                                submenu?.menuType == "brand"
                                    ? "listing"
                                    : submenu?.menuType,
                            image: process.env.DOMAIN + submenu?.image
                        };
                    })
                };
            });
            const data = {
                announcements: generalSettings?.text.map((item) => ({
                    text: item[language]
                })),
                whatsappNumber: generalSettings?.whatsappNumber,
                analyticsId: generalSettings?.analyticsId,
                tagManagerId: generalSettings?.tagManagerId,
                pixelId: generalSettings?.pixelId,
                logo: process.env.DOMAIN + generalSettings?.logo,
                tryCartBtnText:  generalSettings?.tryCartBtnText?.[language],
                header: response,
                seoDetails: generalSettings?.seoDetails,
                tamara: generalSettings?.tamara,
                tabby: generalSettings?.tabby,
                clickAndCollect: generalSettings?.clickAndCollect,
                isStoreLocatorIframe: generalSettings?.isStoreLocatorIframe,
                isGiftWrapping: generalSettings?.isGiftWrapping,
                giftWrappingFee: generalSettings?.giftWrappingFee
            };
            return helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params;
        let result = await service.update({ refid: refid, storeId: storeid }, { isDelete: true });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.HEADER_MENU.DELETED.error_code,
                error_message: "something went wrong"
            });
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "header" });    
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.HEADER_MENU.DELETED.error_code,
                error_message: messages.HEADER_MENU.DELETED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        return;
    }
}