const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require('../services/faq.service')
const adminService = require('../services/admin.users.service')
const axios = require('axios');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const { email } = res?.locals?.user;
        const adminDetails = await adminService.findOne({ email: email, isDelete: false });
        body.admin = adminDetails?._id;
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        await axios.post(process.env.REVALIDATE, { tag: "insurance-faq" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.FAQ.CREATED.error_code,
            "error_message": messages.FAQ.CREATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getFaqs = async (req, res) => {
    try {
        const { language } = req.headers;
        let { storeid } = req?.headers;
        storeid = storeid? storeid: "ae";
        const response = await service.find({ isDelete: false, type: req?.params?.type, storeId: storeid });
        let data;
        if (!response) {
            return helper.deliverResponse(res, 200, {}, {
                "error_code": messages.NO_DATA.error_code,
                "error_message": messages.NO_DATA.error_message
            });
        }
        if (language) {
            data = response.map(item => {
                return {
                    title: item.title[language] ?? item.title.en,
                    description: item.description[language] ?? item.description.en,
                    faq: item.faq.map(faq => {
                        return {
                            question: faq.question[language].length? faq.question[language]: faq.question.en,
                            answer: faq.answer[language].length? faq.answer[language]: faq.answer.en
                        };
                    }),
                };
            });
        } else {
            data = response;
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "insurance-faq" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.FAQ.UPDATED.error_code,
            "error_message": messages.FAQ.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}