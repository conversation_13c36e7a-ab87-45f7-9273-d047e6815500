const mongoose = require('mongoose');

const multiStoreSchema = new mongoose.Schema({
    refid: { type: String, required: true },
    name: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    currencyCode: { type: String },
    countryCode: { type: String },
    currencySymbol: { type: String },
    envs: {
        // CRM_ORG_ID: { type: String, required: true },
        // CRM_USERNAME: { type: String, required: true },
        // CRM_TOKEN: { type: String, required: true },
        // EMAIL_FROM: { type: String, required: true },
        // EMAIL_USERNAME: { type: String, required: true },
        // EMAIL_PASSWORD: { type: String, required: true },
        // ORDER_CCS: { type: String },
        // INSURANCE_CCS: { type: String },
        // ECO_API_KEY: { type: String, required: true },
        // MIM_USERID: { type: String, required: true },
        // MIM_PASSWORD: { type: String, required: true },
        // MIM_FIALOVER_ID: { type: String, required: true },
        // MIM_FIALOVER_KEY: { type: String, required: true },
        NOON_API_KEY: { type: String, required: true },
        NOON_API_URL: { type: String, required: true },
        TAMARA_API_URL: { type: String, required: true },
        TAMARA_API_TOKEN: { type: String, required: true },
        TAMARA_NOTIFICATION_TOKEN: { type: String, required: true },
        TABBY_TOKEN: { type: String, required: true }
    },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true }
}, { timestamps: true });

module.exports = mongoose.model('multi.stores', multiStoreSchema);