const mongoose = require("mongoose");

const VMPolicySchema = new mongoose.Schema({
    title: {
        en: { type: String, required: true },
        ar: { type: String },
    },
    content: {
        en: { type: String, required: true },
        ar: { type: String },
    },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, {timestamps: true})

module.exports = mongoose.model("vm.policy", VMPolicySchema)