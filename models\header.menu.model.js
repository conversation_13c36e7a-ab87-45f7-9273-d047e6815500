const mongoose = require('mongoose')

const headerMenuSchema = new mongoose.Schema({
    title: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    link : { type: String, required: true },
    isSubmenu: { type: Boolean, required: true },
    menuType: { type: String, enum: ['category', 'product', 'collection', 'brand', 'page'] },
    redirection: { type: String },
    submenus: [{
        title: {
            en: { type: String },
            ar: { type: String }
        },
        menuType: { type: String, enum: ['category', 'product', 'collection', 'brand', 'page'] },
        redirection: { type: String },
        image: { type: String },
    }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'admin.users' },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('header.menu', headerMenuSchema)
