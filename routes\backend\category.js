const express = require('express');
const router = express.Router();
const controller = require('../../controllers/category.controller')
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-category', upload.fields([{ name: 'image', maxCount: 1 }, { name: 'banner', maxCount: 1 }, { name: 'hoverImage', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), controller.create);
    router.post('/create-sub-category', controller.createSubCategory);
    router.get('/get-categories', controller.getCategories);
    router.get('/get-child-categories/:id', controller.getChildCategories);
    router.get('/sub-category/:refId', controller.getSubCategories);
    router.get('/category-details/:refid', controller.categoryDetails);
    router.get('/sub-category-details/:id', controller.subCategoryDetails);
    router.post('/update-category', upload.fields([{ name: 'image', maxCount: 1 }, { name: 'banner', maxCount: 1 }, { name: 'hoverImage', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), controller.update);
    router.post('/update-sub-category', controller.updateSubCategory);
    router.get('/parent-category', controller.parentCategory);
    router.put('/delete-category/:refid', controller.delete);
    router.put('/delete-sub-category/:id', controller.deleteSubCatgory);

    router.put('/update-inHome/:refid', controller.updateInHome);
    router.put('/update-category-order', controller.updateCategoryOrder);

    router.post('/import-categories', upload.single('file'), controller.bulkImport);

    return router;
}
