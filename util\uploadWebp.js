const sharp = require('sharp');
const { uploadToS3 } = require('./uploadToS3');
const fs = require('fs');

const uploadWebp = async (filePath, name, type = 'image/webp') => {
    let webpBuffer;
    if(type == 'image/webp'){
        webpBuffer = await sharp(filePath).webp({ quality: 80 }).toBuffer();
    }else if(type.includes('image/')){
        webpBuffer = await sharp(filePath, { animated: true }).toBuffer();
    }else{
        webpBuffer = fs.readFileSync(filePath);
    }
    const { key, fileURL } = await uploadToS3(webpBuffer, name, type)
    return {
        key, fileURL
    }
};

module.exports = {
    uploadWebp
}