const express = require('express');
const router = express.Router();
const controller = require('../../controllers/attribute.controller');
const authorize = require('../../middlewares/authorize');
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-attribute', upload.array('files', 20), authorize.verifyToken, controller.create);
    router.get('/get-attributes', controller.getAttributes);
    router.get('/attribute-details/:refid', controller.attributeDetails);
    router.put('/update-attribute/:refid', upload.array('files', 20), authorize.verifyToken, controller.update);

    router.post('/attribute-value', controller.getAttributeValues);

    return router;
}
