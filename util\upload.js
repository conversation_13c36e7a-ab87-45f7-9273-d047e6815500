const multer = require("multer");
const fs = require("fs");

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        let folderPath = `uploads/`;
        let reqPath = req.path;

        if (reqPath.includes('create-brand') || reqPath.includes('update-brand')) {
            folderPath += 'brands/';
        }

        if (reqPath.includes('create-category') || reqPath.includes('update-category')) {
            folderPath += 'categories/';
        }

        if (reqPath.includes('create-about') || reqPath.includes('update-about')) {
            folderPath += 'abouts/';
        }

        if (reqPath.includes('create-frame-shape') || reqPath.includes('update-frame-shape')) {
            folderPath += 'frameShapes/';
        }

        if (reqPath.includes('create-frame-type') || reqPath.includes('update-frame-type')) {
            folderPath += 'frameTypes/';
        }

        if (reqPath.includes('create-product') || reqPath.includes('update-product')) {
            folderPath += 'products/';
        }

        if (reqPath.includes('create-prescription') || reqPath.includes('update-prescription')) {
            folderPath += 'prescriptions/';
        }

        if (reqPath.includes('create-blog') || reqPath.includes('update-blog')) {
            folderPath += 'blogs/';
        }

        if (reqPath.includes('add-contact-banner') || reqPath.includes('update-contact-banner')) {
            folderPath += 'contactBanners/';
        }

        if (reqPath.includes('create-admin') || reqPath.includes('update-admin')) {
            folderPath += 'adminUsers/';
        }

        if (reqPath.includes('add-contact-banner') || reqPath.includes('update-contact-banner')) {
            folderPath += 'contactBanners/';
        }

        if (reqPath.includes('add-insurance-provider') || reqPath.includes('update-provider')) {
            folderPath += 'insuranceProviders/';
        }

        if (reqPath.includes('add-banner') || reqPath.includes('update-banner')) {
            folderPath += 'banners/';
        }

        if (reqPath.includes('video-upload')) {
            folderPath += 'videos/';
        }

        fs.mkdirSync(folderPath, { recursive: true });
        cb(null, folderPath);
    },
    filename: function (req, file, cb) {
        filename = Date.now() + "-" + file.originalname.replaceAll(" ", "_");
        cb(null, filename);
    },
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 1024 * 1024 * 5000,
    },
});

module.exports = upload;