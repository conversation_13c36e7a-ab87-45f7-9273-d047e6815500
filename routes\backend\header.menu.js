const express = require('express');
const router = express.Router();
const controller = require('../../controllers/header.menu.controller');
const authorize = require('../../middlewares/authorize');
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-menu', upload.any('image'), authorize.verifyToken, controller.create);
    router.get('/get-menu', controller.list);
    router.get('/menu-details/:refid', controller.detail);
    router.put('/update-menu/:refid', upload.any('image'), controller.update);
    router.put('/delete-menu/:refid', controller.delete);

    return router;
}