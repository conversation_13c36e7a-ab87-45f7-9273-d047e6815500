const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.Order(data)
        await response.save()
        return await db.Order.findById(response?._id)
            .populate('products.contactLens.sphLeft', 'name')
            .populate('products.contactLens.cylLeft', 'name')
            .populate('products.contactLens.axisLeft', 'name')
            .populate('products.contactLens.sphRight', 'name')
            .populate('products.contactLens.cylRight', 'name')
            .populate('products.contactLens.axisRight', 'name')
            .populate('products.size', "name")
            .populate('address');
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}, page = 0, limit = 0, sort = { createdAt: -1 }) => {
    try {
        console.log(limit * 1)
        console.log((page - 1) * limit)
        let response = await db.Order.find(query, projection)
            .sort(sort)
            .skip((page - 1) * limit)
            .limit(limit * 1)
            .populate("customer", "name mobile")
            .populate("address", "mobile city name")
            .populate("products.size")
            .populate({
                path: "products.product",
                select: "name thumbnail color slug brand",
                populate: [
                    { path: "color", select: "name color" },
                    { path: "brand", select: "name" },
                ]
            });
        return response;
    } catch (error) {
        throw error;
    }
};

exports.detail = async (query, projection = {}) => {
    try {
        let response = await db.Order.findOne(query, projection)
            .populate("customer", "name")
            .populate("cart")
            .populate("address")
            .populate("store")
            .populate("products.size", "name")
            .populate({
                path: "products.product",
                select: "name thumbnail color sku",
                populate: { path: "color", select: "name color" }
            })
            .populate('products.lensDetails.brand', 'name')
            .populate('products.lensDetails.index', 'name')
            .populate('products.lensDetails.coating', 'name')
            .populate('products.contactLens.sphLeft', 'name')
            .populate('products.contactLens.cylLeft', 'name')
            .populate('products.contactLens.axisLeft', 'name')
            .populate('products.contactLens.sphRight', 'name')
            .populate('products.contactLens.cylRight', 'name')
            .populate('products.contactLens.axisRight', 'name')
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Order.findOne(query, projection)
            .populate("products.contactLens.sphLeft", "name")
            .populate("products.contactLens.cylLeft", "name")
            .populate("products.contactLens.axisLeft", "name")
            .populate("products.contactLens.sphRight", "name")
            .populate("products.contactLens.cylRight", "name")
            .populate("products.contactLens.axisRight", "name")
            .populate("products.size", "name")
            .populate("store")
            .populate({
                path: "products.product",
                select: "name brand sku brand category color",
                populate: [
                    { path: "color", select: "name color" },
                    { path: "category", select: "name" },
                    { path: "brand", select: "name" },
                ]
            })
            .populate("customer")
            .populate("address")
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.Order.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.pull = async (query, data) => {
    try {
        let response = await db.Order.findOneAndUpdate(
            query,
            { $pull: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.Order.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.aggregate = async (query) => {
    try {
        let response = await db.Order.aggregate(query);
        return response;
    } catch (error) {
        throw error;
    }
};