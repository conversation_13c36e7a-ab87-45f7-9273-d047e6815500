const express = require("express");
const router = express.Router();
const controller = require("../../controllers/products.controller");
const upload = require("../../util/upload");
const authorize = require("../../middlewares/authorize");

module.exports = () => {
    router.post(
        "/create-product",
        authorize.verifyToken,
        upload.fields([
            { name: "images", maxCount: 10 },
            { name: "banner", maxCount: 1 },
            { name: "descriptionFiles", maxCount: 5 },
            { name: "thumbnail", maxCount: 1 },
            { name: "variantImage", maxCount: 1 },
            { name: "video", maxCount: 1 },
            { name: "ogImage", maxCount: 1 },
        ]),
        controller.create
    );

    router.post("/create-duplicate", authorize.verifyToken, controller.createDuplicate)

    router.post("/get-products", controller.getProducts);
    router.post("/get-sunglasses", controller.getSunglasses);
    router.get("/product-details/:refid", controller.getProductDetails);

    router.post(
        "/update-product",
        authorize.verifyToken,
        upload.fields([
            { name: "images", maxCount: 10 },
            { name: "banner", maxCount: 1 },
            { name: "descriptionFiles", maxCount: 5 },
            { name: "thumbnail", maxCount: 1 },
            { name: "variantImage", maxCount: 1 },
            { name: "ogImage", maxCount: 1 },
        ]),
        controller.update
    );

    router.get("/get-variants/:refid", controller.getVariants);

    router.put("/remove-variant/:refid", authorize.verifyToken, controller.removeVariant);

    router.post("/add-existing-variant", authorize.verifyToken, controller.addExistingVariant);

    router.put("/delete-product/:refid", authorize.verifyToken, controller.delete);

    router.post("/product-import", upload.single("file"), controller.bulkImports);

    router.post("/sunglass-import", upload.single("file"), controller.bulkSunglassImport);
    router.post("/contact-lens-import", upload.single("file"), controller.bulkContactLensImport);
    router.post("/accessory-import", upload.single("file"), controller.bulkAccessoryImport);

    router.post("/create-contact-lenses", authorize.verifyToken, controller.createContactLens);
    router.post("/update-contact-lenses", authorize.verifyToken, controller.updateContactLens);

    // router.post("/contactLens-import", upload.single("file"), controller.bulkContactLensImport);

    router.get("/variant-colors/:refid", controller.getVariantColors);

    router.put("/bulk-update", controller.bulkUpdate);

    router.post("/generate-slug", controller.generateSlug);

    router.get("/export-csv", controller.exportCSV)
    router.get("/export-sunglass-csv", controller.exportSunglassCSV)
    router.get("/export-contact-lens-csv", controller.exportContactLensCSV)
    router.get("/export-accessory-csv", controller.exportAccessoryCSV)

    router.get("/generate-product-feed", controller.generateXmlFeed)

    return router;
};
