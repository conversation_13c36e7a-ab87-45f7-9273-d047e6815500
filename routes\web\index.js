const express = require('express')
const router = express.Router();
const logger = require("morgan");
router.use(logger("dev"));

const generalRoutes = require("./general");
const productRoutes = require("./products");
const blogRoutes = require("./blogs");
const contactUsRoutes = require("./contactUs");
const stores = require("./stores");
const insuranceRoutes = require("./insurance");
const customerRoutes = require("./customer");
const faqRoutes = require("./faq");
const categoryRoutes = require("./category");
const newsletterRoutes = require("./newsletter");
const homeRoutes = require("./home");
const cartRoutes = require("./cart");
const brands = require("./brands");
const order = require("./order");
const frame = require("./frame");
const collection = require("./collection");
const contactLens = require("./contact.lens");
const lens = require("./lens");
const subscription = require("./subscription");
const paymentMethodFees = require("./paymentMethodFee");
const vmPolicy = require("./vmPolicy")

module.exports = () => {
    router.use((req, res, next) => {
        res._json = res.json;
        res.json = function json(obj) {
            obj.APIType = "App";
            obj.APIVersion = 1;
            res._json(obj);
        };
        next();
    });

    router.use("", generalRoutes());
    router.use("", productRoutes());
    router.use("", blogRoutes());
    router.use("", contactUsRoutes());
    router.use("", stores());
    router.use("", insuranceRoutes());
    router.use("", customerRoutes());
    router.use("", faqRoutes());
    router.use("", categoryRoutes());
    router.use("", newsletterRoutes());
    router.use("", homeRoutes());
    router.use("", cartRoutes());
    router.use("", brands());
    router.use("", order());
    router.use("", frame());
    router.use("", collection());
    router.use("", contactLens());
    router.use("", lens());
    router.use("", subscription());
    router.use("", paymentMethodFees());
    router.use("", vmPolicy());

    return router;
}