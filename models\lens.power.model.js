const mongoose = require("mongoose");

const lensPowerSchema = new mongoose.Schema({
    type: { type: String, required: true, enum: ['Sph', 'Cyl', 'Axis', 'Add'] },
    brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand", required: true },
    name: { type: String, required: true },
    price: { type: Number, required: true, default: 0 },
    currency: { type: String, default: "AED" },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("lens.power", lensPowerSchema)