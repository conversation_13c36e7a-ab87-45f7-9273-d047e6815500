const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.AttributeValues(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.AttributeValues.find(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.AttributeValues.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.AttributeValues.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.AttributeValues.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.delete = async (query) => {
    try {
       const response = await db.AttributeValues.deleteOne(query)
       return response;
    } catch (error) {
       throw error;
    }
 }