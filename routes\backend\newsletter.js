const express = require('express');
const router = express.Router();
const controller = require('../../controllers/newsletter.controller');

module.exports = () => {

    router.post('/get-newsletter', controller.getNewsletter);
    router.post('/newsletter', controller.subscribe);
    router.delete('/newsletter/:refid', controller.delete)
    router.put('/newsletter-unsubscribe/:refid', controller.unsubscribe)

    return router;
}
