const mongoose = require("mongoose");

const productHeadSchema = new mongoose.Schema(
    {
        refid: { type: String, required: true },
        name: { type: String, required: true },
        type: { type: String, enum: ["contactLens", "frame", "accessory"], default: "frame" },
        sku: { type: String, required: true },
        isActive: { type: Boolean, default: true },
        isDelete: { type: Boolean, default: false },
        products: [{ type: mongoose.Schema.Types.ObjectId, ref: "products" }],
        storeId: { type: String, required: true, default: 'ae' }
    },
    { timestamps: true }
);



module.exports = mongoose.model("product.head", productHeadSchema);
