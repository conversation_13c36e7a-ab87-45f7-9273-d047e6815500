const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const orderService = require("../services/order.service");
const productService = require("../services/products.service");
const categoryService = require("../services/category.service");

exports.statisticData = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const currentStartDate = new Date(); // Current start date (e.g., today)
        const currentEndDate = new Date(
            currentStartDate.getFullYear(),
            currentStartDate.getMonth(),
            1
        ); // Current end date (e.g., first day of the current month)
        const previousStartDate = new Date(
            currentEndDate.getFullYear(),
            currentEndDate.getMonth() - 3,
            1
        ); // Previous start date (e.g., 3 months before the current end date)
        const previousEndDate = new Date(
            previousStartDate.getFullYear(),
            previousStartDate.getMonth() + 3,
            0
        ); // Previous end date (e.g., last day of the previous 3-month period)

        const totalOrders = await getTotalOrderCount(storeid);
        const totalRevenue = await getTotalRevenue(storeid);
        const totalAverageSale = await getTotalAverageSale(storeid);

        const previousOrders = await getOrderCount(previousStartDate, previousEndDate, storeid);
        const previousRevenue = await getRevenue(previousStartDate, previousEndDate, storeid);
        const previousAverageSale = await getAverageSale(previousStartDate, previousEndDate, storeid);

        const ordersGrowShrink = calculateGrowShrink(totalOrders, previousOrders, true);
        const revenueGrowShrink = calculateGrowShrink(totalRevenue, previousRevenue, true);
        const averageSaleGrowShrink = calculateGrowShrink(
            totalAverageSale,
            previousAverageSale,
            true
        );

        const data = {
            statisticData: {
                revenue: { value: totalRevenue, growShrink: revenueGrowShrink },
                orders: { value: totalOrders, growShrink: ordersGrowShrink },
                purchases: { value: totalAverageSale.toFixed(2), growShrink: averageSaleGrowShrink }
            }
        };

        helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

const getTotalOrderCount = async (storeId) => {
    const totalOrdersQuery = [
        { $match: { isActive: true, isDelete: false, storeId } },
        { $group: { _id: null, totalOrders: { $sum: 1 } } }
    ];
    const result = await orderService.aggregate(totalOrdersQuery);
    return result.length > 0 ? result[0].totalOrders : 0;
};

const getTotalRevenue = async (storeId) => {
    const totalRevenueQuery = [
        { $match: { isActive: true, isDelete: false, storeId } },
        { $group: { _id: null, total: { $sum: "$total" } } }
    ];
    const result = await orderService.aggregate(totalRevenueQuery);
    return result.length > 0 ? result[0].total : 0;
};

const getTotalAverageSale = async (storeId) => {
    const averageSaleQuery = [
        { $match: { isActive: true, isDelete: false, storeId } },
        { $group: { _id: null, averageSale: { $avg: "$total" } } }
    ];
    const result = await orderService.aggregate(averageSaleQuery);
    return result.length > 0 ? result[0].averageSale : 0;
};

const getOrderCount = async (startDate, endDate, storeId) => {
    const ordersQuery = [
        { $match: { storeId, orderDate: { $gte: startDate, $lte: endDate } } },
        { $group: { _id: null, totalOrders: { $sum: 1 } } }
    ];
    const result = await orderService.aggregate(ordersQuery);
    return result.length > 0 ? result[0].totalOrders : 0;
};

const getRevenue = async (startDate, endDate, storeId) => {
    const revenueQuery = [
        { $match: { storeId, orderDate: { $gte: startDate, $lte: endDate } } },
        { $group: { _id: null, total: { $sum: "$total" } } }
    ];
    const result = await orderService.aggregate(revenueQuery);
    return result.length > 0 ? result[0].total : 0;
};

const getAverageSale = async (startDate, endDate, storeId) => {
    const averageSaleQuery = [
        { $match: { storeId, orderDate: { $gte: startDate, $lte: endDate } } },
        { $group: { _id: null, averageSale: { $avg: "$total" } } }
    ];
    const result = await orderService.aggregate(averageSaleQuery);
    return result.length > 0 ? result[0].averageSale : 0;
};

const calculateGrowShrink = (current, previous, isPercentage = false) => {
    if (previous === 0) return 0;
    const growShrink = ((current - previous) / previous) * 100;
    const cappedGrowShrink = Math.min(growShrink, 100);
    const roundedGrowShrink = isPercentage ? Number(cappedGrowShrink.toFixed(0)) : cappedGrowShrink;
    return roundedGrowShrink;
};

exports.latestOrderData = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const latestOrders = await orderService.find({ isDelete: false, isActive: true, storeId: storeid }, {}, 10);
        let data = [];
        const statusMapping = {
            PENDING: 1,
            PLACED: 3,
            CONFIRMED: 4,
            SHIPPED: 5,
            "OUT FOR DELIVERY": 6,
            DELIVERED: 7,
            CANCELLED: 8,
            FAILED: 2,
            REFUNDED: 9,
            RETURNED: 10
        };
        for (let order of latestOrders) {
            order.status = statusMapping[order?.orderStatus] || 0; // Default to 0 if not found

            data.push({
                id: order?.orderNo,
                date: order?.orderDate,
                customer: order?.customer?.name,
                status: order?.status,
                paymentMethod: order?.paymentMethod,
                totalAmount: order?.total || 0
            });
        }
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.topSellingData = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        // Aggregation pipeline to get the top 10 selling products and their sold count
        const pipeline = [
            {
                $match: { storeId: storeid }
            },
            {
                $unwind: "$products" // Unwind the products array
            },
            {
                $group: {
                    _id: "$products.product", // Group by product ID
                    sold_count: { $sum: "$products.quantity" } // Sum the quantity sold
                }
            },
            {
                $sort: { sold_count: -1 } // Sort by sold count in descending order
            },
            {
                $limit: 10 // Limit to top 10
            }
        ];
        const result = await orderService.aggregate(pipeline);
        let data = [];

        for (let item of result) {
            const product = await productService.findOne({ _id: item._id, isDelete: false, storeId: storeid });
            data.push({
                id: product?._id,
                name: product?.name.en,
                img: product?.thumbnail,
                sold: item?.sold_count
            });
        }
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.salesByCategories = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        // Fetch all unique categories from the Product collection
        const categories = await categoryService.find(
            { isDelete: false, isRoot: true, storeId: storeid },
            { name: 1, _id: 1 }
        );

        // Aggregation pipeline to get the count of products sold for each category
        const pipeline = [
            {
                $match: { storeId: storeid }
            },
            {
                $unwind: "$products" // Unwind the products array
            },
            {
                $lookup: {
                    from: "products", // Assuming 'products' is the name of your products collection
                    localField: "products.product",
                    foreignField: "_id",
                    as: "product_info"
                }
            },
            {
                $unwind: "$product_info"
            },
            {
                $unwind: "$product_info.category" // Unwind the category array within each product
            },
            {
                $group: {
                    _id: "$product_info.category", // Group by product category
                    sold_count: { $sum: "$products.quantity" } // Sum the quantity sold
                }
            },
            {
                $sort: { sold_count: -1 } // Sort by sold count in descending order
            }
        ];

        // Execute the aggregation pipeline
        const result = await orderService.aggregate(pipeline);

        // Initialize the labels and data arrays
        const labels = [];
        const data = [];

        // Iterate over the unique categories
        for (const category of categories) {
            // Find the category in the aggregation result
            const categoryResult = result.find(
                (item) => item._id.toString() === category._id.toString()
            );
            // If the category exists in the result, add its sold count; otherwise, add 0
            labels.push(category.name?.en);
            data.push(categoryResult ? categoryResult.sold_count : 0);
        }

        // Format the results into the desired structure
        const salesByCategoriesData = {
            labels,
            data
        };

        // Send the results back to the client
        helper.deliverResponse(res, 200, salesByCategoriesData, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.salesReport = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const pipeline = [
            { $match: { storeId: storeid } },
            {
                $project: {
                    month: { $month: "$orderDate" },
                    year: { $year: "$orderDate" },
                    total: "$total"
                }
            },
            {
                $group: {
                    _id: { month: "$month", year: "$year" },
                    avgTotal: { $avg: "$total" }
                }
            },
            {
                $sort: { "_id.year": 1, "_id.month": 1 }
            }
        ];

        // Execute the aggregation pipeline
        const result = await orderService.aggregate(pipeline);

        // Initialize the series and categories arrays
        const series = [{ name: "", data: [] }];
        const categories = [];

        // Iterate over the result to populate the series and categories arrays
        result.forEach((item) => {
            const month = item._id.month;
            const year = item._id.year;
            const avgTotal = Number(item.avgTotal).toFixed(2);

            // Convert month number to month name
            const monthName = new Date(year, month - 1).toLocaleString("default", {
                month: "short"
            });
            const formattedMonthYear = `${monthName} ${year}`;

            // Add the average total to the series data
            series[0].data.push(avgTotal);

            // Add the formatted month and year to the categories
            categories.push(formattedMonthYear);
        });

        // Prepare the response data
        const salesReportData = {
            series,
            categories
        };

        helper.deliverResponse(res, 200, salesReportData, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};
