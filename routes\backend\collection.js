const express = require('express');
const router = express.Router();
const controller = require('../../controllers/collection.controller');
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-collection', upload.fields([{ name: 'image', maxCount: 1 }, { name: 'banner', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), controller.create);
    router.get('/get-collections', controller.getCollections);
    router.get('/collection-details/:refid', controller.collectionDetails);
    router.put('/update-collection/:refid', upload.fields([{ name: 'image', maxCount: 1 }, { name: 'banner', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), controller.update);
    router.put('/delete-collection/:refid', controller.delete);

    return router;
}