const mongoose = require("mongoose");

const textSchema = new mongoose.Schema({
    footer: {
        top: [
            {
                title: {
                    en: { type: String, required: true },
                    ar: { type: String }
                },
                description: {
                    en: { type: String, required: true },
                    ar: { type: String }
                }
            }
        ],
        followUs: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        newsLetter: {
            text: {
                en: { type: String, required: true },
                ar: { type: String }
            },
            placeholder: {
                en: { type: String, required: true },
                ar: { type: String }
            },
        },
        doYouNeedHelp: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        poweredBy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    formFields: {
        fullName: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        fullNameRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        numericError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emailAddress: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emailAddressRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emailAddressInvalidError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emailAddressYahooError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        phoneNumber: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        phoneNumberRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        phoneNumberInvalidError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeEnquiry: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeEnquiryRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        selectStore: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        submit: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cancel: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        message: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        messageRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        nameOfInsurance: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        nameOfInsuranceRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        selectInsurance: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        nationality: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        nationalityRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        country: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        countryRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emirates: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emiratesRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        selectEmirates: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emiratesId: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emiratesIdRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emiratesIdInvalidError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        uploadEmirates: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        memberId: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        insuranceId: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        streat: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        streatRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        city: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cityRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        apartment: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        postalCode: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        address: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressRequired: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressType: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressTypeRequiredError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    productListing: {
        sortBy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        filterBy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        apply: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        reset: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        clearAll: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        showing: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        products: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        category: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frameType: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frameShape: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        brands: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frameSize: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frontMaterial: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        color: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        price: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        types: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        lensType: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        virtualTry: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewMore: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        newArrivals: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sale: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        exclusive: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        priceLowToHigh: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        priceHighToLow: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        noProductsFound: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    myAccount: {
        myProfile: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myCart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tryCart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myOrders: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myWishlist: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myAddressBook: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        mySubscription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myPrescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myCashbacks: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        login: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        logout: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        search: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeLocator: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        personalInformation: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressHome: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressWork: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressOther: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressDefault: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        setDefaultAddress: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressEdit: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressRemove: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressAdd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addressSaveCheck: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        uploadPrescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        emptyPrescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        totalCashbackEarned: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        totalCashbackSpent: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cashbackBalance: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cashbackSpent: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cashbackEarned: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        noCashbackHistory: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        notes: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadInputLabel: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadInputPlaceholder: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadInputFile: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadFileInfo: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadInfo: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadSubmit: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionUploadError: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionCreateSuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionDeleteSuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionDeleteWarning: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionDeleteYes: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        prescriptionDeleteNo: {
            en: { type: String, required: true },
            ar: { type: String }
        },

    },
    productPage: {
        compare: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        inc: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        color: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        size: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addToCart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        outOfStock: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        productDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        technicalInfo: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        contactLensPowerTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        contactLensCheckBox: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sphere: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        axis: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cyl: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addition: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        subscription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frequentlyBoughtWith: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        recommendedProducts: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        leftEye: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        rightEye: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        saving: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        totalPriceIncludingPower: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        select: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        chooseLens: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        buyWithLensTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        buyWithLensText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        doYouWantYourBrand: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        singleVisionTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        singleVisionDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        progressiveTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        progressiveDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frameOnlyTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        frameOnlyDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        next: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        uploadPrescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        uploadPhoto: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        uploadPhotoTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enterManually: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enterManuallyTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        checkPrescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        fileSize: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        fileType: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        pd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        iKnowPd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        iDontKnowPd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        userDetails: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enquirySuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        continue: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        name: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        brand: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        gender: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        men: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        women: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        kids: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        unisex: {
            en: { type: String, required: true },
            ar: { type: String }
        },

    },
    cartPage: {
        cartItems: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        orderSummary: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        subtotal: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        loyaltyDiscount: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        savings: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tryCartDeduction: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shipping: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        items: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        total: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewOffers: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        offersTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        couponOffers: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        noOffers: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        discountCode: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        discountCodeText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        couponCode: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        applyCoupon: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        remove: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        loyalty: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        redeemableAmount: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        redeemed: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        redeem: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        checkout: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addShipping: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        continueToDelivery: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shippingDetails: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shippingMethods: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        paymentMethods: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        doorstepDelivery: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        doorstepDeliveryText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        continueToPayment: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        card: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cod: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        codText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        placeOrder: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        yourCartIsEmpty: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cartEmptyMsg: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        goShop: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sphereLeft: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sphereRight: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cylinderLeft: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cylinderRight: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        axisRight: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        axisleft: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tamara: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tamaraDesc: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tabby: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        tabbyDesc: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        giftWrapping: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        
    },
    orderPage: {
        orderNo: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        orderDate: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        estimatedDelivery: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        orderStatus: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        paymentMethod: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewDetail: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        customerDetails: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        orderPlaced: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        confirm: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shipped: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        delivered: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cancelled: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        invoiceSummary: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        paymentStatus: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cod: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        online: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        pending: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        paid: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        downloadInvoice: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    popup: {
        logoutTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        pleaseLogin: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        logoutBtn: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cancel: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        wishlistAdd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        wishlistRemove: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cartAdd: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cartRemove: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cartUpdate: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        basketSubtotal: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewCart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        fileUploadSuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        checkoutTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        checkoutDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        continueShopping: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewOrder: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        viewOrders: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        selectAccountType: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        otpSentSuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        somethingWentWrong: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        invalidOTP: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        welcomeBack: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        loggingOut: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        logoutSuccess: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        newsLetterSubscribedTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        newsLetterSubscribedDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        newsLetterSubscribedButton: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enquirySubmitTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enquirySubmitDescription: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enquirySubmitButton: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    login: {
        welcomeTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enterPhoneNumber: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        pleaseWait: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        verifyTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        verifyTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        confirmationCode: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        didntReceiveCode: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sendAgain: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        verify: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        verifying: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        signup: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        signupTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        accountTxt: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        signupToYateem: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        continueAsGuest: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        haveInsurance: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        yes: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        no: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    search: {
        search: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        result: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        noResult: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        popular: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        topCategory: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    breadCrump:{
        brands: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        home: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        products: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        insurance: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeLocator: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myAccount: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        myCart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        contactUs: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        aboutUs: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        privacyPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        termsAndCondition: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shippingPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        returnPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cookiePolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        refundPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        allProducts: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        blogs: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        category: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    policy:{
        privacyPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        termsAndCondition: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        shippingPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        returnPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cookiePolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        refundPolicy: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    homeTry: {
        comingSoonTitle: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        comingSoonText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        comingSoonBtn: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },
    compare: {
        compareBtn: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        removeAll: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        title: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addProduct: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sku: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        price: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        rating: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        color: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        sizesAvailable: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        addToCart: {
            en: { type: String},
            ar: { type: String }
        },
    },
    other: {
        home: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        category: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        account: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        cart: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        share: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        ourBrands: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeLocator: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        storeLocatorText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        nothingMoreToLoad: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        loading: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        enquireWithUs: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        availableStores: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        download: {
            en: { type: String, required: true },
            ar: { type: String }
        },
    },

    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("text", textSchema)