const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require('../services/prescription.service')
const customerService = require('../services/customer.service');
const db = require('../models/index');
const { uploadWebp } = require('../util/uploadWebp');

exports.create = async (req, res) => {
    try {
        let { body } = req;
        let { language } = req.headers
        language = language ?? "en"
        const { customerId } = res?.locals?.user
        const customerDetails = await customerService.findOne({ refid: customerId, isDelete: false });
        const translation = await db.Texts.findOne({})
        body.customer = customerDetails?._id;
        body.refid = await service.count({}) + 1
        if (req?.file){
            const name = `prescriptions/${body?.refid}-${req?.file?.originalname}`
            const {key} = await uploadWebp(req?.file?.path, name, req?.file?.mimetype)
            body.file = key;
        };
        const response = await service.create(body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, Error, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message
            });
        }
        return helper.deliverResponse(res, 200, {}, {
            "error_code": messages.PRESCRIPTIONS.CREATED.error_code,
            "error_message": translation?.myAccount?.prescriptionCreateSuccess[language] ?? messages.PRESCRIPTIONS.CREATED.error_message
        });
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getPrescriptions = async (req, res) => {
    try {
        const { customerId } = res?.locals?.user
        const customerDetails = await customerService.findOne({ refid: customerId, isDelete: false });
        let projection = { isActive: 0, __v: 0, isDelete: 0, updatedAt: 0 };
        const response = await service.find({ customer: customerDetails?._id, isDelete: false }, projection);
        let data = [];
        for (let item of response) {
            data.push({
                ...item?._doc,
                file: process.env.DOMAIN + item?.file,
            })
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        const { refid } = req.params;
        let { language } = req.headers
        language = language ?? "en"
        const response = await service.update({ refid: refid, isDelete: false }, { isDelete: true });
        const translation = await db.Texts.findOne({})
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, Error, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message
            });
            return;
        }
        return helper.deliverResponse(res, 200, {}, {
            "error_code": messages.PRESCRIPTIONS.DELETED.error_code,
            "error_message": translation?.myAccount?.prescriptionCreateSuccess[language] ?? messages.PRESCRIPTIONS.DELETED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}