const express = require('express')
const router = express.Router()
const controller = require('../../controllers/insurance.controller')
const upload = require('../../util/upload')

module.exports = () => {

    router.post('/insurance-form', upload.single('file'), controller.addEnquiry)

    router.get('/insurance-providers', controller.activeProviders)
    router.get('/insurance-content', controller.getContent)

    return router;
}