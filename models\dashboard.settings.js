const mongoose = require('mongoose')

const dashboardSettingsSchema = new mongoose.Schema({
    items: [{
        type: { type: String, required: true },
        isActive : { type: Boolean, default: true },
        mainTitle: {
            en: { type: String },
            ar: { type: String },
        },
        keyword: [{
            id: { type: String },
            title: {
                en: { type: String },
                ar: { type: String }
            },
            keyword: { type: String },
        }],
        items: [{
            title: {
                en: { type: String },
                ar: { type: String },
            },
            description: {
                en: { type: String },
                ar: { type: String },
            },
            buttonText: {
                en: { type: String },
                ar: { type: String },
            },
            type: { type: String },
            srcType: { type: String, default: "file" },
            src: { type: String },
            mobileSrc: { type: String },
            link: { type: String },
            image: { type: String },
            mobileImage: { type: String },
            video: { type: String },
            mobileVideo: { type: String },
            subImage: { type: String },
            isActive: { type: <PERSON>olean, default: true}
        }],
    }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })


module.exports = mongoose.model('dashboard.settings', dashboardSettingsSchema)