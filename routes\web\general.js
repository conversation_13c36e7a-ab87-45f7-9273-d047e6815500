const express = require('express')
const router = express.Router()
const Aboutontroller = require('../../controllers/about.controller')
const termsController = require('../../controllers/terms.controller')
const privacyController = require('../../controllers/privacy.policy.controller')
const cookieController = require('../../controllers/cookie.policy.controller')
const headerController = require('../../controllers/header.menu.controller')
const footerController = require('../../controllers/footer.controller')
const mediaController = require('../../controllers/image.controller');
const returnController = require('../../controllers/return.policy.controller')
const refundController = require('../../controllers/refund.policy.controller')
const shippingController = require('../../controllers/shipping.controller')
const textController = require('../../controllers/text.controller')
const upload = require('../../util/upload')
const metaTagController = require('../../controllers/meta.tag.controller')
const multiStoreController = require('../../controllers/multiStore.contorller')
const controller = require('../../controllers/general.settings.controller');

module.exports = () => {
    router.get('/about-us', Aboutontroller.getAbouts)
    router.get('/terms-and-conditions', termsController.getTerms)
    router.get('/privacy-policy', privacyController.getPrivacyPolicy)
    router.get('/cookie-policy', cookieController.getCookiePolicy)
    router.get('/return-policy', returnController.getReturnPolicy)
    router.get('/refund-policy', refundController.getRefundPolicy)
    router.get('/shipping-policy', shippingController.getShippingPolicy)

    router.get('/header', headerController.headers)

    router.get('/footer', footerController.footer)
    router.get('/meta-tags', metaTagController.metaTags)

    router.get('/translation', textController.getTextsContent)

    router.post('/media-upload', upload.single('file'), mediaController.uploadFile);

    router.get("/multi-stores", multiStoreController.listWeb)
    router.get("/get-locales", multiStoreController.getLocales)

    return router;
}