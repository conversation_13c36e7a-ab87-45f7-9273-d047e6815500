const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/dashboard.settings.service");
const bannerService = require("../services/banner.service");
const axios = require("axios");
const collectionService = require("../services/collection.service");

const db = require('../models/index');
const mongoose = require('mongoose')

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid? storeid: "ae"
    try {
        let { body } = req;
        body.refid = (await service.count({})) + 1;
        body.storeId = storeid;
        const result = await service.create(body);
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            });
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(
            res,
            200,
            result,
            {},
            {
                message: messages.SUCCESS_RESPONSE.message,
                error_code: messages.SUCCESS_RESPONSE.error_code,
            }
        );
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    console.log(storeid)
    try {
        const result = await service.find({ storeId: storeid });
        if (result instanceof Error) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
            return;
        }
        let items = [];
        for (let data of result) {
            for (let item of data.items) {
                items.push({
                    id: item.id,
                    type: item.type,
                    isActive: item.isActive,
                    image: item.image,
                    keyword: item.keyword,
                    mainTitle: item?.mainTitle,
                    items: item.items,
                });
            }
            data.items = items;
        }
        console.log(result)
        helper.deliverResponse(res, 200, result, {
            message: messages.SUCCESS_RESPONSE.message,
            error_code: messages.SUCCESS_RESPONSE.error_code,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.get = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        let result = await service.findOne({ storeId: storeid });
        if (result instanceof Error) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        }
        let items = [];
        // for (let data of result) {  
        for (let item of result.items) {
            if (item.isActive) {
                let innerItems = []
                for(let innerItem of item?.items){
                    if(innerItem?.isActive == false) continue;
                    innerItems.push(
                        {
                            ...innerItem?._doc,
                            title: innerItem.title?.[language],
                            description: innerItem.description?.[language],
                            buttonText: innerItem.buttonText?.[language],
                            image: innerItem.image ? process.env.DOMAIN + innerItem.image : null,
                            mobileImage: innerItem.mobileImage ? process.env.DOMAIN + innerItem.mobileImage : null,
                            subImage: innerItem.subImage ? process.env.DOMAIN + innerItem?.subImage : null,
                            video: innerItem.video ? process.env.DOMAIN + innerItem?.video : null,
                            mobileVideo: innerItem.mobileVideo ? process.env.DOMAIN + innerItem?.mobileVideo : null,
                            isActive: innerItem?.isActive,
                            link: innerItem?.link
                        }
                    )
                }
                items.push({
                    id: item.id,
                    type: item.type,
                    mainTitle: item?.mainTitle?.[language],
                    image: item.image,
                    keyword: item?.keyword?.map(k => {
                        return ({
                            ...k._doc,
                            title: k?.title?.[language]
                        })
                    }),
                    items: innerItems
                });
            }
        }
        result.items = items;
        // }
        const data = {
            ...result?._doc,
            items,
        }
        helper.deliverResponse(res, 200, [data], {
            message: messages.SUCCESS_RESPONSE.message,
            error_code: messages.SUCCESS_RESPONSE.error_code,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const result = await service.update({ refid: req?.params?.refid, storeId: storeid }, body);
        if (result instanceof Error) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addWidget = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const result = await service.addWidget({ refid: req?.params?.refid, storeId: storeid }, body);
        if (result instanceof Error) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "home" });
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.setBanner = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        let homeorder = await service.findOne({ storeId: storeid });

        const items = homeorder.items;
        let i = null
        let orderItem = items.find((item, index) => {
            if (item._id == body.widgetId) {
                i = index
                return true
            }
        });

        if (body?.type) {
            orderItem.type = body?.type;
        }

        if(body?.mainTitle){
            orderItem.mainTitle = body?.mainTitle
        }
        orderItem.items = {
            image: body?.image,
            title: {
                en: body?.title?.en ? body?.title?.en : null,
                ar: body?.title?.ar ? body?.title?.ar : null,
            },
            description: {
                en: body?.description?.en ? body?.description?.en : null,
                ar: body?.description?.ar ? body?.description?.ar : null,
            },
            buttonText: {
                en: body?.buttonText?.en ? body?.buttonText?.en : null,
                ar: body?.buttonText?.ar ? body?.buttonText?.ar : null,
            },
            link: body?.link ? body?.link : null,
            video: body?.video ? body?.video : null,
            mobileVideo: body?.mobileVideo ? body?.mobileVideo : null,
            subImage: body?.subImage ? body?.subImage : null,
        };

        homeorder.items[i] = orderItem

        // const result = await db.DashboardSettings.findOneAndUpdate({ refid: 1, "items._id": body.widgetId }, {
        //     $set: { "items.$.items": orderItem.items, "items.$.isActive": false  },
        // },{ new: true, upsert: false, useFindAndModify: false }
        // )

        const result = await service.update({ storeId: storeid }, items);
        if (result instanceof Error) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "home" });
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.error(error);
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.setSingleCollection = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const collection = await collectionService.findOne({ _id: body?.collection, isDelete: false, storeId: storeid });
        const homeorder = await service.findOne({ storeId: storeid });

        const items = homeorder.items;

        const orderItem = items.find((item) => item._id == body.widgetId);

        if (!Array.isArray(orderItem.keyword) || orderItem.keyword.length === 0) {
            orderItem.keyword = [{ id: "", title: "", keyword: "" }];
        }

        orderItem.keyword[0].id = collection._id;
        orderItem.keyword[0].title = collection.title?.en;
        orderItem.keyword[0].keyword = collection.slug;

        orderItem.items = [{title: body?.title}]

        const result = await service.update({ storeId: storeid }, items);
        if (result instanceof Error) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "home" });
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.setMultipleCollection = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const homeorder = await service.findOne({ storeId: storeid });
        const items = homeorder.items;
        const orderItem = items.find((item) => item._id == body.widgetId);
        let keywords = [];
        for (let collection of body.collections) {
            const collectionData = await collectionService.findOne({ refid: collection, isDelete: false, isActive: true, storeId: storeid });
            keywords.push({
                id: collectionData.refid,
                title: collectionData.title,
                keyword: collectionData.slug,
            });
        }
        orderItem.keyword = keywords;
        orderItem.items = [{title: body?.title}]
        const result = await service.update({ storeId: storeid }, items);
        if (result instanceof Error) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
            return;
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, 200, result, {
            error_code: messages.HOME_UPDATED.error_code,
            error_message: messages.HOME_UPDATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getSelectedCollection = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const homeorder = await service.findOne({ storeId: storeid });
        const result = homeorder.items.find((item) => item.type == "collectionsMultiple");
        const data = result?.keyword ? result?.keyword : [];
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.setSlider = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;

        const homeorder = await service.findOne({ storeId: storeid });

        const items = homeorder.items;

        const orderItem = items.find((item) => item._id == body.widgetId);

        if (body?.type) {
            orderItem.type = body?.type;
        }

        let banners = [];

        if (body?.banners) {
            for (let banner of body?.banners) {
                banners.push({
                    image: banner,
                });
            }
        } else {
            for (let slider of body?.sliders) {
                banners.push({
                    image: slider?.image ? slider?.image : null,
                    mobileImage: slider?.image ? slider?.mobileImage : null,
                    type: slider?.type ? slider?.type : null,
                    srcType: slider?.srcType ? slider?.srcType : null,
                    src: slider?.src ? slider?.src : null,
                    mobileSrc: slider?.mobileSrc ? slider?.mobileSrc : null,
                    mobileVideo: slider?.mobileVideo ? slider?.mobileVideo : null,
                    video: slider?.video ? slider?.video : null,
                    title: {
                        en: slider?.title?.en ? slider?.title?.en : null,
                        ar: slider?.title?.ar ? slider?.title?.ar : null,
                    },
                    description: {
                        en: slider?.description ? slider?.description : null,
                        ar: slider?.descriptionAr ? slider?.descriptionAr : null,
                    },
                    subImage: slider?.subImage ? slider?.subImage : null,
                    buttonText: slider?.buttonText ? slider?.buttonText : null,
                    link: slider?.link ? slider?.link : null,
                    isActive: slider?.isActive == false ? false: true
                });
            }
        }

        orderItem.items = banners;

        orderItem.mainTitle = body?.mainTitle

        const result = await service.update({ storeId: storeid }, items);
        if (result instanceof Error) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
            return;
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, 200, result, {
            error_code: messages.HOME_UPDATED.error_code,
            error_message: messages.HOME_UPDATED.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.widgetStatus = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const homeOrder = await service.findOne({ isDelete: false, storeId: storeid });
        const selectedWiget = homeOrder.items.find((item) => item._id == body.widgetId);
        if (selectedWiget) {
            selectedWiget.isActive = body.isActive;
            const result = await service.update({ isDelete: false, storeId: storeid }, homeOrder.items);
            if (result instanceof Error) {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    }
                );
                return;
            }
            helper.deliverResponse(res, 200, result, {
                error_code: messages.HOME_UPDATED.error_code,
                error_message: messages.HOME_UPDATED.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};
