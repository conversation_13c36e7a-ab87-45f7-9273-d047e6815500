const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Category(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.createSubCategory = async (data) => {
    try {
        let response = new db.SubCategory(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}, limit) => {
    try {
        let response = await db.Category.find(query, projection).sort({ createdAt: -1 }).limit(limit)
        // .populate('parent', 'name ')
        return response
    } catch (error) {
        throw error;
    }
}

exports.findSub = async (query, projection = {}, limit) => {
    try {
        let response = await db.SubCategory.find(query, projection).sort({ createdAt: -1 }).limit(limit)
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Category.findOne(query, projection)
            .populate('parent', 'name')
            .populate('subCategory')
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOneSub = async (query, projection = {}) => {
    try {
        let response = await db.SubCategory.findOne(query, projection)
            .populate('name')
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Category.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.updateSub = async (query, data) => {
    try {
        let response = await db.SubCategory.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Category.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.SubCount = async (query) => {
    try {
        let response = await db.SubCategory.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}