const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/vm.policy.service");
const axios = require('axios');


exports.createVMPolicy = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        }
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.VM_POLICY.CREATED.error_code,
            "error_message": messages.VM_POLICY.CREATED.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getVMPolicy = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getVMPolicyContent = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const response = await service.findOne({ isDelete: false, storeId: storeid });
        let data
        if (language) {
            data = {
                    title: response?.title?.[language],
                    content: response?.content?.[language]
                }
        } else {
            data = response
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.updateVMPolicy = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const response = await service.update({ storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "vm-policy" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.VM_POLICY.UPDATED.error_code,
            "error_message": messages.VM_POLICY.UPDATED.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}