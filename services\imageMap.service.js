const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.ImageMap(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.ImageMap.find(query, projection)
        return response;
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.ImageMap.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.ImageMap.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.upsert = async (query, data) => {
    try {
        let response = await db.ImageMap.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: true,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.ImageMap.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}