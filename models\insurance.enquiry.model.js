const mongoose = require('mongoose');

const insuranceSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String, required: true },
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers" },
    countryCode: { type: String },
    mobile: { type: String, required: true },
    insurance: { type: mongoose.Schema.Types.ObjectId, ref: "insurance.provider" },
    nationality: { type: String, required: true },
    emirates: { type: String, required: true },
    emiratesId: { type: String, required: true },
    file: { type: String },
    memberId: { type: String },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    refid: { type: String, required: true },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('insurance', insuranceSchema)