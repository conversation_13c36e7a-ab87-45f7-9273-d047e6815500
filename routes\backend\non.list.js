const express = require('express');
const router = express.Router();
const controller = require('../../controllers/non.list.controller');

module.exports = () => {
    router.post('/non-list', controller.create)
    router.get('/non-list', controller.find)
    router.get('/non-list/:refid', controller.findOne)
    router.put('/non-list/:refid', controller.update)
    router.put('/delete-non-list/:refid', controller.delete)
    router.get('/product-enquiries', controller.getEnquiries)

    return router
}