const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
    name: { type: String, default: 'Guest' },
    image: { type: String },
    countryCode: { type: String, required: true },
    mobile: { type: String, required: true },
    email: { type: String },
    emirates: { type: String },
    isInsurance: { type: Boolean, },
    insuranceId: { type: String },
    file: { type: String },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    slug: { type: String },
    refid: { type: String, required: true },
    tokens: [{ type: String }],
    tryCart: [{
        product: { type: mongoose.Schema.Types.ObjectId, ref: 'products' },
        size: { type: mongoose.Schema.Types.ObjectId, ref: 'sizes' },
    }],
    wishlist: [{ type: mongoose.Schema.Types.ObjectId, ref: 'products' }],
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isGuest: { type: Boolean, default: true },
    loyaltyNumber: { type: String },
}, { timestamps: true });

module.exports = mongoose.model('customers', customerSchema);