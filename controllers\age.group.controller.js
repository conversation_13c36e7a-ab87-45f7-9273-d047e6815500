const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/age.group.service");
const adminService = require("../services/admin.users.service");
const productService = require("../services/products.service");
const generateUniqueNumber = require('../util/getRefid');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [

            ]
        }
    }
}

exports.create = async (req, res) => {
    try {
        let { body } = req;
        const { email } = res?.locals?.user
        const ageGroup = await service.findOne({ name: body?.name, isDelete: false })
        if (ageGroup) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": messages.AGE_GROUP.ALREADY_EXIST.error_code,
                "error_message": messages.AGE_GROUP.ALREADY_EXIST.error_message
            });
            return;
        }
        body.refid = generateUniqueNumber()
        const adminDetails = await adminService.findOne({ email: email, isDelete: false })
        body.createdBy = adminDetails?._id
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.AGE_GROUP.CREATED.error_code,
            "error_message": messages.AGE_GROUP.CREATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getAgeGroups = async (req, res) => {
    try {
        let { body } = req;
        let query = { isDelete: false };
        if (body?.isActive) query.isActive = body?.isActive
        const response = await service.find(query);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.ageGroupDetails = async (req, res) => {
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        let { body } = req;
        const ageGroup = await service.findOne({ name: body?.name, isDelete: false, refid: { $ne: body?.refid } });
        if (ageGroup) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": messages.AGE_GROUP.ALREADY_EXIST.error_code,
                "error_message": messages.AGE_GROUP.ALREADY_EXIST.error_message
            });
            return;
        }
        const response = await service.update({ refid: body?.refid, isDelete: false }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.AGE_GROUP.UPDATED.error_code,
            error_message: messages.AGE_GROUP.UPDATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        let { refid } = req.params;
        const ageGroup = await service.findOne({ refid: refid, isDelete: false });
        const products = await productService.find({ ageGroup: ageGroup?._id, isDelete: false })
        if (products?.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Age Group is used in products"
            })
            return;
        }
        const response = await service.update({ refid: refid, isDelete: false }, { isDelete: true });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.AGE_GROUP.DELETED.error_code,
            error_message: messages.AGE_GROUP.DELETED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}