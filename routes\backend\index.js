const express = require("express");
const router = express.Router();
const logger = require("morgan");
router.use(logger("dev"));

const authorize = require("../../middlewares/authorize");

const adminRoutes = require("./admin.users");
const termsRoutes = require("./terms");
const cookiePolicyRoutes = require("./cookie.policy");
const privacyPolicyRoutes = require("./privacy.policy");
const brandRoutes = require("./brands");
const categoryRoutes = require("./category");
const customerRoutes = require("./customers");
const aboutRoutes = require("./about");
const frameShapeRoutes = require("./frame.shapes");
const frameTypeRoutes = require("./frame.type");
const ageGroup = require("./age.group");
const reviewRoutes = require("./review");
const productRoutes = require("./products");
const productHeadRoutes = require("./product.head");
const attributeRoutes = require("./attribute");
const prescriptionRoutes = require("./prescription");
const storeRoutes = require("./stores");
const imageRoutes = require("./image");
const blogRoutes = require("./blogs");
const contactUsRoutes = require("./contactUs");
const collectionRoutes = require("./collection");
const insuranceRoutes = require("./insurance");
const faqRoutes = require("./faq");
const newsletterRoutes = require("./newsletter");
const dashboardSettings = require("./dashboard.settings");
const coupons = require("./coupons");
const headerMenu = require("./header.menu");
const generalSettings = require("./general.settings");
const coating = require("./coating");
const banners = require("./banner");
const subscribePlan = require("./subscribe.plan");
const trycart = require("./trycart");
const orders = require("./order");
const colors = require("./color");
const sizes = require("./size");
const lensPower = require("./lens.power");
const contactLens = require("./contactLens");
const lensBrand = require("./lens.brand");
const lensType = require("./lens.type");
const lensIndex = require("./lens.index");
const shipping = require("./shipping");
const returnPolicy = require("./return.policy");
const refundPolicy = require("./refund.policy");
const labels = require("./label");
const contactLensPower = require("./contactLens.power");
const footer = require("./footer");
const home = require("./home");
const nonList = require("./non.list");
const metaTags = require("./meta.tag");
const frontMaterialRoutes = require("./front.material");
const typeRoutes = require("./type");
const lensMaterialRoutes = require("./lens.material");
const paymentFeeRoutes = require("./paymentMethodFee");
const vmPolicy = require("./vmPolicy")
const texts = require("./text")
const contactSize = require("./contactSize")
const multiStore = require("./multiStore")

module.exports = () => {
    router.use((req, res, next) => {
        res._json = res.json;
        res.json = function json(obj) {
            obj.APIType = "App";
            obj.APIVersion = 1;
            res._json(obj);
        };
        next();
    });

    router.use("", adminRoutes());
    router.use("", termsRoutes());
    router.use("", cookiePolicyRoutes());
    router.use("", privacyPolicyRoutes());
    router.use("", brandRoutes());
    router.use("", categoryRoutes());
    router.use("", customerRoutes());
    router.use("", aboutRoutes());
    router.use("", frameShapeRoutes());
    router.use("", frameTypeRoutes());
    router.use("", ageGroup());
    router.use("", reviewRoutes());
    router.use("", productRoutes());
    router.use("", attributeRoutes());
    router.use("", prescriptionRoutes());
    router.use("", storeRoutes());
    router.use("", imageRoutes());
    router.use("", blogRoutes());
    router.use("", contactUsRoutes());
    router.use("", collectionRoutes());
    router.use("", insuranceRoutes());
    router.use("", faqRoutes());
    router.use("", newsletterRoutes());
    router.use("", dashboardSettings());
    router.use("", coupons());
    router.use("", headerMenu());
    router.use("", generalSettings());
    router.use("", coating());
    router.use("", banners());
    router.use("", subscribePlan());
    router.use("", trycart());
    router.use("", orders());
    router.use("", colors());
    router.use("", sizes());
    router.use("", lensPower());
    router.use("", contactLens());
    router.use("", lensBrand());
    router.use("", lensType());
    router.use("", lensIndex());
    router.use("", shipping());
    router.use("", returnPolicy());
    router.use("", refundPolicy());
    router.use("", labels());
    router.use("", contactLensPower());
    router.use("", footer());
    router.use("", home());
    router.use("", nonList());
    router.use("", metaTags());
    router.use("", paymentFeeRoutes());
    router.use("", vmPolicy());
    router.use("", authorize.verifyToken, frontMaterialRoutes());
    router.use("", authorize.verifyToken, typeRoutes());
    router.use("", authorize.verifyToken, lensMaterialRoutes());
    router.use("", texts());
    router.use("", contactSize());
    router.use("", multiStore());
    router.use("", productHeadRoutes());

    return router;
};
