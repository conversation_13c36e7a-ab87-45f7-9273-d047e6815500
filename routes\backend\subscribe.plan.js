const express = require('express')
const router = express.Router()
const controller = require('../../controllers/subscribe.plan.controller')
const subscriptionController = require('../../controllers/subscription.controller')

module.exports = () => {
    router.post('/create-plan', controller.create)
    router.get('/get-plans', controller.find)
    router.get('/get-plan/:refid', controller.findOne)
    router.put('/update-plan/:refid', controller.update)
    router.get('/active-plans', controller.activePlans)
    router.put('/delete-plan/:refid', controller.delete)

    router.get('/get-subscriptions', subscriptionController.find)

    return router
}