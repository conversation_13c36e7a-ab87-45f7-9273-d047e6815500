const express = require('express');
const router = express.Router();
const frameTypeController = require('../../controllers/frame.types.controller');
const frameShapeController = require('../../controllers/frame.shapes.controller');
const frontMaterialController = require('../../controllers/front.material.controller');
const typeController = require('../../controllers/type.controller');
const lensMaterialController = require('../../controllers/lens.material.controller');

module.exports = () => {
    router.get('/frame-types', frameTypeController.frameTypes);
    router.get('/frame-shapes', frameShapeController.frameShapes);
    router.get('/front-materials', frontMaterialController.frontMaterials);
    router.get('/lens-materials', lensMaterialController.lensMaterials);
    router.get('/types', typeController.types);

    return router;
}
