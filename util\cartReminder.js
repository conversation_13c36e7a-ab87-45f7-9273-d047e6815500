const cartService = require("../services/cart.service");
const emailHelper = require("../util/emailHelper");
const template = require("../util/templates");
const productService = require("../services/products.service");

exports.cartReminder = async () => {
    try {
        const twoDaysAgo = new Date();
        twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

        let query = {
            isPurchased: false,
            isDelete: false,
            isActive: true,
            "date.added": { $lt: twoDaysAgo },
        };

        const cartResponse = await cartService.find(query);

        for (let cart of cartResponse) {
            // let productDetail = await productService.findOne({_id: cart?.produts})
            if (cart?.products?.length > 0) {
                if (cart?.customer?.email) {
                    try {
                        const mailOptions = {
                            from: process.env.EMAIL_USER,
                            to: cart?.customer?.email,
                            subject: "Your cart has items waiting for you!",
                            html: await template.abandonedCart(cart),
                            attachments: [
                                {
                                    filename: "logo-light-full.png",
                                    path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                    cid: "logo", // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                                },
                                {
                                    filename: "reminder.png",
                                    path: __dirname + "/../public/reminder.png", // path contains the filename, do not just give path of folder where images are reciding.
                                    cid: "reminder", // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                                },
                            ],
                        };

                        emailHelper.sendMail(mailOptions, (error, info) => {
                            if (error) {
                                console.error(`Failed to send cart reminder email to: ${cart?.customer?.email}`, error);
                            } else {
                            }
                        });
                    } catch (emailError) {
                        console.error(`Failed to send cart reminder email to: ${cart?.customer?.email}`, emailError);
                    }
                } else {
                }
            }
        }
    } catch (error) {
        console.error("Error in cartReminder function:", error);
    }
};
