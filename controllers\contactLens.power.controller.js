const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const service = require("../services/contactLens.power.service");
const cartService = require("../services/cart.service");
const orderService = require("../services/order.service");
const generateUniqueNumber = require("../util/getRefid");

exports.create = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        body.refid = generateUniqueNumber();
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CONTACT_LENS.POWER_CREATED.error_code,
            error_message: messages.CONTACT_LENS.POWER_CREATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.list = async (req, res) => {
    try {
        const { type } = req?.params;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ isDelete: false, type: type, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.details = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({
            refid: req?.params?.refid,
            isDelete: false,
            storeId: storeid
        });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.update = async (req, res) => {
    try {
        const { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.update(
            { refid: req?.params?.refid, isDelete: false, storeId: storeid }, 
            body
        );
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CONTACT_LENS.POWER_UPDATED.error_code,
            error_message: messages.CONTACT_LENS.POWER_UPDATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.powers = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const projection = { createdAt: 0, updatedAt: 0, __v: 0 };
        const sphResponse = await service.find({ isDelete: false, isActive: true, type: "Sph", storeId: storeid }, projection);
        const cylResponse = await service.find({ isDelete: false, isActive: true, type: "Cyl", storeId: storeid }, projection);
        const axisResponse = await service.find({ isDelete: false, isActive: true, type: "Axis", storeId: storeid }, projection);

        const response = {
            sph: sphResponse ? sphResponse : [],
            cyl: cylResponse ? cylResponse : [],
            axis: axisResponse ? axisResponse : [],
        };
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.delete = async (req, res) => {
    try {
        const { refid } = req?.params;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const powerDetails = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        const cartWithPower = await cartService.findOne({
            $or: [
                { 'products.contactLens.sphLeft': powerDetails?._id },
                { 'products.contactLens.sphRight': powerDetails?._id },
                { 'products.contactLens.cylLeft': powerDetails?._id },
                { 'products.contactLens.cylRight': powerDetails?._id },
                { 'products.contactLens.axisLeft': powerDetails?._id },
                { 'products.contactLens.axisRight': powerDetails?._id }
            ],
            isDelete: false,
            isActive: true,
            isPurchased: false,
            storeId: storeid
        });

        const orderWithPower = await orderService.findOne({
            $or: [
                { 'products.contactLens.sphLeft': powerDetails?._id },
                { 'products.contactLens.sphRight': powerDetails?._id },
                { 'products.contactLens.cylLeft': powerDetails?._id },
                { 'products.contactLens.cylRight': powerDetails?._id },
                { 'products.contactLens.axisLeft': powerDetails?._id },
                { 'products.contactLens.axisRight': powerDetails?._id }
            ],
            isDelete: false,
            isActive: true,
            storeId: storeid
        });
        if (cartWithPower || orderWithPower) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: 'This contact lens power is currently in use and cannot be deleted.'
                }
            );
        }

        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, {
            error_code: 0,
            error_message: 'Contact lens power deleted successfully',
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};
