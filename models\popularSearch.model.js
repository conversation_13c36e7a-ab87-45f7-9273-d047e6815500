const mongoose = require('mongoose')

const popularSearchSchema = new mongoose.Schema({
    keywords: [{
        key: { type: String },
        count: { type: Number }
    }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('popularSearch', popularSearchSchema)