const express = require('express');
const router = express.Router();
const controller = require('../../controllers/subscription.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/subscribe', authorize.verifyToken, controller.subscribe);
    router.post('/subscribe-summary', authorize.verifyToken, controller.subscribeSummary);
    router.get('/my-subscriptions', authorize.verifyToken, controller.mySubscriptions);
    router.post('/unsubscribe/:id', authorize.verifyToken, controller.unsubscribe);

    return router;
}