const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/reviews.service");
const adminService = require("../services/admin.users.service");
const customerService = require("../services/customer.service");
const productService = require("../services/products.service");
const orderService = require("../services/order.service");

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('name', 'Name is required').exists(),
            ]
        }
    }
}

exports.create = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { email } = res?.locals?.user
        const adminDetails = await adminService.findOne({ email: email, isDelete: false })
        body.refid = await service.count({}) + 1
        body.createdBy = adminDetails?._id;
        body.storeId = storeid;
        const response = await service.create(body);
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.REVIEWS.CREATED.error_code,
            "error_message": messages.REVIEWS.CREATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getReviews = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.reviewDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const { refid } = req?.params
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.REVIEWS.UPDATED.error_code,
            error_message: messages.REVIEWS.UPDATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.addReview = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { customerId } = res?.locals?.user
        const customerDetails = await customerService.findOne({ refid: customerId, isDelete: false })
        if (!customerDetails) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.CUSTOMER_NOT_FOUND.error_code,
                error_message: messages.CUSTOMER_NOT_FOUND.error_message
            });
        }
        let { body } = req;
        const productDetails = await productService.findOne({ _id: body?.productId, isDelete: false, storeId: storeid })
        body.refid = await service.count({}) + 1
        body.customer = customerDetails?._id
        body.product = productDetails?._id
        body.storeId = storeid;
        const response = await service.create(body);
        const avgRating = await service.aggregate([
            { $match: { product: productDetails?._id, storeId: storeid } },
            { $group: { _id: "$product", avgRating: { $avg: "$rating" } } }
        ]);
        await productService.update({ _id: productDetails?._id, storeId: storeid }, { rating: avgRating[0]?.avgRating })
        if (body?.orderNo) {
            const orderDetails = await orderService.findOne({ orderNo: body?.orderNo, isDelete: false, storeid: storeid })
            orderDetails?.products?.find((product) => {
                if (String(product.product) == String(body?.productId)) {
                    product.isReviewed = true
                }
            })
            await orderService.update({ _id: orderDetails?._id, storeId: storeid }, { products: orderDetails?.products })
        }
        if (response) {
            return helper.deliverResponse(res, 200, response, {
                error_code: messages.REVIEWS.CREATED.error_code,
                error_message: messages.REVIEWS.CREATED.error_message
            });
        } else {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.REVIEWS.FAILED.error_code,
                error_message: messages.REVIEWS.FAILED.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true })
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.REVIEWS.DELETED.error_code,
            error_message: messages.REVIEWS.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}