const mongoose = require('mongoose');

const aboutSchema = new mongoose.Schema({
    refid: { type: String, required: true },
    sectionOne: {
        title: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        image: { type: String, required: true },
    },
    sectionTwo: [
        {
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            count: { type: String, required: true },
            symbol: { type: String, default: "" }
        }
    ],
    sectionThree: {
        title: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        timeline: [{
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            description: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            image: { type: String, required: true },
            year: { type: String, required: true }
        }]
    },
    sectionFour: {
        title: {
            en: { type: String },
            ar: { type: String, }
        },
        data: [{
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            description: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            image: { type: String, required: true },
        }]
    },
    seoDetails: {
        title: {
            en: { type: String},
            ar: { type: String }
        },
        description: {
            en: { type: String},
            ar: { type: String }
        },
        keywords: {
            en: { type: String},
            ar: { type: String },
        },
        canonical: {
            en: { type: String},
            ar: { type: String },
        },
        ogImage: { type: String },
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('abouts', aboutSchema);