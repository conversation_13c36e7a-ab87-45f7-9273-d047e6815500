const db = require('../models/index')

exports.create = async (data) => {
    try {
        let count = await db.LensEnquiries.find({}).countDocuments()
        data.refid = count + 1;
        let response = new db.LensEnquiries(data)
        await response.save()
        const result = await db.LensEnquiries.findById(response._id).populate("product", "name")
            .populate("brand", "name")
            .populate("prescription.leftSph", "name")
            .populate("prescription.rightSph", "name")
            .populate("prescription.leftCyl", "name")
            .populate("prescription.rightCyl", "name")
            .populate("prescription.leftAxis", "name")
            .populate("prescription.rightAxis", "name")
            .populate("prescription.leftAdd", "name")
            .populate("prescription.rightAdd", "name")
        return result
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.LensEnquiries.find(query, projection).sort({ createdAt: -1 })
            .populate('customer', 'name mobile')
            .populate('product', 'name')
            .populate('prescription.leftSph', 'name')
            .populate('prescription.leftCyl', 'name')
            .populate('prescription.leftAxis', 'name')
            .populate('prescription.rightSph', 'name')
            .populate('prescription.rightCyl', 'name')
            .populate('prescription.rightAxis', 'name')
            .populate('brand', 'name')
            .populate('index', 'name')
            .populate('coating', 'name')
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.LensEnquiries.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.LensEnquiries.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.LensEnquiries.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.aggregate = async (query) => {
    try {
        let response = await db.LensEnquiries.aggregate(query)
        return response;
    } catch (error) {
        throw error;
    }
};
