const messages = require('../config/constants/messages');
const helper = require('../util/responseHelper');
const service = require('../services/lens.type.service');
const enquiryService = require('../services/lens.enquiries.service');
const generateUniqueNumber = require('../util/getRefid');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_TYPE.CREATED.error_code,
                error_message: messages.LENS_TYPE.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.brandTypes = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const { brand } = req?.params
        const types = await service.find({ isDelete: false, brand: brand, storeId: storeid });
        const response = { types: types?.map((item=> ({...item._doc, name: item.name[language]}))) }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.detail = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_TYPE.UPDATED.error_code,
                error_message: messages.LENS_TYPE.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const {refid} = req?.params
        const detail = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        const enquiries = await enquiryService.find({ lensType: detail?.name, isDelete: false, storeId: storeid });
        if (enquiries.length > 0) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Cannot delete lens type as it is in used in Products'
            });
        }
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_TYPE.DELETED.error_code,
                error_message: messages.LENS_TYPE.DELETED.error_message
            });
        }
    } catch (error) { 
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}