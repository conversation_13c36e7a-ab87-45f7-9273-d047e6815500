const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Blogs(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.Blogs.find(query, projection).sort({ createdAt: -1 })
            .populate('createdBy', 'name')
        return response;
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Blogs.findOne(query, projection)
            .populate('createdBy', 'name')
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Blogs.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Blogs.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.pagination = async (query, page, limit, projection = {}) => {
    try {
        let response = await db.Blogs.find(query, projection).limit(limit * 1).skip((page - 1) * limit).sort({ createdAt: -1 })
        return response;
    } catch (error) {
        throw error;
    }
}