const express = require('express')
const router = express.Router()
const controller = require('../../controllers/banner.controller')
const upload = require('../../util/upload')

module.exports = () => {
    router.post('/add-banner', upload.array('image', 20), controller.addBanner)
    router.get('/get-banners', controller.list)
    router.get('/banner-details/:refid', controller.detail)
    router.put('/update-banner/:refid', upload.array('image', 20), controller.update)

    return router
}