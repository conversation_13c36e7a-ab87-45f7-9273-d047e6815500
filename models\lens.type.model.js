const mongoose = require("mongoose");

const lensTypeSchema = new mongoose.Schema({
    brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand", required: true },
    name: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    price: { type: Number, required: true },
    currency: { type: String, default: "AED" },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("lens.type", lensTypeSchema)