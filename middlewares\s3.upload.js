// middlewares/media-uploader.js
// const multer = require("multer");
// const multerS3 = require('multer-s3');
// const { s3, bucketName } = require('../../config/aws/aws.config');
// const mediaService = require("../services/media.service");

// const uploadToS3 = multer({
//   storage: multerS3({
//     s3: s3,
//     bucket: bucketName,
//     contentType: multerS3.AUTO_CONTENT_TYPE, 
//     // acl: 'public-read',
//     key: async function (req, file, cb) {
//       const mediaDetails = await mediaService.findOne({ title: file.originalname });
//       const filename = mediaDetails ? `${Date.now()}_${file.originalname}` : file.originalname;
//       cb(null, filename);
//     }
//   }),
//   limits: {
//     fileSize: 1024 * 1024 * 50, // 50MB
//   },
// });

// module.exports = uploadToS3;


const multer = require("multer");
const multerS3 = require('multer-s3');
const sharp = require('sharp');
const { s3, bucketName } = require('../config/s3');

const fileFilter = (req, file, cb) => {

  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload only images.'), false);
  }
};
const multerToS3 = multer({
  fileFilter,
  storage: multerS3({
    s3: s3,
    bucket: bucketName,
    acl: "public-read",
    contentType: function(req, file, cb) {
      cb(null, 'image/jpeg');
    },
    key: async function (req, file, cb) {
      const filename = `${Date.now()}_${file.originalname?.replaceAll(" ","-")}`;
      const extension = 'webp';
      cb(null, `images/${filename.split('.')[0]}.${extension}`);
    },
    shouldTransform: true,
    transforms: [{
      id: 'original',
      key: function (req, file, cb) {
        const filename = `${Date.now()}_${file.originalname}`
        const extension = 'webp';
        cb(null, `${filename.split('.')[0]}.${extension}`);
      },
      transform: function (req, file, cb) {
        cb(null, sharp().webp({ quality: 80 }));
      }
    }]
  }),
  limits: {
    fileSize: 1024 * 1024 * 50, // 50MB
  },
});

module.exports = multerToS3;