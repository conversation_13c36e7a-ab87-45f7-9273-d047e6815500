const express = require('express')
const router = express.Router()
const authorize = require('../../middlewares/authorize')
const controller = require('../../controllers/cart.controller')

module.exports = () => {
    router.post('/add-to-cart', controller.validate('addToCart'), controller.addCart)
    router.get('/cart', controller.getCart)
    router.post('/remove-from-cart', controller.removeFromCart)
    router.post('/update-cart', controller.updateCart)
    router.get('/coupons', controller.getCoupons)
    router.post('/apply-coupon', authorize.verifyToken, controller.applyCoupon)
    router.post('/remove-coupon', authorize.verifyToken, controller.removeCoupon)
    router.post('/buy-with-lens', controller.validate('buyWithLens'), controller.buyWithLens)
    router.post('/contact-lens-cart', controller.contactLensCart)
    router.post('/shipping-charge', controller.getShippingCharge)
    router.post('/cart-update', controller.addToCart)
    router.get('/loyalty-points', authorize.verifyToken, controller.loyaltyPoints)
    router.post('/redeem-loyalty', authorize.verifyToken, controller.redeemLoyalty)
    router.post('/remove-loyalty', authorize.verifyToken, controller.removeLoyalty)

    return router
}