const express = require('express')
const router = express.Router()
const controller = require('../../controllers/store.controller')
const authorize = require('../../middlewares/authorize')
const upload = require('../../util/upload')

module.exports = () => {
    router.post('/create-store', authorize.verifyToken, upload.single('image'), controller.create)
    router.get('/get-stores', controller.getStores)
    router.get('/get-store/:refid', controller.storeDetails)
    router.get('/get-store-filters', controller.getStoresFilters)
    router.post('/update-store', authorize.verifyToken, upload.single('image'), controller.update)
    router.put('/delete-store/:refid', controller.delete)

    router.post('/add-store-cms', controller.createStoreCms)
    router.get('/get-store-cms', controller.getStoreCms)
    router.put('/update-store-cms', controller.updateStoreCms)

    return router;
}