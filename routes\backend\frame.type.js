const express = require('express');
const router = express.Router();
const controller = require('../../controllers/frame.types.controller');
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-frame-type', upload.single('image'), controller.create);
    router.get('/get-frame-types', controller.getFrameTypes);
    router.get('/frame-type-details/:refid', controller.frameTypeDetails);
    router.post('/update-frame-type', upload.single('image'), controller.update);
    router.get('/active-frameTypes', controller.activeFrameTypes);
    router.put('/delete-frame-type/:refid', controller.delete);


    return router;
}
