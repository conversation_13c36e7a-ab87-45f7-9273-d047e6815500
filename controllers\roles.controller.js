const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const service = require("../services/roles.service");
const adminService = require("../services/admin.users.service")

exports.create = async (req, res) => {
    try {
        const { body } = req;
        body.refid = await service.count({}) + 1;

        const existingRole = await service.findOne({ permissions: body.permissions, isDelete: false });
        if (existingRole) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Role with the same permissions already exists',
            });
        }

        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.ROLES.CREATED.error_code,
            error_message: messages.ROLES.CREATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.find = async (req, res) => {
    try {
        let { body } = req;
        let query = { isDelete: false };
        if (body?.isActive) query.isActive = body?.isActive
        const response = await service.find(query);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.findOne = async (req, res) => {
    try {
        const { refid } = req.params;
        const response = await service.findOne({ isDelete: false, refid: refid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        ("error :: ", error);
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.update = async (req, res) => {
    try {
        const { body } = req;
        const { refid } = req.params;

        const existingRole = await service.findOne({ permissions: body.permissions, isDelete: false, refid: { $ne: refid } });
        if (existingRole) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Role with the same permissions already exists',
            });
        }

        const response = await service.update({ refid: refid }, body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.ROLES.UPDATED.error_code,
            error_message: messages.ROLES.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.delete = async (req, res) => {
    try {
        const { refid } = req.params;
        const role = await service.findOne({ refid: refid, isDelete: false });
        const admins = await adminService.find({ role: role?._id, isDelete: false });
        if(admins?.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Cannot delete role as it is in used in Admins'
            });
            return;
        }
        await service.update({ refid: refid }, { isDelete: true });
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.ROLES.DELETED.error_code,
            error_message: messages.ROLES.DELETED.error_message,
        });
    } catch(error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}