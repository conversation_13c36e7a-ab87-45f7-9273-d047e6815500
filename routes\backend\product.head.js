const express = require('express');
const router = express.Router();
const controller = require('../../controllers/product.head.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/product-head', authorize.verifyToken, controller.create);
    router.post('/get-product-head', controller.find);
    router.post('/variant-exists', controller.checkProductAssociation);
    router.get('/product-head/:refid', controller.findOne);
    router.put('/product-head/:refid', authorize.verifyToken, controller.update);
    router.put('/delete-product-head/:refid', authorize.verifyToken, controller.delete);

    return router;
}
