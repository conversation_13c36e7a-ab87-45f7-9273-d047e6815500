const express = require('express');
const router = express.Router();
const controller = require('../../controllers/contactUs.controller');
const upload = require('../../util/upload')

module.exports = () => {
    router.get('/get-contact-us', controller.getContactUs);
    router.post('/add-contact-banner', upload.fields([
        { name: 'image', maxCount: 1 },
        { name: 'ogImage', maxCount: 1 }
    ]), controller.addBanner);
    router.get('/get-contact-banner', controller.getBanner);
    router.post('/update-contact-banner', upload.fields([
        { name: 'image', maxCount: 1 },
        { name: 'ogImage', maxCount: 1 }
    ]), controller.updateBanner);

    return router;
}