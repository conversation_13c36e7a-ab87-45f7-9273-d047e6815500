const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/order.service");
const { body, validationResult } = require("express-validator");
const customerService = require("../services/customer.service");
const cartService = require("../services/cart.service");
const paymentFeeService = require("../services/paymentMethodFee.service");
const productService = require("../services/products.service");
const addressService = require("../services/address.service");
const tryCartService = require("../services/trycart.service");
const axios = require("axios");
const sizeService = require("../services/size.service");
const contactSizeService = require("../services/contact.size.service");
const jwt = require("jsonwebtoken");
const { KEYS } = require("../config/constants/key");
const template = require("../util/templates");
const emailHelper = require("../util/emailHelper");
const summaryService = require("../services/subscribe.summary.service");
const subscriptionService = require("../services/subscription.service");
const subscribePlanService = require("../services/subscribe.plan.service");
const { NOONAPI } = require("../config/constants/noon");
const paymentUrl = NOONAPI;
const paymentKey = process.env.PAYMENT_KEY;
const moment = require("moment");
const orderService = require("../services/order.service");
const lensPowerService = require("../services/lens.power.service");
const db = require('../models/index');
const { CRM_USERNAME, CRM_TOKEN, CRM_ORG_ID } = require("../config/constants/crm");
const { formatDate } = require("../util/timFormator");
const { TAMARA_API, TAMARA_API_TOKEN, TAMARA_NOTIFICATION_TOKEN } = require("../config/constants/tamara");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const generalSettingService = require("../services/general.settings.service");

exports.validate = (method) => {
    switch (method) {
        case "placeOrder": {
            return [
                body("paymentMethod", "Payment Method is required").exists(),
                body("cart", "Cart is required").exists()
            ];
        }
    }
};

function isLensPriceable(lensData) {
    if (lensData?.name != "0.00" && lensData?.name != "None" && lensData?.name != "none" && lensData?.name != "NONE") {
        return true;
    }
}

exports.getAllOrders = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { page, limit, sort } = req.query;

        page = page ?? 0
        limit = limit ?? 0

        const { body } = req;
        const filters = body?.filters ?? {};
        let query = { isTryCart: false, isDelete: false, storeId: storeid, ...filters };
        let customerQuery = {};

        if (body?.fromDate && !body?.toDate) query.orderDate = { $gte: new Date(body.fromDate) };
        if (body?.toDate && !body?.fromDate) query.orderDate = { $lte: new Date(body.toDate) };
        if (body?.fromDate && body?.toDate)
            query.orderDate = { $gte: new Date(body.fromDate), $lte: new Date(body.toDate) };
        if (body?.isTryCart) query.isTryCart = body?.isTryCart;
        // if (body?.isActive) query.isActive = body?.isActive;

        if (body?.keyword) {
            customerQuery["$or"] = [
                { "orderNo": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "customer.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "address.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "addressDetails.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "customer.mobile": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
            ]
        }

        // const response = await 
        console.log(query)
        const pipeline = [
            {
                $match: query
            },
            {
                $lookup: {
                    from: "customers",
                    localField: "customer",
                    foreignField: "_id",
                    as: "customer"
                }
            },
            {
                $unwind: "$customer"
            },
            {
                $match: customerQuery
            },
        ]

        const [orders, totalArray] = await Promise.all([
            db.Order.aggregate([
                ...pipeline,
                {
                    $lookup: {
                        from: "addresses",
                        localField: "address",
                        foreignField: "_id",
                        as: "address"
                    }
                },
                {
                    $unwind: "$address"
                },
                {
                    $sort: { createdAt: -1 }
                },
                {
                    $skip: ((Number(page)) - 1) * Number(limit)
                },
                {
                    $limit: Number(limit)
                }
            ]),
            db.Order.aggregate([
                ...pipeline,
                {
                    $count: "count"
                }
            ]),
        ])

        const total = totalArray?.[0]?.count ?? 0

        if (orders) {
            helper.deliverResponse(res, 200, {
                orders,
                total
            }, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }

    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.exportCSV = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const filters = body?.filters ?? {};
        let customerQuery = {};

        let query = { isDelete: false, isTryCart: false, storeId: storeid, ...filters };

        if (body?.fromDate && !body?.toDate) query.orderDate = { $gte: new Date(body.fromDate) };
        if (body?.toDate && !body?.fromDate) query.orderDate = { $lte: new Date(body.toDate) };
        if (body?.fromDate && body?.toDate)
            query.orderDate = { $gte: new Date(body.fromDate), $lte: new Date(body.toDate) };

        if (body?.keyword) {
            customerQuery["$or"] = [
                { "orderNo": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "customer.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "address.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "addressDetails.name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "customer.mobile": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
            ]
        }
        const pipeline = [
            {
                $match: query
            },
            {
                $lookup: {
                    from: "customers",
                    localField: "customer",
                    foreignField: "_id",
                    as: "customer"
                }
            },
            {
                $unwind: "$customer"
            },
            {
                $match: customerQuery
            },
        ]

        const orders = await db.Order.aggregate([
            ...pipeline,
            {
                $lookup: {
                    from: "addresses",
                    localField: "address",
                    foreignField: "_id",
                    as: "address"
                }
            },
            {
                $unwind: "$address"
            },
            {
                $sort: { createdAt: -1 }
            }
        ])
        const items = [];
        for (let [key, order] of orders.entries()) {
            const shipped = order?.history?.find((item) => item?.status?.toLowerCase()?.includes("shipped"))
            const delivered = order?.history?.find((item) => item?.status?.toLowerCase()?.includes("delivered"))

            const products = await db.Products.find({ _id: { $in: order?.products?.map((product) => product?.product)}}).populate("brand", "name")

            items.push(
                {
                    no: key + 1,
                    orderNo: order?.orderNo,
                    // customer: order?.customer?.name,
                    customer: order?.addressDetails.name ? order?.addressDetails.name : (order?.address?.name ?? order?.customer.name),
                    mobile: order?.address?.mobile,
                    city: order?.address?.city,
                    date: formatDate(new Date(order?.orderDate)),
                    shipped: shipped ? formatDate(new Date(shipped?.date)) : "",
                    delivered: delivered ? formatDate(new Date(delivered?.date)) : "",
                    total: order?.total,
                    products: `${products?.map((product) => product?.name?.en).join(', ')}`,
                    brands: `${products?.map((product) => product?.brand?.name?.en).join(', ')}`,
                    paymentMethod: order?.paymentMethod,
                    orderStatus: order?.orderStatus?.toLowerCase()?.includes("shipped") ? "SHIPPED" : order?.orderStatus,
                }
            )
        }

        const header = [
            { id: "no", title: "Sl No" },
            { id: "orderNo", title: "Order No" },
            { id: "customer", title: "Customer" },
            { id: "mobile", title: "Mobile" },
            { id: "city", title: "City" },
            { id: "date", title: "Placed Date" },
            { id: "shipped", title: "Shipped Date" },
            { id: "delivered", title: "Delivered Date" },
            { id: "products", title: "Products" },
            { id: "brands", title: "Brands" },
            { id: "total", title: "Total" },
            { id: "paymentMethod", title: "Payment Method" },
            { id: "orderStatus", title: "Order Status" },
        ]

        const csvWriter = createCsvWriter({
            path: "orders.csv",
            header,
            alwaysQuote: true,
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="orders.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('orders.csv');


        // helper.deliverResponse(res, 200, response, {
        //     error_code: messages.SUCCESS_RESPONSE.error_code,
        //     error_message: messages.SUCCESS_RESPONSE.error_message,
        // });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.getOrderDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { orderNo } = req.params;
        const orderDetails = await service.detail({ orderNo: orderNo, storeId: storeid });
        if (!orderDetails) {
            return helper.deliverResponse(
                res,
                404,
                {},
                {
                    error_code: messages.ORDERS.NOT_FOUND.error_code,
                    error_message: messages.ORDERS.NOT_FOUND.error_message
                }
            );
        }
        if (orderDetails && orderDetails?.products?.length > 0) {
            for (let product of orderDetails?.products) {
                let lensDetails = null

                if (product?.lensDetails?.brand) {
                    let leftSph = null
                    let rightSph = null
                    let leftCyl = null
                    let rightCyl = null
                    let leftAxis = null
                    let rightAxis = null
                    if (product?.lensDetails?.prescription && typeof product.lensDetails.prescription === 'object') {
                        if (product?.lensDetails?.prescription?.leftSph) {
                            const leftSphData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftSph
                            })
                            if (leftSphData) leftSph = leftSphData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightSph) {
                            const rightSphData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightSph
                            })
                            if (rightSphData) rightSph = rightSphData?.name
                        }

                        if (product?.lensDetails?.prescription?.leftCyl) {
                            const leftCylData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftCyl
                            })
                            if (leftCylData) leftCyl = leftCylData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightCyl) {
                            const rightCylData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightCyl
                            })
                            if (rightCylData) rightCyl = rightCylData?.name
                        }

                        if (product?.lensDetails?.prescription?.leftAxis) {
                            const leftAxisData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftAxis
                            })
                            if (leftAxisData) leftAxis = leftAxisData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightAxis) {
                            const rightAxisData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightAxis
                            })
                            if (rightAxisData) rightAxis = rightAxisData?.name
                        }
                    }
                    lensDetails = {
                        vision: product?.lensDetails?.vision,
                        prescription: {
                            leftSph: leftSph,
                            leftCyl: leftCyl,
                            leftAxis: leftAxis,
                            rightSph: rightSph,
                            rightCyl: rightCyl,
                            rightAxis: rightAxis,
                            pd: product?.lensDetails?.prescription?.pd || null
                        },
                        lensType: product?.lensDetails?.lensType || null,
                        brand: product?.lensDetails?.brand || null,
                        photocromic: product?.lensDetails?.photocromic || null,
                        index: product?.lensDetails?.index || null,
                        coating: product?.lensDetails?.coating || null
                    }
                }
                product.lensDetails = lensDetails
            }
        }

        helper.deliverResponse(res, 200, orderDetails, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.updateOrderStatus = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { orderNo } = req.params;
        const { body } = req;
        const orderDetails = await service.findOne({ orderNo: orderNo, isDelete: false, storeId: storeid });
        if (!orderDetails) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.ORDERS.NOT_FOUND.error_code,
                    error_message: "Cannot update order"
                }
            );
        }
        if (body?.paymentStatus) orderDetails.paymentStatus = body?.paymentStatus;
        if (body?.orderStatus) {
            if (body?.orderStatus == "CANCELLED") {
                // if (orderDetails.history.includes("SHIPPED VIA ECO") || orderDetails.history.includes("SHIPPED VIA INHOUSE")) {
                if (orderDetails.history.includes("DELIVERED")) {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: messages.ORDERS.CANT_CANCEL.error_code,
                            error_message: messages.ORDERS.CANT_CANCEL.error_message
                        }
                    );
                    return;
                }
                for (let product of orderDetails.products) {
                    const productDetails = await productService.findOne({ _id: product.product });
                    productDetails.stock += product.quantity;
                    await productService.update({ _id: product.product }, productDetails);
                }
            }

            const sendSI = async (order, customerDetails) => {
                try {
                    const url = "https://app.optculture.com/subscriber/processReceipt.mqrm";
                    // const url = "https://qcapp.optculture.com/subscriber/processReceipt.mqrm";
                    let items = [];
                    let gross = 0;
                    let totalDiscount = 0;
                    let couponType = "Receipt";
                    let promotionItems = [];
                    for (let product of order?.products) {
                        const productDetails = await productService.findOne({
                            _id: product?.product,
                            isDelete: false
                        });
                        let discount = Number(product.price) != Number(product.OGPrice) ? (Math.abs(product.price - product.OGPrice) * product?.quantity) : 0
                        if (product?.couponAmount && product?.couponAmount > 0) {
                            discount += (product?.couponAmount ?? 0);
                            couponType = "Item";
                            promotionItems.push({
                                "DiscountType": "Item",
                                "ItemCode": productDetails?.sku,
                                "ItemDiscount": (Number(product?.couponAmount) / product?.quantity).toFixed(2),
                                "DiscountAmount": "",
                                "QuantityDiscounted": product?.quantity,
                                "CouponCode": order?.couponCode,
                                "RewardRatio": ""
                            })
                        }
                        totalDiscount += Number(discount);
                        gross += Number(product?.OGPrice) * product?.quantity
                        items.push({
                            H_SYS_ID: productDetails?._id, // Mandatory
                            REF_SYS_ID: "",
                            COMP_CODE: "",
                            TXN_CODE: "",
                            H_LOCN_CODE: "", // Mandatory
                            H_LOCN_NAME: "UAE", //Mandatory
                            H_SM_CODE: "", //Mandatory
                            DOC_NO: order?._id, //Mandatory
                            DOC_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("YYYY-MM-DD HH:mm:ss"), //Mandatory
                            REF_TXN_CODE: "",
                            REF_NO: "",
                            ITEM_BAR_CODE: "", //Mandatory
                            ITEM_CODE: productDetails?.sku, //Mandatory
                            ITEM_ANLY_CODE_01: productDetails?.category?.[0]?.name?.en ? `${productDetails?.category?.[0]?.name?.en}` : "", //Mandatory
                            ITEM_ANLY_CODE_02: productDetails?.brand?.name?.en ? `${productDetails?.brand?.name?.en}` : "", //Mandatory
                            ITEM_ANLY_CODE_03: "",
                            ITEM_ANLY_CODE_04: productDetails?.color?.name?.en ? `${productDetails?.color?.name?.en}` : "",
                            ITEM_ANLY_CODE_05: product?.size?.name ? `${product?.size?.name}` : "", //Mandatory
                            ITEM_ANLY_CODE_06: product?.contactLens?.sphLeft ? `Sph Left: ${product?.contactLens?.sphLeft?.name}` : "",
                            ITEM_ANLY_CODE_07: product?.contactLens?.cylLeft ? `Cyl Left: ${product?.contactLens?.cylLeft?.name}` : "",
                            ITEM_ANLY_CODE_08: product?.contactLens?.axisLeft ? `Axis Left: ${product?.contactLens?.axisLeft?.name}` : "",
                            ITEM_ANLY_CODE_09: product?.contactLens?.sphRight ? `Sph Right: ${product?.contactLens?.sphRight?.name}` : "",
                            ITEM_ANLY_CODE_10: product?.contactLens?.cylRight ? `Cyl Right: ${product?.contactLens?.cylRight?.name}` : "",
                            ITEM_ANLY_CODE_11: product?.contactLens?.axisRight ? `Axis Right: ${product?.contactLens?.axisRight?.name}` : "",
                            ITEM_ANLY_CODE_12: product?.contactLens?.multiFocal ? `Addition: ${product?.contactLens?.multiFocal}` : "",
                            ITEM_DESC: productDetails?.name?.en, //Mandatory
                            UOM_CODE: "PCS", //Mandatory
                            INVI_QTY: product?.quantity, //Mandatory
                            INVI_RATE: product?.OGPrice, //Mandatory
                            GROSS: product?.OGPrice * product?.quantity, //Mandatory
                            DIS: discount, //Mandatory
                            VAT: (product?.tax * product?.quantity).toFixed(3) || "0.00", //Mandatory
                            NET: (product?.OGPrice * product?.quantity) - discount, //Mandatory
                            RINVI_QTY: "0", //Mandatory
                            RINVI_RATE: "0.00", //Mandatory
                            RGROSS: "0.00", //Mandatory
                            RDIS: "0.00", //Mandatory
                            RVAT: "0.00", //Mandatory
                            RNET: "0.00", //Mandatory
                            H_CR_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("YYYY-MM-DD HH:mm:ss"), //Mandatory
                            CUST_NAME: order?.address?.name ?? customerDetails?.name,
                            CUST_CODE: customerDetails?._id,
                            BILL_MOBILE: order?.address?.mobile ?? customerDetails?.mobile,
                            BILL_EMAIL: order?.address?.email ?? customerDetails?.email,
                            DIS_OPT: (product?.couponAmount && !isNaN(product?.couponAmount)) ? Number(product?.couponAmount)?.toFixed(2) : "0.00"
                        });
                    }
                    console.log(items)
                    let promotion = {};
                    if (order?.couponCode && couponType == "Receipt") {
                        promotion = {
                            "DiscountType": "Receipt",
                            "DiscountAmount": order?.savings?.toFixed(2),
                            "CouponCode": order?.couponCode
                        }
                    }

                    let balance = gross - totalDiscount;
                    if (order?.couponCode || order?.loyaltyDiscount) {
                        if (order?.loyaltyDiscount) {
                            balance -= order?.loyaltyDiscount;
                        }
                        if (order?.couponCode && couponType == "Receipt") {
                            balance -= order?.savings
                        }
                    }
                    balance += (order?.paymentCharge ?? 0);
                    const payload = {
                        Head: {
                            // og
                            // user: {
                            //     userName: "webcastle", // Mandatory field
                            //     token: "0J3RIVME081FARD3", // Mandatory field
                            //     organizationId: "webcastle" // Mandatory field
                            // },
                            user: {
                                userName: CRM_USERNAME, // Mandatory field
                                token: CRM_TOKEN, // Mandatory field
                                organizationId: CRM_ORG_ID // Mandatory field
                                // userName: "webcastle", // Mandatory field
                                // token: "0J3RIVME081FARD3", // Mandatory field
                                // organizationId: "webcastle" // Mandatory field
                            },
                            enrollCustomer: "N", // Mandatory field
                            isLoyaltyCustomer: "Y", // Mandatory field
                            emailReceipt: "Y", // Mandatory field ->If we want send E-receipt through email then it should 'Y' otherwise 'N'
                            printReceipt: "N", // Mandatory field
                            requestSource: "Orion", // Mandatory field
                            requestFormat: "JSON", // Mandatory field
                            requestEndPoint: "/processReceipt.mqrm", // Mandatory field
                            requestType: "New", // Mandatory field
                            receiptType: "SO_INVOICE" // Mandatory field -> If the customer pruchases then receipt type should be'SALE' ,
                        },
                        Body: {
                            Items: items,
                            Receipt: {
                                COMP_CODE: "",
                                LOCATION_CODE: "1",
                                LOCN_NAME: "",
                                INV_RCPT_NUM: order?.orderNo, // Mandatory field
                                INV_RCPT_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                                    "YYYY-MM-DD HH:mm:ss"
                                ), // Mandatory field
                                INV_BUSINESS_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                                    "YYYY-MM-DD HH:mm:ss"
                                ), // Mandatory field
                                INV_RCPT_TM: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("DD/MM/YYYY hh:mm:ss a"), // Mandatory field
                                // INV_NET_AMT: (order?.total - order?.vatAmount),
                                INV_NET_AMT: Number(gross - totalDiscount + (order?.paymentCharge ?? 0) + (order?.shippingCharge ?? 0))?.toFixed(2), //order?.total
                                INV_NET_TDR_AMT: Number(balance)?.toFixed(2),

                                // INV_TAX_AMT: order?.vatAmount || "0.00",
                                INV_TAX_AMT: order?.vatAmount,
                                RNET: "0.00",
                                RINV_TAX_AMT: "",
                                INV_DISC_AMT: Number(totalDiscount)?.toFixed(2) || "0.00", // Mandatory field
                                INV_GROSS_AMT: Number(gross)?.toFixed(2), // Mandatory field
                                TXN_CODE: "",
                                CUST_NAME: order?.address?.name ?? customerDetails?.name, // Mandatory field
                                CUST_CODE: customerDetails?._id?.toString(), // Mandatory field
                                BILL_MOBILE: order?.address?.mobile ?? customerDetails?.mobile, // Mandatory field
                                BILL_EMAIL: order?.address?.email ?? customerDetails?.email, // Mandatory field
                                DocSID: order?._id?.toString() + "SI" // Mandatory field
                            },
                        },
                        OptcultureDetails: {
                            MembershipNumber: "", // Mandatory field
                            Email: customerDetails?.email, // Mandatory field
                            Phone: customerDetails?.mobile, // Mandatory field
                            LoyaltyRedeem: {
                                DiscountAmount: order?.loyaltyDiscount?.toFixed(2) ?? "" // Mandatory field
                            },
                            LoyaltyRedeemReversal: "0",
                            Promotions: order?.couponCode ? couponType == "Receipt" ? [promotion] : promotionItems : []
                        },
                    };

                    if (order.paymentMethod === "ONLINE") {
                        payload.Body["Payment"] = {
                            "VISA_CARD": {
                                // Payment details are mandatory
                                CURRENCY_CODE: "AED",
                                TENDER_AMOUNT: order?.total,
                                SOTENDER_AMOUNT: "0.00",
                                RTENDER_AMOUNT: "0.00"
                            }
                        }
                    } else {
                        payload.Body.Receipt["CHARGES"] = {
                            "COD_CHARGES": order?.paymentCharge ?? 0,
                            "SHIPPING_CHARGES": order?.shippingCharge ?? 0,
                            "HANDELING_CHARGES": 0,
                            "CHARGE_01": 0,
                            "CHARGE_02": 0,
                            "CHARGE_03": 0,
                            "CHARGE_04": 0,
                            "CHARGE_05": 0
                        }
                        if (order.paymentStatus === "PAID") {
                            payload.Body["Payment"] = {
                                "CASH": {
                                    // Payment details are mandatory
                                    CURRENCY_CODE: "AED",
                                    TENDER_AMOUNT: order?.total,
                                    SOTENDER_AMOUNT: "0.00",
                                    RTENDER_AMOUNT: "0.00"
                                }
                            }

                        } else {
                            payload.Body["Payment"] = {
                                "CASH": {
                                    // Payment details are mandatory
                                    CURRENCY_CODE: "AED",
                                    TENDER_AMOUNT: order?.total,
                                    SOTENDER_AMOUNT: "0.00",
                                    RTENDER_AMOUNT: "0.00"
                                }
                            }
                        }
                    }
                    console.log(payload.Body["Payment"])
                    console.log(payload)
                    console.log(payload.OptcultureDetails)
                    console.log(payload.Body.Receipt["CHARGES"])
                    const crmResponse = await axios.post(url, payload);
                    console.log(crmResponse?.data);
                    const URL = "https://app.optculture.com/subscriber/loyaltyTransactionHistory.mqrm"
                    const lookup = await axios.post(URL, {
                        "header": {
                            "requestId": "",
                            "requestDate": "",
                            "sourceType": "ALL"
                        },
                        "lookup": {
                            "emailAddress": customerDetails?.email,
                            "phone": customerDetails?.mobile,
                            "membershipNumber": customerDetails?.loyaltyNumber
                        },
                        "report": {
                            "source": "All",
                            "transactionType": "All",
                            "startDate": "2014-01-01 00:00:00",
                            "endDate": moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                                "YYYY-MM-DD HH:mm:ss"
                            ),
                            "offset": "0",
                            "maxRecords": "10",
                            "store": ""
                        },
                        "user": {
                            "userName": CRM_USERNAME,
                            "token": CRM_TOKEN,
                            "organizationId": CRM_ORG_ID,
                            "sessionID": ""
                            // userName: "webcastle", // Mandatory field
                            // token: "0J3RIVME081FARD3", // Mandatory field
                            // organizationId: "webcastle", // Mandatory field
                            // sessionID: ""
                        }
                    })

                    const receipt = lookup?.data?.matchedCustomers?.[0]?.transactions?.[0]?.eReceiptURL
                    return receipt
                } catch (error) {
                    console.log(error);
                    return null
                }
            };

            const orderStatusSequence = [
                "PLACED",
                "CONFIRMED",
                "READY TO SHIP",
                "SHIPPED VIA ECO",
                "SHIPPED VIA INHOUSE",
                // "OUT FOR DELIVERY",
                "DELIVERED",
                "RETURNED",
                "REFUNDED"
            ];

            const address = await addressService.findOne({
                _id: orderDetails?.address,
            });

            const currentStatusIndex = orderStatusSequence.indexOf(orderDetails.orderStatus);
            const newStatusIndex = orderStatusSequence.indexOf(body?.orderStatus);

            if (newStatusIndex > currentStatusIndex + 1) {
                for (let i = currentStatusIndex + 1; i < newStatusIndex; i++) {
                    if (
                        orderStatusSequence[i] === "SHIPPED VIA INHOUSE" &&
                        orderDetails.history[orderDetails.history.length - 1].status === "SHIPPED VIA ECO"
                    ) continue;
                    if (
                        body?.orderStatus === "SHIPPED VIA INHOUSE" &&
                        orderStatusSequence[i] === "SHIPPED VIA ECO"
                    ) continue;

                    if (orderStatusSequence[i] === "DELIVERED") {
                        const invoice = await sendSI(orderDetails, orderDetails?.customer)
                        orderDetails.invoice = invoice
                    }
                    if (orderStatusSequence[i] === "READY TO SHIP") {
                        const shipping = await shippingCreate(orderDetails, address, orderDetails?.paymentMethod)
                        console.log(shipping)
                        if (shipping?.status === "error") {
                            return helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: shipping?.message
                                }
                            );
                        }
                    }
                    orderDetails.history.push({
                        status: orderStatusSequence[i],
                        date: new Date().toISOString()
                    });
                }
            }
            if (orderDetails.history.includes(body?.orderStatus)) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Choose another status"
                    }
                );
            }

            if (body?.orderStatus != orderDetails.orderStatus) {
                if (body?.orderStatus === "DELIVERED") {
                    const invoice = await sendSI(orderDetails, orderDetails?.customer);
                    console.log(invoice)
                    orderDetails.invoice = invoice;
                }
                if (body?.orderStatus === "SHIPPED VIA ECO") {
                    const shipping = await shippingCreate(orderDetails, address, orderDetails?.paymentMethod)
                    console.log(shipping)
                    if (shipping?.status === "error") {
                        return helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: shipping?.message
                            }
                        );
                    }
                    orderDetails.awb = shipping?.data?.awb_label_print
                    orderDetails.trackingId = shipping?.data?.tracking_no
                }
                orderDetails.orderStatus = body?.orderStatus;
                orderDetails.history.push({
                    status: body?.orderStatus,
                    date: new Date().toISOString()
                });
            } else {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Choose another status"
                    }
                );
            }
        }
        const updatedOrder = await service.update({ orderNo: orderNo, storeId: storeid }, orderDetails);
        if (updatedOrder instanceof Error) {
            return helper.deliverResponse(res, 422, updatedOrder, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        }

        async function captureTamaraPayment(products, order) {
            try {
                const payload = {
                    "order_id": order?.tamaraOrderId,
                    "total_amount": {
                        "amount": order?.total,
                        "currency": "AED"
                    },
                    "items": products,
                    // "shipping_amount": {
                    //     "amount": 0,
                    //     "currency": "AED"
                    // },
                    "shipping_info": {
                        "shipped_at": new Date().toISOString(),
                        "shipping_company": order?.orderStatus == "SHIPPED VIA ECO" ? "ECO" : "INHOUSE",
                        // "tracking_number": 100,
                        // "tracking_url": "https://shipping.com/tracking?id=123456"
                    },
                }

                await axios.post(
                    `${TAMARA_API}/payments/capture`,
                    payload,
                    {
                        headers: { Authorization: `Bearer ${TAMARA_API_TOKEN}` }
                    }
                );

                console.log("Payment Captured")

            } catch (error) {
                console.log(error)
            }
        }

        const customerDetails = await customerService.findOne({ _id: orderDetails.customer });
        const products = [];
        let tamaraProducts = []

        const addressDetails = await addressService.findOne({ _id: orderDetails.address });
        for (let product of updatedOrder.products) {
            const productDetails = await productService.findOne({ _id: product.product });
            products.push({
                thumbnail: process.env.DOMAIN + productDetails.thumbnail,
                name: productDetails.name?.en,
                quantity: product.quantity,
                total: product.total,
                price: product?.price,
                couponAmount: product?.couponAmount ?? 0
            });
            if (updatedOrder?.orderStatus?.includes("SHIPPED") && updatedOrder?.gateway === "TAMARA") {
                tamaraProducts.push({
                    name: productDetails?.name?.en,
                    type: productDetails?.productType,
                    reference_id: productDetails?.refid,
                    sku: productDetails?.sku,
                    quantity: product?.quantity,
                    total_amount: {
                        amount: product.price * product.quantity,
                        currency: "AED"
                    }
                })

            }
        }

        if (updatedOrder?.orderStatus?.includes("SHIPPED") && updatedOrder?.gateway === "TAMARA") {
            await captureTamaraPayment(tamaraProducts, updatedOrder)
        }

        if (body?.orderStatus && body?.orderStatus != "READY TO SHIP") {
            const orderdata = {
                status: updatedOrder?.orderStatus,
                order: updatedOrder,
                address: addressDetails,
                products: products
            };

            if (customerDetails?.email) {
                let subjectStatus = (updatedOrder?.orderStatus === "SHIPPED VIA ECO" || updatedOrder?.orderStatus === "SHIPPED VIA INHOUSE") ? "SHIPPED" : updatedOrder?.orderStatus
                const mailOptions = {
                    from: process.env.EMAIL_USER,
                    to: customerDetails?.email,
                    subject: "Order" + " " + subjectStatus,
                    html: await template.orderPlaced(orderdata),
                    attachments: [
                        {
                            filename: "logo-light-full.png",
                            path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                            cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                        }
                    ]
                };

                if (updatedOrder?.orderStatus != "DELIVERED") {

                    emailHelper.sendMail(mailOptions, (error, info) => {
                        if (error) {
                            console.log(error);
                        } else {
                        }
                    });
                }


                // const smsApi = "https://qcapp.optculture.com/subscriber/sendMimSms.mqrm"
            }

            if (updatedOrder?.orderStatus === "CONFIRMED" || (updatedOrder?.orderStatus === "SHIPPED VIA ECO" || updatedOrder?.orderStatus === "SHIPPED VIA INHOUSE")) {
                const failoverAPI = "https://waba.myinboxmedia.in/api/sendwaba";

                let message = "";
                let templateName = "";
                switch (updatedOrder?.orderStatus) {
                    // case "PLACED":
                    //     message = `Order Received!\nDear ${customerDetails?.name}, Thank you for your purchase with ${process.env.FRONTEND_URL}! Your order ${updatedOrder?.orderNo} has been received. Download your order details here: ${process.env.FRONTEND_URL}/my-accounts/my-orders/${updatedOrder?.orderNo}`
                    //     break;
                    case "CONFIRMED":
                        message = `Order Confirmed!\nDear ${customerDetails?.name}, Thank you for your order. Your order ${updatedOrder?.orderNo} with ${process.env.FRONTEND_URL} has been confirmed. We'll notify you once it's ready for shipment.`
                        templateName = "order_confirmation_website";
                        break;
                    case "SHIPPED VIA ECO":
                    case "SHIPPED VIA INHOUSE":
                        message = `Order Shipped!\nDear ${customerDetails?.name}, Your order ${updatedOrder?.orderNo} from ${process.env.FRONTEND_URL} has been shipped.`
                        templateName = "order_shipped_website";
                        break;
                    // case "DELIVERED":
                    //     message = `Order Delivered!\nDear ${customerDetails?.name}, Thank you for shopping with ${process.env.FRONTEND_URL}.  Your Order ${updatedOrder?.orderNo} has been delivered. Please download your eReceipt ${updatedOrder?.invoice}`
                    //     break;
                }

                const countryCode = customerDetails?.countryCode[0] == "+" ? customerDetails?.countryCode.substr(1) : customerDetails?.countryCode

                const payload = {
                    "ProfileId": process.env.MIM_FAILOVER_ID,
                    "APIKey": process.env.MIM_FAILOVER_KEY,
                    "MobileNumber": `${countryCode}${customerDetails?.mobile}`,
                    "templateName": templateName,
                    "Parameters": [customerDetails?.name, updatedOrder?.orderNo],
                    "HeaderType": "Text",
                    "Text": "",
                    "MediaUrl": "",
                    "isTemplate": "true",
                    "Latitude": 0,
                    "Longitude": 0,
                    "HeaderParameter": "",
                    "Base64Filename": "",
                    "Base64File": "",
                    "CTAButtonURLParameter": "",
                    "CTAButtonURLParameter2": "",
                    "Failover": [
                        {
                            "FailoverType": "SMS",
                            "Payload": {
                                "userid": process.env.MIM_USERID,
                                "pwd": process.env.MIM_PWD,
                                "mobile": `${countryCode}${customerDetails?.mobile}`, //customerDetails?.countryCode + customerDetails?.mobile,
                                "sender": "YATEEM",
                                "msg": message,
                                "msgtype": "16"
                            }
                        }
                    ]
                }
                console.log(payload)
                console.log(payload['Failover'])
                const res = await axios.post(failoverAPI, payload)
                console.log(res.data)
            }

        }


        helper.deliverResponse(res, 200, updatedOrder, {
            error_code: messages.ORDERS.UPDATED.error_code,
            error_message: messages.ORDERS.UPDATED.error_message
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

const shippingCreate = async (order, address, paymentMethod) => {
    try {
        let totalQuantity = 0;
        let totalWeight = 0;
        for (let product of order.products) {
            totalQuantity += product.quantity;
            const productDetails = await productService.findOne({ _id: product.product });
            totalWeight += productDetails.weight * product.quantity;
        }
        let payload = {
            order_reference: order?.orderNo,
            service_type: "normal_delivery",
            product_type: "non_document",
            consignee_name: address?.name,
            consignee_address: `${address?.street}, ${address.city}, ${address.emirates}, ${address.country}`,
            consignee_city: address.emirates,
            consignee_mobile_no: address?.countryCode + address.mobile,
            item_quantity: totalQuantity,
            item_weight: totalWeight,
            shipper_name: "Test",
            shipper_city: "Abu Dhabi",
            shipper_address: "Test",
            shipper_mobile_no: "+971-555558552"
        };
        if (paymentMethod == "COD") {
            payload.cod = order.total;
            payload.cod_payment_mode = "cash_only";
        } else {
            payload.cod_payment_mode = "card_only"
        }
        const url =
            "https://app.ecofreight.ae/api/webservices/client/order";
        return axios
            .post(url, payload, {
                headers: {
                    "Authorization": `Bearer ${process.env.ECOFREIGHT_API_KEY}`
                }
            })
            .then((response) => {
                return response?.data;
            })
            .catch((error) => {
                throw error; // Rethrow error to be caught below
            });
    } catch (error) {
        throw error; // Rethrow error to be caught where the function is called
    }
};

exports.placeOrder = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message
            });
        }

        let { authorization, devicetoken, storeid, language } = req.headers;
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            customerId = getCustomerIdFromToken(authorization);
            isGuest = false;
            customerDetails = await getCustomerDetails(customerId);
            if (!customerDetails) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Customer is Blocked"
                    }
                );
            }
            cartDetails = await getCustomerCart(customerDetails?._id, storeid);
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await getGuestCart(devicetoken, storeid);
        }

        const { body } = req;

        if (body?.cart == "try-cart") {
            return handleTryCartOrder(res, req, body, customerDetails);
        } else if (body?.cart == "subscription") {
            return handleSubscriptionOrder(res, req, body, isGuest, customerDetails, cartDetails);
        } else {
            return handleRegularOrder(res, req, body, isGuest, customerDetails, cartDetails);
        }
    } catch (error) {
        console.log(error)
        return deliverServerErrorResponse(res, error);
    }
};

exports.tabbyCheck = async (req, res) => {
    try {

        const { authorization, devicetoken } = req.headers;
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";

        if (authorization) {
            customerId = getCustomerIdFromToken(authorization);
            isGuest = false;
            customerDetails = await getCustomerDetails(customerId);
            if (!customerDetails) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Customer is Blocked"
                    }
                );
            }
            cartDetails = await getCustomerCart(customerDetails?._id);
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await getGuestCart(devicetoken);
        }

        const { body } = req;
        const products = [];

        const addressDetails = await addressService.findOne({
            _id: body?.address,
            isDelete: false
        });

        for (let product of cartDetails?.products) {
            let flag = false;
            if (product?.contactLens) {
                if (product?.contactLens?.sphLeft) flag = isLensPriceable(product?.contactLens?.sphLeft);
                if (product?.contactLens?.sphRight) flag = isLensPriceable(product?.contactLens?.sphRight);
                if (product?.contactLens?.cylLeft) flag = isLensPriceable(product?.contactLens?.cylLeft);
                if (product?.contactLens?.cylRight) flag = isLensPriceable(product?.contactLens?.cylRight);
                if (product?.contactLens?.axisLeft) flag = isLensPriceable(product?.contactLens?.axisLeft);
                if (product?.contactLens?.axisRight) flag = isLensPriceable(product?.contactLens?.axisRight);
            }
            const productDetails = await productService.findOne({
                _id: product?.product,
                isActive: true,
                isDelete: false
            });

            if (!productDetails) continue;

            let price;
            let OGPrice;
            let stock = productDetails?.stock;

            price = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                ? productDetails?.offerPrice?.aed
                : productDetails?.price?.aed;
            OGPrice = productDetails?.price?.aed

            if (flag) {
                price += (productDetails?.powerPrice ?? 0);
                OGPrice += (productDetails?.powerPrice ?? 0);
            }

            if (stock < product.quantity) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: `Product ${productDetails.name?.en} is dont have enough stock`
                    }
                );
            }

            if (product.quantity < 1) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: `Product ${productDetails.name?.en} is dont have enough quantity`
                    }
                );
            }

            let productSize = null;
            let contactProductSize = null
            if (product?.size) {
                productSize = await sizeService.findOne(
                    { _id: product?.size, isDelete: false },
                    { _id: 1, name: 1 }
                );
            }
            if (product?.contactSize) {
                contactProductSize = await contactSizeService.findOne(
                    { _id: product?.contactSize, isDelete: false },
                    { _id: 1, name: 1 }
                );
            }

            // let productPrice = product?.size
            //     ? productDetails?.sizes.find(
            //         (size) => String(size?.size?._id) === String(product?.size)
            //     )?.offerPrice ||
            //     productDetails?.sizes.find(
            //         (size) => String(size?.size?._id) === String(product?.size)
            //     )?.price
            //     : productDetails?.offerPrice?.aed || productDetails?.price?.aed;

            products.push({
                title: productDetails?.name?.en,
                description: productDetails?.description?.en,
                category: productDetails?.productType,
                unit_price: price,
                quantity: product?.quantity
            });
        }

        const store = await db.MultiStore.findOne({ storeId: storeid, isDelete: false })

        const payload = {
            "payment": {
                "amount": body?.amount,
                "currency": "AED",
                "description": "Tabby payment check",
                "buyer": {
                    "phone": customerDetails?.countryCode + customerDetails?.mobile,
                    "email": customerDetails?.email,
                    "name": customerDetails?.name,
                },
                "buyer_history": {
                    "registered_since": customerDetails?.createdAt,
                    "loyalty_level": 0,
                },
                "order": {
                    // "tax_amount": "0.00",
                    // "shipping_amount": "0.00",
                    // "discount_amount": "0.00",
                    // "updated_at": "2019-08-24T14:15:22Z",
                    "reference_id": "check",
                    "items": products
                },
                "order_history": [
                    {
                        "purchased_at": new Date().toISOString(),
                        "amount": body?.amount,
                        "payment_method": "card",
                        "status": "new",
                        "buyer": {
                            "phone": customerDetails?.countryCode + customerDetails?.mobile,
                            "email": customerDetails?.email,
                            "name": customerDetails?.name,
                        },
                        "shipping_address": {
                            "city": addressDetails?.city,
                            "address": `${addressDetails?.street}, ${addressDetails?.city}, ${addressDetails?.emirates}, ${addressDetails?.country}`,
                            "zip": addressDetails?.postalCode
                        },
                    }
                ],
                "shipping_address": {
                    "city": addressDetails?.city,
                    "address": `${addressDetails?.street}, ${addressDetails?.city}, ${addressDetails?.emirates}, ${addressDetails?.country}`,
                    "zip": addressDetails?.postalCode
                },
            },
            "lang": "en",
            "merchant_code": "YOUAE",
            "merchant_urls": {
                "cancel": process.env.FRONTEND_URL + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                "failure": process.env.FRONTEND_URL + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                "success": process.env.FRONTEND_URL + "/verify-order?order_number_tabby=" + body?.orderNo + "&cartType=" + "cart",
            }
        }

        console.log(payload)

        try {
            const response = await axios.post("https://api.tabby.ai/api/v2/checkout", payload, {
                headers: {
                    "Authorization": `Bearer ${process.env.TABBY_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.data?.status === "rejected") {
                return helper.deliverResponse(
                    res,
                    422,
                    {
                        error: "Tabby not available for this order"
                    },
                    {
                        error_code: 1,
                        error_message: "Tabby not available for this order",
                    }
                );
            }

            return helper.deliverResponse(res, 200, { success: true }, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });

        } catch (error) {
            console.log(error.response.data)
            return deliverServerErrorResponse(res, error.response.data);
        }

    } catch (error) {
        console.log(error)
        return deliverServerErrorResponse(res, error);
    }
};


const getCustomerIdFromToken = (authorization) => {
    const token = authorization.split("Bearer ")[1];
    let customerId = null;
    jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
        if (!err) {
            customerId = decoded?.customerId;
        }
    });
    return customerId;
};

const getCustomerDetails = async (customerId) => {
    return await customerService.findOne({ refid: customerId, isDelete: false, isActive: true });
};

const getCustomerCart = async (customerId, storeId) => {
    return await cartService.findOne({
        customer: customerId,
        isDelete: false,
        isActive: true,
        isPurchased: false,
        storeId
    });
};

const getGuestCart = async (devicetoken, storeId) => {
    return await cartService.findOne({
        devicetoken,
        customer: { $exists: false },
        isDelete: false,
        isActive: true,
        isPurchased: false,
        storeId
    });
};


const handleTryCartOrder = async (res, req, body, customerDetails) => {
    try {
        if (!customerDetails) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Please login first"
                }
            );
        }

        if (!body?.address) {
            const defaultAddress = await addressService.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isDefault: true
            });
            if (!defaultAddress) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Please add an address"
                    }
                );
            }
            body.address = defaultAddress?._id;
        }

        const addressDetails = await addressService.findOne({
            _id: body?.address,
            customer: customerDetails?._id,
            isDelete: false,
            isActive: true
        });

        const trycart = await service.findOne({
            customer: customerDetails?._id,
            isDelete: false,
            isActive: true,
            isPurchased: true,
            isTryCart: true
        });

        if (trycart && trycart?.orderStatus !== "RETURNED") {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "You have already placed an order"
                }
            );
        }

        body.customer = customerDetails?._id;
        body.refid = (await service.count({})) + 1;
        body.cart = null;
        const expectedDate = new Date();
        expectedDate.setDate(expectedDate.getDate() + 7);
        body.expectedDate = expectedDate;
        body.orderNo = `YATE-00${body.refid}`;
        body.isTryCart = true;

        let products = [];
        const tryCartSettings = await tryCartService.findOne({ isDelete: false });

        for (let product of customerDetails?.tryCart) {
            const productDetails = await productService.findOne({
                _id: product?.product,
                isDelete: false
            });

            let productSize = null;
            let productPrice = null;
            if (product?.size) {
                productSize = await sizeService.findOne({ _id: product?.size, isDelete: false });
                productPrice =
                    productDetails?.sizes.find(
                        (size) => String(size?.size?._id) == String(product?.size)
                    )?.offerPrice ||
                    productDetails?.sizes.find(
                        (size) => String(size?._id) === String(product?.size)
                    )?.price;
            } else {
                productPrice = productDetails?.offerPrice?.aed || productDetails?.price?.aed;
            }


            products.push({
                product: productDetails?._id,
                quantity: 1,
                price: productPrice,
                total: productPrice,
                currency: productDetails?.currency || "AED",
                size: productSize?._id,
                name: productDetails?.name?.en
            });
        }
        let vatAmount = 0;
        body.products = products;
        body.baseTotal = tryCartSettings?.amount;
        const paymentMethod = await paymentFeeService.findOne({ type: req.body.paymentMethod });
        paymentCharge = paymentMethod?.fee;
        body.paymentCharge = paymentCharge
        body.total = paymentCharge ? Number(tryCartSettings?.amount) + Number(paymentCharge) : tryCartSettings?.amount;
        const vat = await paymentFeeService.findOne({ type: "vat" });
        if (vat && vat?.fee > 0) vatAmount = (body.total * vat.fee) / 100;
        if (vatAmount > 0) body.total += vatAmount;
        const currentDate = new Date();
        const returnDate = new Date(currentDate);
        returnDate.setDate(currentDate.getDate() + tryCartSettings?.duration);
        body.returnDate = returnDate;

        if (body?.paymentMethod === "COD") {
            body["paymentMethod"] = "COD";
            body["paymentStatus"] = "PENDING";
            body["orderStatus"] = "PLACED";
            body["history"] = [
                {
                    status: "PLACED",
                    date: new Date()
                }
            ];
        } else {
            const paymentApiBody = {
                apiOperation: "INITIATE",
                order: {
                    reference: body?.orderNo,
                    amount: Number(body?.total).toFixed(2),
                    currency: "AED",
                    name: "Payment for order " + body?.orderNo,
                    channel: "web",
                    category: "3ds",
                    items: products.map((product) => ({
                        name: product.name,
                        quantity: product.quantity,
                        unitPrice: Number(product.price).toFixed(2)
                    })),
                    ipAddress:
                        req.headers["x-forwarded-for"] || req.ip || req.connection.remoteAddress
                },
                billing: {
                    address: {
                        street: addressDetails?.street,
                        city: addressDetails?.city,
                        stateProvince: addressDetails?.emirates,
                        country: "AE",
                        postalCode: addressDetails?.postalCode
                    },
                    contact: {
                        firstName: addressDetails?.name,
                        phone: addressDetails?.countryCode + addressDetails?.mobile,
                        mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                        email: customerDetails?.email
                    }
                },
                shipping: {
                    address: {
                        street: addressDetails?.street,
                        city: addressDetails?.city,
                        stateProvince: addressDetails?.emirates,
                        country: "AE",
                        postalCode: addressDetails?.postalCode
                    },
                    contact: {
                        firstName: addressDetails?.name,
                        phone: addressDetails?.countryCode + addressDetails?.mobile,
                        mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                        email: customerDetails?.email
                    }
                },
                configuration: {
                    tokenizeCc: "true",
                    returnUrl:
                        "https://beta.yateem.com/verify-order?orderNo=" +
                        body?.orderNo +
                        "&cartType=" +
                        "cart",
                    locale: "en"
                }
            };


            try {
                const response = await axios.post(
                    NOONAPI,
                    paymentApiBody,
                    {
                        headers: { Authorization: process.env.PAYMENT_KEY }
                    }
                );


                if (response?.data?.resultCode === 0) {
                    body["paymentMethod"] = "ONLINE";
                    body["paymentStatus"] = "PENDING";
                    body["orderStatus"] = "PENDING";
                    body["isActive"] = false;

                    paymentApiResponse = response?.data?.result?.checkoutData;
                } else {
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Payment initiation failed"
                        }
                    );
                }
            } catch (error) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "error in payment initiation"
                    }
                );
            }

            // return helper.deliverResponse(
            //     res,
            //     422,
            //     {},
            //     {
            //         error_code: 1,
            //         error_message: "This Payment Method is Temporarily not available"
            //     }
            // );
        }

        const order = await service.create(body);

        if (order instanceof Error) {
            return helper.deliverResponse(res, Error, {
                error_code: messages.ORDERS.FAILED.error_code,
                error_message: messages.ORDERS.FAILED.error_message
            });
        } else {
            if (order.orderStatus !== "PENDING" && order.paymentMethod !== "ONLINE") {
                await customerService.update(
                    { _id: customerDetails?._id, isDelete: false, isActive: true },
                    { tryCart: [] }
                );

                // for (let product of order?.products) {
                //     const productDetails = await productService.findOne({
                //         _id: product?.product,
                //         isActive: true,
                //         isDelete: false
                //     });
                //     if (product?.size) {
                //         productDetails.sizes.map((size) => {
                //             if (String(size?.size?._id) === String(product?.size)) {
                //                 size.stock = size.stock - 1;
                //             }
                //         });
                //         productDetails.stock = productDetails.stock - 1;
                //         await productService.update({ _id: productDetails?._id }, productDetails);
                //     } else {
                //         await productService.update(
                //             { _id: product?.product, isActive: true, isDelete: false },
                //             { stock: productDetails?.stock - product.quantity }
                //         );
                //     }
                // }

                if (customerDetails?.email) {
                    const orderdata = {
                        status: "Placed",
                        order: order,
                        address: addressDetails,
                        products: products
                    };
                    const mailOptions = {
                        from: process.env.EMAIL_USER,
                        to: customerDetails?.email,
                        subject: "Order Placed",
                        html: await template.orderPlaced(orderdata),
                        attachments: [
                            {
                                filename: "logo-light-full.png",
                                path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                            }
                        ]
                    };

                    emailHelper.sendMail(mailOptions, (error, info) => {
                        if (error) {
                        } else {
                        }
                    });
                }
            }

            let responseData = {};
            if (order?.paymentMethod === "COD") {
                isGuest = false;
                responseData = order;
            } else responseData = paymentApiResponse;
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.ORDERS.PLACED.error_code,
                error_message: messages.ORDERS.PLACED.error_message
            });
        }
    } catch (error) {
        console.log(error)
        return deliverServerErrorResponse(res, error);
    }
};

const handleRegularOrder = async (res, req, body, isGuest, customerDetails, cartDetails) => {
    try {
        let { storeid, language } = req?.headers;
        storeid = storeid ? storeid : "ae";
        language = language ? language : "en";
        if (!isGuest) {
            body.customer = customerDetails?._id;
            if (!body?.address) {
                const defaultAddress = await addressService.findOne({
                    customer: customerDetails?._id,
                    isDelete: false,
                    isActive: true,
                    isDefault: true
                });
                if (!defaultAddress) {
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Please add an address"
                        }
                    );
                }
                body.address = defaultAddress?._id;
            }
        } else {
            body.devicetoken = devicetoken;
            if (!body?.address)
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Please add an address"
                    }
                );
        }

        let paymentApiResponse = null;

        body.cart = cartDetails?._id;
        body.refid = (await service.count({})) + 1;
        const expectedDate = new Date();
        expectedDate.setDate(expectedDate.getDate() + 7);
        body.expectedDate = expectedDate;
        body.orderNo = `YATE${storeid.toUpperCase()}-0000${body.refid}`;
        body.invoiceId = `YATE-IN00${body.refid}`;
        body.isTryCart = false;
        body.storeId = storeid;

        const [addressDetails, store] = await Promise.all([
            addressService.findOne({
                _id: body?.address,
                isDelete: false,
                storeId: storeid
            }),
            db.MultiStore.findOne({ storeId: storeid, isDelete: false })
        ])

        const NOON_API_URL = store?.envs?.NOON_API_URL;
        const NOON_API_KEY = store?.envs?.NOON_API_KEY;
        const TAMARA_API_URL = store?.envs?.TAMARA_API_URL;
        const TAMARA_TOKEN = store?.envs?.TAMARA_API_TOKEN;
        const TABBY_TOKEN = store?.envs?.TABBY_TOKEN;

        let products = [];
        let emailProducts = [];
        let tamaraProducts = [];
        let tabbyProducts = [];
        let totalTryCartDeduction = 0;
        let vatAmount = 0;
        let appliedOrderNo = null;
        let nonContactLensTotal = 0;

        if (cartDetails?.products?.length === 0) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Cart is empty"
                }
            );
        }

        const vat = await paymentFeeService.findOne({ type: "vat", storeId: storeid });

        const taxConstant = (100 + Number(vat?.fee)) / 100

        for (let product of cartDetails?.products) {
            let flag = false;
            if (product?.contactLens) {
                if (product?.contactLens?.sphLeft) flag = isLensPriceable(product?.contactLens?.sphLeft);
                if (product?.contactLens?.sphRight) flag = isLensPriceable(product?.contactLens?.sphRight);
                if (product?.contactLens?.cylLeft) flag = isLensPriceable(product?.contactLens?.cylLeft);
                if (product?.contactLens?.cylRight) flag = isLensPriceable(product?.contactLens?.cylRight);
                if (product?.contactLens?.axisLeft) flag = isLensPriceable(product?.contactLens?.axisLeft);
                if (product?.contactLens?.axisRight) flag = isLensPriceable(product?.contactLens?.axisRight);
            }
            const productDetails = await productService.findOne({
                _id: product?.product,
                isActive: true,
                isDelete: false,
                storeId: storeid
            });
            let price;
            let OGPrice;
            let stock = productDetails?.stock;

            price = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                ? productDetails?.offerPrice?.aed
                : productDetails?.price?.aed;
            OGPrice = productDetails?.price?.aed

            if (flag) {
                price += (productDetails?.powerPrice ?? 0);
                OGPrice += (productDetails?.powerPrice ?? 0);
            }

            let tax = price - (price / taxConstant)

            // if(product?.contactLens?.multiple){
            //     tax = tax * 2;
            // }

            if (stock < product.quantity) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: `Product ${productDetails.name?.en} is dont have enough stock`
                    }
                );
            }

            if (product.quantity < 1) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: `Product ${productDetails.name?.en} is dont have enough quantity`
                    }
                );
            }

            // let productPrice = product?.size
            //     ? productDetails?.sizes.find(
            //         (size) => String(size?.size?._id) === String(product?.size)
            //     )?.offerPrice ||
            //     productDetails?.sizes.find(
            //         (size) => String(size?.size?._id) === String(product?.size)
            //     )?.price
            //     : productDetails?.offerPrice?.aed || productDetails?.price?.aed;

            if (product.fromTryCart) {
                if (product.orderNo !== appliedOrderNo) {
                    const orderAmount = await orderService.findOne({
                        orderNo: product.orderNo,
                        storeId: storeid
                    });
                    totalTryCartDeduction += orderAmount?.total;
                    appliedOrderNo = product.orderNo;
                }
            }

            if (!body?.storeId) body.storeId = null;

            products.push({
                product: product?.product,
                tax: productDetails?.isTaxIncluded ? tax : 0,
                couponCode: product?.couponCode,
                couponAmount: product?.couponAmount,
                quantity: product?.quantity * (product?.contactLens?.multiple ? 2 : 1),
                price: price,
                OGPrice,
                type: productDetails?.productType,
                total: (price * (product?.contactLens?.multiple ? 2 : 1)) * product.quantity,
                currency: product?.currency,
                name: productDetails?.name?.en,
                size: productDetails?.size?._id,
                contactSize: productDetails?.contactSize,
                thumbnail: productDetails?.thumbnail,
                lensDetails: product?.lensDetails ? product?.lensDetails : null,
                contactLens: product?.contactLens ? {
                    multiple: product?.contactLens?.multiple,
                    sphLeft: product?.contactLens?.sphLeft?._id,
                    sphRight: product?.contactLens?.sphRight?._id,
                    cylLeft: product?.contactLens?.cylLeft?._id,
                    cylRight: product?.contactLens?.cylRight?._id,
                    axisLeft: product?.contactLens?.axisLeft?._id,
                    axisRight: product?.contactLens?.axisRight?._id,
                    multiFocal: product?.contactLens?.multiFocal,
                } : null
            });
            emailProducts.push({
                product: product?.product,
                quantity: product?.quantity * (product?.contactLens?.multiple ? 2 : 1),
                price: price,
                total: (price * (product?.contactLens?.multiple ? 2 : 1)) * product.quantity,
                currency: product?.currency,
                name: productDetails?.name?.en,
                size: productDetails?.size?._id,
                contactSize: productDetails?.contactSize?._id,
                thumbnail: productDetails?.thumbnail,
                lensDetails: product?.lensDetails ? product?.lensDetails : null,
                contactLens: product?.contactLens ? {
                    multiple: product?.contactLens?.multiple,
                    sphLeft: product?.contactLens?.sphLeft,
                    sphRight: product?.contactLens?.sphRight,
                    cylLeft: product?.contactLens?.cylLeft,
                    cylRight: product?.contactLens?.cylRight,
                    axisLeft: product?.contactLens?.axisLeft,
                    axisRight: product?.contactLens?.axisRight,
                } : null
            });
            tamaraProducts.push({
                name: productDetails?.name?.en,
                type: productDetails?.productType,
                reference_id: productDetails?.refid,
                sku: productDetails?.sku,
                quantity: product?.quantity * (product?.contactLens?.multiple ? 2 : 1),
                // discount_amount: {
                //     amount: 0,
                //     currency: "AED"
                // },
                // tax_amount: {
                //     amount: productDetails?.isTaxIncluded ? tax : 0,
                //     currency: "AED"
                // },
                // unit_price: {
                //     amount: price,
                //     currency: "AED"
                // },
                total_amount: {
                    amount: (price * (product?.contactLens?.multiple ? 2 : 1)) * product.quantity,
                    currency: "AED"
                }
            })
            tabbyProducts.push({
                title: productDetails?.name?.en,
                description: productDetails?.description?.en,
                category: productDetails?.productType,
                unit_price: price,
                quantity: product?.quantity
            });
        }

        let totalQty = 0;
        let totaWeight = 0;
        let shippingCharge = 0;
        let paymentCharge = 0;
        const [paymentMethod, giftWrapping] = await Promise.all([
            paymentFeeService.findOne({ type: req.body.paymentMethod, storeId: storeid }),
            generalSettingService.findOne({ storeId: storeid }, { isGiftWrapping: 1, giftWrappingFee: 1 })
        ]);
        if(body?.isGiftWrapping){
            body.giftWrappingFee = giftWrapping?.giftWrappingFee;
            body.isGiftWrapping = true;
        }
        paymentCharge = paymentMethod?.fee;
        cartDetails?.products?.forEach((product) => {
            totalQty = totalQty + product?.quantity * (product?.contactLens?.multiple ? 2 : 1);
            const productDetail = productService.findOne({
                _id: product?.product,
                isDelete: false
            });
            totaWeight = totaWeight + productDetail?.weight * product?.quantity;
        });
        const url =
            "https://app.ecofreight.ae/api/webservices/client/checkprice";
        const payload = {
            rec_city: addressDetails?.city,
            service_type: "normal_delivery",
            qty: totalQty,
            weight: Number(totaWeight).toFixed(2)
        };
        let headers = {
            "Content-Type": "application/json",
            Accept: "application/json"
        };

        // await axios
        //     .post(url, payload, { headers: headers })
        //     .then((response) => {
        //         let fixedResponseData = fixResponseString(response.data);
        //         let jsonObject = JSON.parse(fixedResponseData);
        //         if (jsonObject?.status == "success") {
        //             shippingCharge = jsonObject?.data?.price;
        //         }
        //     })
        //     .catch((error) => {
        //     });

        if (vat && vat?.fee > 0) {
            vatAmount = products.reduce((acc, product) => Number(acc + (product?.tax * product?.quantity)), 0);
        }
        const baseTotal = products.reduce((acc, product) => acc + product?.total, 0)
        body.products = products;
        body.baseTotal = baseTotal;
        body.savings = cartDetails?.savings;
        body.couponCode = cartDetails?.couponCode;
        body.loyaltyDiscount = cartDetails?.loyaltyAmount;
        body.shippingCharge = shippingCharge;
        body.paymentCharge = paymentCharge;
        body.tryCartDeduction = totalTryCartDeduction > 0 ? totalTryCartDeduction : 0;

        if (body?.shippingMethod != "click") {
            body.store = null
        }

        body.vat = vat.fee > 0 ? vat.fee : 0;
        body.vatAmount = vatAmount > 0 ? vatAmount.toFixed(2) : 0;

        let total = paymentCharge ? Number(baseTotal) + Number(paymentCharge) : baseTotal;

        if (totalTryCartDeduction > 0) total -= totalTryCartDeduction;
        if (cartDetails?.loyaltyAmount > 0) total -= cartDetails?.loyaltyAmount;
        if (shippingCharge > 0) total += Number(shippingCharge);
        if (body?.isGiftWrapping && giftWrapping?.giftWrappingFee > 0) total += Number(giftWrapping?.giftWrappingFee);
        if (cartDetails?.savings > 0) total -= cartDetails?.savings;
        body.total = Number(total).toFixed(2); //cartDetails?.total;

        if (body?.paymentMethod === "COD") {
            body["paymentMethod"] = "COD";
            body["paymentStatus"] = "PENDING";
            body["orderStatus"] = "PLACED";
            body["history"] = [
                {
                    status: "PLACED",
                    date: new Date()
                }
            ];
        } else {

            if (body?.paymentMethod == "creditCard") {
                const paymentApiBody = {
                    apiOperation: "INITIATE",
                    order: {
                        reference: body?.orderNo,
                        amount: Number(body?.total).toFixed(2),
                        currency: "AED",
                        name: "Payment for order " + body?.orderNo,
                        channel: "web",
                        category: "3ds",
                        items: products.map((product) => ({
                            name: product.name,
                            quantity: product.quantity,
                            unitPrice: Number(product.price).toFixed(2)
                        })),
                        ipAddress:
                            req.headers?.["x-forwarded-for"]?.split(",")?.[1]?.trim() || req.ip || req.connection.remoteAddress
                    },
                    billing: {
                        address: {
                            street: addressDetails?.street,
                            city: addressDetails?.city,
                            stateProvince: addressDetails?.emirates,
                            country: "AE",
                            postalCode: addressDetails?.postalCode
                        },
                        contact: {
                            firstName: addressDetails?.name,
                            phone: addressDetails?.countryCode + addressDetails?.mobile,
                            mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                            email: customerDetails?.email
                        }
                    },
                    shipping: {
                        address: {
                            street: addressDetails?.street,
                            city: addressDetails?.city,
                            stateProvince: addressDetails?.emirates,
                            country: "AE",
                            postalCode: addressDetails?.postalCode
                        },
                        contact: {
                            firstName: addressDetails?.name,
                            phone: addressDetails?.countryCode + addressDetails?.mobile,
                            mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                            email: customerDetails?.email
                        }
                    },
                    configuration: {
                        tokenizeCc: "true",
                        returnUrl:
                            `${process.env.FRONTEND_URL}/${storeid}-${language}/verify-order?orderNo=` +
                            body?.orderNo +
                            "&cartType=" +
                            "cart",
                        locale: "en"
                    }
                };

                try {
                    const response = await axios.post(
                        NOON_API_URL,
                        paymentApiBody,
                        {
                            headers: { Authorization: NOON_API_KEY }
                        }
                    );
                    console.log(response.data)

                    if (response?.data?.resultCode === 0) {
                        body["paymentMethod"] = "ONLINE";
                        body["gateway"] = "NOON";
                        body["paymentStatus"] = "PENDING";
                        body["orderStatus"] = "PENDING";
                        body["isActive"] = false;

                        paymentApiResponse = response?.data?.result?.checkoutData;
                    } else {
                        return helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Payment initiation failed"
                            }
                        );
                    }
                } catch (error) {
                    console.log(error)
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "error in payment initiation"
                        }
                    );
                }
            } else if (body?.paymentMethod == "tamara") {
                const paymentAPIBody = {
                    "total_amount": {
                        "amount": body.total,
                        "currency": "AED"
                    },
                    "shipping_amount": {
                        "amount": 0,
                        "currency": "AED"
                    },
                    "tax_amount": {
                        "amount": body.vatAmount,
                        "currency": "AED"
                    },
                    "order_reference_id": body?.refid,
                    "order_number": body.orderNo,
                    // "discount": {
                    //   "amount": {
                    //     "amount": 200,
                    //     "currency": "SAR"
                    //   },
                    //   "name": "Christmas 2020"
                    // },
                    "items": tamaraProducts,
                    "consumer": {
                        "email": customerDetails?.email,
                        "first_name": addressDetails?.name,
                        "last_name": "",
                        "phone_number": addressDetails?.countryCode + addressDetails?.mobile
                    },
                    "country_code": "AE",
                    "description": "Payment for order " + body?.orderNo,
                    "merchant_url": {
                        "cancel": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                        "failure": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                        "success": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?order_number=" + body?.orderNo + "&cartType=" + "cart",
                        // "notification": "https://store-demo.com/payments/tamarapay"
                    },
                    "payment_type": "PAY_BY_INSTALMENTS",
                    "instalments": 3,
                    "billing_address": {
                        "city": addressDetails?.city,
                        "country_code": "AE",
                        "first_name": addressDetails?.name,
                        "last_name": "",
                        "line1": addressDetails?.street,
                        "line2": "string",
                        "phone_number": addressDetails?.countryCode + addressDetails?.mobile,
                        "region": addressDetails?.emirates
                    },
                    "shipping_address": {
                        "city": addressDetails?.city,
                        "country_code": "AE",
                        "first_name": addressDetails?.name,
                        "last_name": "",
                        "line1": addressDetails?.street,
                        "line2": "string",
                        "phone_number": addressDetails?.countryCode + addressDetails?.mobile,
                        "region": addressDetails?.emirates
                    },
                    // "platform": "platform name here",
                    "is_mobile": false,
                    "locale": "en_US",
                    // "risk_assessment": {
                    //   "customer_age": 22,
                    //   "customer_dob": "31-01-2000",
                    //   "customer_gender": "Male",
                    //   "customer_nationality": "SA",
                    //   "is_premium_customer": true,
                    //   "is_existing_customer": true,
                    //   "is_guest_user": true,
                    //   "account_creation_date": "31-01-2019",
                    //   "platform_account_creation_date": "string",
                    //   "date_of_first_transaction": "31-01-2019",
                    //   "is_card_on_file": true,
                    //   "is_COD_customer": true,
                    //   "has_delivered_order": true,
                    //   "is_phone_verified": true,
                    //   "is_fraudulent_customer": true,
                    //   "total_ltv": 501.5,
                    //   "total_order_count": 12,
                    //   "order_amount_last3months": 301.5,
                    //   "order_count_last3months": 2,
                    //   "last_order_date": "31-01-2021",
                    //   "last_order_amount": 301.5,
                    //   "reward_program_enrolled": true,
                    //   "reward_program_points": 300,
                    //   "phone_verified": false
                    // },
                    // "additional_data": {
                    //   "delivery_method": "home delivery",
                    //   "pickup_store": "Store A",
                    //   "store_code": "Store code A",
                    //   "vendor_amount": 0,
                    //   "merchant_settlement_amount": 0,
                    //   "vendor_reference_code": "AZ1234"
                    // }
                }

                try {
                    const response = await axios.post(
                        `${TAMARA_API_URL}/checkout`,
                        paymentAPIBody,
                        {
                            headers: { Authorization: `Bearer ${TAMARA_TOKEN}` }
                        }
                    );
                    console.log(response.data)

                    if (response) {
                        body["paymentMethod"] = "ONLINE";
                        body["gateway"] = "TAMARA";
                        body["paymentStatus"] = "PENDING";
                        body["orderStatus"] = "PENDING";
                        body["isActive"] = false;

                        paymentApiResponse = response?.data;
                    } else {
                        return helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Payment initiation failed"
                            }
                        );
                    }
                } catch (error) {
                    console.log(error.response.data)
                    let TamaraErrors = error.response.data?.errors || [];
                    let errorMessageFromTamara = error.response.data?.message;

                    let errorMessage = "";
                    let errorCode = TamaraErrors[TamaraErrors?.length - 1]?.error_code;

                    if (
                        errorCode == "invalid_total_amount" ||
                        errorCode == "zero_amount" ||
                        errorCode == "amount_is_lower_than_min_limit"
                    ) {
                        errorMessage =
                            "Your order total hasn't reached the minimum required amount for using Tamara as a payment method.";
                    } else if (errorCode == "consumer_empty_phone_number") {
                        errorMessage = "Please provide a valid phone number .";
                    } else if (
                        errorCode == "total_amount_invalid_limit" ||
                        errorCode == "amount_is_higher_than_max_limit"
                    ) {
                        errorMessage = "Total amount is higher than maximum limit";
                    } else {
                        errorMessage = errorMessageFromTamara || "unable proceed with this payment right now, please try again later";
                    }
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: errorMessage
                        }
                    );
                }

            } else {
                const payload = {
                    "payment": {
                        "amount": body?.total,
                        "currency": "AED",
                        "description": "Tabby payment check",
                        "buyer": {
                            "phone": customerDetails?.countryCode + customerDetails?.mobile,
                            "email": customerDetails?.email,
                            "name": customerDetails?.name,
                        },
                        "buyer_history": {
                            "registered_since": customerDetails?.createdAt,
                            "loyalty_level": 0,
                        },
                        "order": {
                            // "tax_amount": "0.00",
                            // "shipping_amount": "0.00",
                            // "discount_amount": "0.00",
                            // "updated_at": "2019-08-24T14:15:22Z",
                            "reference_id": body.orderNo,
                            "items": tabbyProducts
                        },
                        "order_history": [
                            {
                                "purchased_at": new Date().toISOString(),
                                "amount": body?.total,
                                "payment_method": "card",
                                "status": "new",
                                "buyer": {
                                    "phone": customerDetails?.countryCode + customerDetails?.mobile,
                                    "email": customerDetails?.email,
                                    "name": customerDetails?.name,
                                },
                                "shipping_address": {
                                    "city": addressDetails?.city,
                                    "address": `${addressDetails?.street}, ${addressDetails?.city}, ${addressDetails?.emirates}, ${addressDetails?.country}`,
                                    "zip": addressDetails?.postalCode
                                },
                            }
                        ],
                        "shipping_address": {
                            "city": addressDetails?.city,
                            "address": `${addressDetails?.street}, ${addressDetails?.city}, ${addressDetails?.emirates}, ${addressDetails?.country}`,
                            "zip": addressDetails?.postalCode
                        },
                    },
                    "lang": "en",
                    "merchant_code": "YOUAE",
                    "merchant_urls": {
                        "cancel": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                        "failure": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?failure=" + body?.orderNo + "&cartType=" + "cart",
                        "success": process.env.FRONTEND_URL + `/${storeid}-${language}` + "/verify-order?order_number_tabby=" + body?.orderNo + "&cartType=" + "cart",
                    }
                }

                try {
                    const response = await axios.post("https://api.tabby.ai/api/v2/checkout", payload, {
                        headers: {
                            "Authorization": `Bearer ${TABBY_TOKEN}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response) {
                        return helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Payment initiation failed"
                            }
                        );
                    }

                    if (response.data?.status === "rejected") {
                        return helper.deliverResponse(
                            res,
                            422,
                            {
                                error: "Tabby not available for this order"
                            },
                            {
                                error_code: 1,
                                error_message: "Tabby not available for this order",
                            }
                        );
                    }

                    body["paymentMethod"] = "ONLINE";
                    body["gateway"] = "TABBY";
                    body["paymentStatus"] = "PENDING";
                    body["orderStatus"] = "PENDING";
                    body["isActive"] = false;
                    body["tabbyOrderId"] = response?.data?.payment?.id;
                    body["tabbySessionId"] = response?.data?.id;

                    paymentApiResponse = response?.data;

                } catch (error) {
                    console.log(error.response.data)
                    let errorMessage = "unable proceed with this payment right now, please try again later";

                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: errorMessage
                        }
                    );
                }
            }


        }
        body.orderDate = new Date().toISOString();
        body.addressDetails = {
            name: addressDetails?.name,
            street: addressDetails?.street,
            city: addressDetails?.city,
            emirates: addressDetails?.emirates,
            postalCode: addressDetails?.postalCode,
            countryCode: addressDetails?.countryCode,
            mobile: addressDetails?.mobile,
            type: addressDetails?.type,
            suiteUnit: addressDetails?.suiteUnit
        }
        console.log("body final :: ", body);
        const order = await service.create(body);
        if (order instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                error_code: messages.ORDERS.FAILED.error_code,
                error_message: messages.ORDERS.FAILED.error_message
            });
        } else {
            if (order.orderStatus !== "PENDING" && order.paymentMethod !== "ONLINE") {
                const carterls = await cartService.update(
                    { _id: cartDetails?._id, isDelete: false, isActive: true, storeId: storeid },
                    { isPurchased: true }
                );


                for (let product of order?.products) {
                    const productDetails = await productService.findOne({
                        _id: product?.product,
                        isActive: true,
                        isDelete: false,
                        storeId: storeid
                    });

                    const remainingStock = productDetails?.stock - product?.quantity;
                    await productService.update(
                        { _id: product?.product, isActive: true, isDelete: false },
                        { stock: remainingStock < 0 ? 0 : remainingStock }
                    );

                }

                const ccs = process.env.ORDER_CCS?.split(" ")
                if (customerDetails?.email || addressDetails?.email) {
                    const orderdata = {
                        status: "Placed",
                        order: order,
                        address: addressDetails,
                        products: emailProducts
                    };
                    const mailOptions = {
                        from: process.env.EMAIL_USER,
                        to: customerDetails?.isGuest ? addressDetails?.email : customerDetails?.email,
                        subject: "Order Placed",
                        html: await template.orderPlaced(orderdata),
                        attachments: [
                            {
                                filename: "logo-light-full.png",
                                path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                            }
                        ]
                    };

                    // emailHelper.sendMail(mailOptions, (error, info) => {
                    //     if (error) {
                    //         console.log(error)
                    //     } else {
                    //     }
                    // });
                }

                const orderdata = {
                    status: "Placed",
                    order: order,
                    address: addressDetails,
                    products: emailProducts
                };

                const adminMailOptions = {
                    from: process.env.EMAIL_USER,
                    to: ccs,
                    subject: `New Order Received - Order #${orderdata?.order?.orderNo}`,
                    html: await template.orderPlacedAdmin(orderdata),
                    attachments: [
                        {
                            filename: "logo-light-full.png",
                            path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                            cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                        }
                    ]
                }

                emailHelper.sendMail(adminMailOptions, (error, info) => {
                    if (error) {
                        console.log(error)
                    } else {
                        console.log(info)
                    }
                });

                const sendDigitalReciept = await sendDigitalReceipt(order, customerDetails);

                // const shipping = await shippingCreate(order, addressDetails, order?.paymentMethod);
                // console.log(shipping)
            }

            let responseData = {};
            if (order?.paymentMethod === "COD") {
                isGuest = isGuest ? true : false;
                responseData = order;
            } else {
                responseData = paymentApiResponse;
            }
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.ORDERS.PLACED.error_code,
                error_message: messages.ORDERS.PLACED.error_message
            });
        }
    } catch (error) {
        console.log(error);
        return deliverServerErrorResponse(res, error);
    }
};

const deliverServerErrorResponse = (res, error) => {
    return helper.deliverResponse(res, 422, error, {
        error_code: messages.SERVER_ERROR.error_code,
        error_message: messages.SERVER_ERROR.error_message
    });
};

const handleSubscriptionOrder = async (res, req, body, isGuest, customerDetails) => {
    try {
        if (!isGuest) {
            body.customer = customerDetails?._id;
            if (!body?.address) {
                const defaultAddress = await addressService.findOne({
                    customer: customerDetails?._id,
                    isDelete: false,
                    isActive: true,
                    isDefault: true
                });
                if (!defaultAddress) {
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Please add an address"
                        }
                    );
                }
                body.address = defaultAddress?._id;
            }
        } else {
            body.devicetoken = devicetoken;
            if (!body?.address)
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Please add an address"
                    }
                );
        }
        const subscribeSummary = await summaryService.findOne({
            customer: customerDetails?._id,
            isDelete: false
        });
        const subscriptionPlan = await subscribePlanService.findOne({
            _id: subscribeSummary?.plan,
            isDelete: false
        });

        let paymentApiResponse = null;

        // body.cart = cartDetails?._id;
        body.refid = (await service.count({})) + 1;
        const expectedDate = new Date();
        expectedDate.setDate(expectedDate.getDate() + 7);
        body.expectedDate = expectedDate;
        body.orderNo = `YATE-0000${body.refid}`;
        body.isTryCart = false;

        const addressDetails = await addressService.findOne({
            _id: body?.address,
            isDelete: false
        });

        let products = [];

        // for (let product of cartDetails?.products) {
        const productDetails = await productService.findOne({
            _id: subscribeSummary?.product,
            isActive: true,
            isDelete: false
        });
        let stock = productDetails?.stock;
        // if (product?.size) {
        //     stock = productDetails?.sizes.find(
        //         (size) => String(size?.size?._id) === String(product?.size)
        //     )?.stock
        // }

        if (stock < 1) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: `Product ${productDetails.name?.en} is dont have enough stock`
                }
            );
        }

        // let productSize = null;
        // if (product?.size) {
        //     productSize = await sizeService.findOne(
        //         { _id: product?.size, isDelete: false },
        //         { _id: 1, name: 1 }
        //     );
        // }

        // let productPrice = product?.size
        //     ? productDetails?.sizes.find(
        //           (size) => String(size?.size?._id) === String(product?.size)
        //       )?.offerPrice ||
        //       productDetails?.sizes.find(
        //           (size) => String(size?.size?._id) === String(product?.size)
        //       )?.price
        //     : productDetails?.offerPrice?.aed || productDetails?.price?.aed;
        const productPrice = productDetails?.offerPrice?.aed
            ? productDetails?.offerPrice?.aed
            : productDetails?.price?.aed;
        const powerPrice = productDetails?.powerPrice ? productDetails?.powerPrice : 0;
        const total = subscribeSummary?.contactLens?.multiple
            ? 2 * (productPrice + powerPrice)
            : 1 * (productPrice + powerPrice);
        const totalPrice = total * 1;

        products.push({
            product: productDetails?._id,
            quantity: 1,
            price: totalPrice,
            total: totalPrice,
            currency: "AED",
            name: productDetails?.name?.en,
            // size: productSize?._id,
            thumbnail: productDetails?.thumbnail
        });
        // }

        body.products = products;
        body.baseTotal = totalPrice;
        body.savings = total * (subscriptionPlan?.discountPercent / 100);
        const paymentMethod = await paymentFeeService.findOne({ type: req.body.paymentMethod });
        paymentCharge = paymentMethod?.fee;
        let fee = paymentCharge ? paymentCharge : 0;
        body.paymentCharge = fee
        body.total = (totalPrice - totalPrice * (subscriptionPlan?.discountPercent / 100)) + fee;

        if (body?.paymentMethod === "COD") {
            body["paymentMethod"] = "COD";
            body["paymentStatus"] = "PENDING";
            body["orderStatus"] = "PLACED";
            body["history"] = [
                {
                    status: "PLACED",
                    date: new Date()
                }
            ];
        } else {
            const paymentApiBody = {
                apiOperation: "INITIATE",
                order: {
                    reference: body?.orderNo,
                    amount: Number(body?.total).toFixed(2),
                    currency: "AED",
                    name: "Payment for order " + body?.orderNo,
                    channel: "web",
                    category: "3ds",
                    items: products.map((product) => ({
                        name: product.name,
                        quantity: product.quantity,
                        unitPrice: Number(product.price).toFixed(2)
                    })),
                    ipAddress:
                        req.headers["x-forwarded-for"] || req.ip || req.connection.remoteAddress
                },
                billing: {
                    address: {
                        street: addressDetails?.street,
                        city: addressDetails?.city,
                        stateProvince: addressDetails?.emirates,
                        country: "AE",
                        postalCode: addressDetails?.postalCode
                    },
                    contact: {
                        firstName: addressDetails?.name,
                        phone: addressDetails?.countryCode + addressDetails?.mobile,
                        mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                        email: customerDetails?.email
                    }
                },
                shipping: {
                    address: {
                        street: addressDetails?.street,
                        city: addressDetails?.city,
                        stateProvince: addressDetails?.emirates,
                        country: "AE",
                        postalCode: addressDetails?.postalCode
                    },
                    contact: {
                        firstName: addressDetails?.name,
                        phone: addressDetails?.countryCode + addressDetails?.mobile,
                        mobilePhone: addressDetails?.countryCode + addressDetails?.mobile,
                        email: customerDetails?.email
                    }
                },
                configuration: {
                    tokenizeCc: "true",
                    returnUrl:
                        `${process.env.DOMAIN}/api/web/verify-subscription?orderNo=` +
                        body?.orderNo +
                        "&cartType=" +
                        "subscription",
                    locale: "en"
                }
            };

            try {
                const response = await axios.post(
                    NOONAPI,
                    paymentApiBody,
                    {
                        headers: { Authorization: process.env.PAYMENT_KEY }
                    }
                );


                if (response?.data?.resultCode === 0) {
                    body["paymentMethod"] = "ONLINE";
                    body["paymentStatus"] = "PENDING";
                    body["orderStatus"] = "PENDING";
                    body["isActive"] = false;

                    paymentApiResponse = response?.data?.result?.checkoutData;
                } else {
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Payment initiation failed"
                        }
                    );
                }
            } catch (error) {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "error in payment initiation"
                    }
                );
            }
        }

        const order = await service.create(body);

        if (order instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                error_code: messages.ORDERS.FAILED.error_code,
                error_message: messages.ORDERS.FAILED.error_message
            });
        } else {
            if (order.orderStatus !== "PENDING" && order.paymentMethod !== "ONLINE") {
                const carterls = await cartService.update(
                    { _id: cartDetails?._id, isDelete: false, isActive: true },
                    { isPurchased: true }
                );

                for (let product of order?.products) {
                    const productDetails = await productService.findOne({
                        _id: product?.product,
                        isActive: true,
                        isDelete: false
                    });
                    if (product?.size) {
                        productDetails.sizes.map((size) => {
                            if (String(size?.size?._id) === String(product?.size)) {
                                size.stock = size.stock - product?.quantity;
                            }
                        });
                        productDetails.stock = productDetails.stock - product?.quantity;
                        await productService.update({ _id: productDetails?._id }, productDetails);
                    } else {
                        await productService.update(
                            { _id: product?.product, isActive: true, isDelete: false },
                            { stock: productDetails?.stock - product?.quantity }
                        );
                    }
                }

                if (customerDetails?.email) {
                    const orderdata = {
                        status: "Placed",
                        order: order,
                        address: addressDetails,
                        products: products
                    };
                    const mailOptions = {
                        from: process.env.EMAIL_USER,
                        to: customerDetails?.email,
                        subject: "Order Placed",
                        html: await template.orderPlaced(orderdata),
                        attachments: [
                            {
                                filename: "logo-light-full.png",
                                path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                            }
                        ]
                    };

                    // emailHelper.sendMail(mailOptions, (error, info) => {
                    //     if (error) {
                    //     } else {
                    //     }
                    // });
                }

                const digitalReciept = await sendDigitalReceipt(order, customerDetails);

                // const shipping = await shippingCreate(order, addressDetails, order?.paymentMethod);
            }

            let responseData = {};
            if (order?.paymentMethod === "COD") {
                isGuest = isGuest ? true : false;
                responseData = order;
            } else responseData = paymentApiResponse;
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.ORDERS.PLACED.error_code,
                error_message: messages.ORDERS.PLACED.error_message
            });
        }
    } catch (error) {
        return deliverServerErrorResponse(res, error);
    }
};

exports.getOrders = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { customerId } = res?.locals?.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false
        });
        if (!customerDetails) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message
                }
            );
        }
        const { page, limit } = req.query
        const query = {
            customer: customerDetails?._id,
            isDelete: false,
            isTryCart: false,
            isActive: true,
            storeId: storeid
        }
        // console.log(customerDetails?._id)
        const [orders, count] = await Promise.all([
            service.find(query, {}, page, limit),
            service.count(query)
        ])

        const response = [];
        // console.log(orders[0])
        if (orders.length > 0) {
            for (let order of orders) {
                let products = [];
                for (let product of order?.products) {
                    products.push({
                        _id: product?.product?._id,
                        name: product?.product?.name[language],
                        thumbnail: process.env.DOMAIN + product?.product?.thumbnail,
                        currency: product?.currency,
                        quantity: product?.quantity,
                        priceTotal: product?.total,
                        color: { ...product?.product?.color?._doc, name: product?.product?.color?.name[language] },
                        size: product?.size,
                        slug: product?.product?.slug
                    });
                }

                response.push({
                    _id: order?._id,
                    orderNo: order?.orderNo,
                    orderDate: order?.orderDate,
                    orderStatus: order?.orderStatus,
                    expectedDate: order?.expectedDate,
                    paymentMethod: order?.paymentMethod,
                    products: products,
                    gateway: order?.gateway
                });
            }
        }

        let pages = Math.ceil(count / Number(limit));
        let isLastPage = Number(page) * Number(limit) >= count;
        let nextPage = isLastPage ? null : Number(page) + 1;

        const data = {
            orders: response,
            count: count,
            totalPages: pages,
            page: Number(page),
            limit: Number(limit),
            nextPage: nextPage,
            isLastPage: isLastPage,
        };

        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.bulkDeleteOrders = async (req, res) => {
    try {
        const { orderIds } = req.body;
        console.log(orderIds)
        const response = await db.Order.updateMany(
            { orderNo: { $in: orderIds }, isDelete: false },
            { isDelete: true }
        );
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.getTryCartOrder = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { customerId } = res?.locals?.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isActive: true,
            isDelete: false
        });
        if (!customerDetails) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message
                }
            );
        }
        const orderDetail = await service.findOne({
            customer: customerDetails?._id,
            isDelete: false,
            isTryCart: true,
            isActive: true,
            storeId: storeid
        });
        if (!orderDetail) {
            return helper.deliverResponse(res, 422, Error, {
                error_code: messages.ORDERS.NOT_FOUND.error_code,
                error_message: messages.ORDERS.NOT_FOUND.error_message
            });
        } else {
            let products = [];
            let invoice = [
                { label: "Try Cart Cost", value: orderDetail?.baseTotal, currency: "AED" },
                { label: "Shipping", value: orderDetail?.shipping || 0, currency: "AED" },
                {
                    label: "Subtotal",
                    value: orderDetail?.baseTotal + (orderDetail?.shipping || 0),
                    currency: "AED"
                }
            ];
            const address = await addressService.findOne({
                _id: orderDetail?.address,
                isActive: true,
                isDelete: false
            });
            for (let product of orderDetail?.products) {
                if (!product?.notInterested) {
                    const productDetails = await productService.findOne({
                        _id: product?.product,
                        isActive: true,
                        isDelete: false,
                        storeId: storeid
                    });
                    let selectedSize = null;
                    if (product?.size) {
                        const size = await sizeService.findOne({
                            _id: product?.size,
                            isActive: true,
                            isDelete: false,
                            storeId: storeid
                        });
                        selectedSize = size;
                    }
                    products.push({
                        _id: productDetails?._id,
                        name: productDetails?.name[language],
                        thumbnail: process.env.DOMAIN + productDetails?.thumbnail,
                        currency: product?.currency,
                        quantity: product?.quantity,
                        price: product?.price,
                        priceTotal: product?.total,
                        size: selectedSize,
                        color: productDetails?.color?.name,
                        slug: productDetails?.slug
                    });
                }
            }

            const response = {
                orderNo: orderDetail?.orderNo,
                history: orderDetail?.history.map((item) => item?.status),
                products: products,
                invoice: invoice,
                address: address,
                expectedDate: orderDetail?.expectedDate,
                orderDate: orderDetail?.orderDate,
                orderStatus: orderDetail?.orderStatus,
                paymentMethod: orderDetail?.paymentMethod,
                returnDate: orderDetail?.returnDate
            };
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.orderDetail = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { customerId } = res?.locals?.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isActive: true,
            isDelete: false
        });
        if (!customerDetails) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message
                }
            );
        }
        const orderDetail = await service.findOne({
            orderNo: req.params?.orderNo,
            customer: customerDetails?._id,
            isDelete: false,
            storeId: storeid
        });
        if (!orderDetail) {
            return helper.deliverResponse(res, 422, Error, {
                error_code: messages.ORDERS.NOT_FOUND.error_code,
                error_message: messages.ORDERS.NOT_FOUND.error_message
            });
        } else {
            let products = [];
            let invoice = [];

            // const address = await addressService.findOne({
            //     _id: orderDetail?.address,
            //     // isActive: true,
            //     // isDelete: false
            // });

            for (let product of orderDetail?.products) {
                const productDetails = await productService.findOne({
                    _id: product?.product,
                    isActive: true,
                    isDelete: false,
                    storeId: storeid
                });

                let selectedSize = null;
                if (product?.size) {
                    const size = await sizeService.findOne({
                        _id: product?.size,
                        isActive: true,
                        isDelete: false,
                        storeId: storeid
                    });
                    selectedSize = size;
                }

                if (product?.contactSize) {
                    const size = await contactSizeService.findOne({
                        _id: product?.contactSize,
                        isActive: true,
                        isDelete: false,
                        storeId: storeid
                    });
                    selectedSize = size;
                }

                products.push({
                    _id: productDetails?._id,
                    name: productDetails?.name[language],
                    thumbnail: process.env.DOMAIN + productDetails?.thumbnail,
                    currency: product?.currency,
                    quantity: product?.quantity,
                    price: product?.price,
                    priceTotal: product?.total,
                    color: {
                        ...productDetails?.color,
                        name: productDetails?.color?.name[language],
                    },
                    size: selectedSize,
                    isReviewed: product?.isReviewed,
                    slug: productDetails?.slug,
                    contactLensDetails: product?.contactLens ? product?.contactLens : null
                });
            }
            const translation = await db.Texts.findOne({ storeId: storeid })
            if (orderDetail?.vat > 0) {
                invoice.push({
                    label: translation.cartPage.subtotal[language] ?? "Subtotal",
                    value: Number(orderDetail?.baseTotal - orderDetail?.vatAmount).toFixed(2),
                    currency: "AED",
                    sign: ""
                });

                invoice.push({
                    label: `VAT(${orderDetail?.vat}%)`,
                    value: Number(orderDetail?.vatAmount).toFixed(2),
                    currency: "AED",
                    sign: "+"
                });
            } else {
                invoice.push({
                    label: translation.cartPage.subtotal[language] ?? "Subtotal",
                    value: orderDetail?.baseTotal,
                    currency: "AED",
                    sign: ""
                });
            }

            // if (orderDetail?.baseTotal)
            //     invoice.push({ label: "Subtotal", value: orderDetail?.baseTotal, currency: "AED", sign: "" });
            // if(orderDetail?.vat)
            //     invoice.push({ label: `VAT(${orderDetail?.vat}%)`, value: orderDetail?.vatAmount, currency: "AED", sign: "+" });
            if (orderDetail?.savings)
                invoice.push({ label: translation.cartPage.savings?.[language] ?? "Savings", value: orderDetail?.savings, currency: "AED", sign: "-" });
            if (orderDetail?.loyaltyDiscount)
                invoice.push({ label: translation.cartPage.loyaltyDiscount?.[language] ?? "Loyalty Discount", value: Number(orderDetail?.loyaltyDiscount).toFixed(2), currency: "AED", sign: '-' });
            if (orderDetail?.tryCartDeduction && orderDetail?.tryCartDeduction > 0)
                invoice.push({ label: translation.cartPage.tryCartDeduction?.[language] ?? "Try Cart Deduction", value: orderDetail?.tryCartDeduction, currency: "AED", sign: "-" });
            if (orderDetail?.shippingCharge && orderDetail?.shippingCharge > 0)
                invoice.push({ label: translation.cartPage.shipping?.[language] ?? "Shipping", value: orderDetail?.shippingCharge, currency: "AED", sign: "+" });
            if (orderDetail?.paymentCharge && orderDetail?.paymentCharge > 0)
                invoice.push({ label: orderDetail?.paymentMethod === "COD" ? "Cash on Delivery Fee" : "Additional charges", value: orderDetail?.paymentCharge, currency: "AED", sign: "+" });
            if (orderDetail?.isGiftWrapping && orderDetail?.giftWrappingFee > 0)
                invoice.push({ label: translation.cartPage.giftWrapping?.[language] ?? "Gift Wrapping", value: orderDetail?.giftWrappingFee, currency: "AED", sign: "+" });
            if (orderDetail?.total)
                invoice.push({ label: translation.cartPage.total?.[language] ?? "Total", value: orderDetail?.total, currency: "AED", sign: "" });

            const response = {
                _id: orderDetail?._id,
                orderNo: orderDetail?.orderNo,
                history: orderDetail?.history.map((item) => item?.status),
                products: products,
                invoice: invoice,
                invoiceUrl: orderDetail?.invoice,
                address: orderDetail?.address?.name ? orderDetail?.address : orderDetail?.addressDetails,
                expectedDate: orderDetail?.expectedDate,
                orderDate: orderDetail?.orderDate,
                orderStatus: orderDetail?.orderStatus.includes("SHIPPED") ? "SHIPPED" : orderDetail?.orderStatus,
                paymentMethod: orderDetail?.paymentMethod,
                paymentStatus: orderDetail?.paymentStatus,
                shippingMethod: orderDetail?.shippingMethod,
                store: {
                    ...orderDetail?.store?._doc,
                    name: orderDetail?.store?.name[language],
                    address: orderDetail?.store?.address[language]
                },
                awb: orderDetail?.awb,
                trackingId: orderDetail?.trackingId,
                gateway: orderDetail?.gateway
            };
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.cancelOrder = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { customerId } = res?.locals?.user;
        const { body } = req;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isActive: true,
            isDelete: false
        });
        const order = await service.findOne({
            _id: body?.order,
            customer: customerDetails?._id,
            isDelete: false,
            isActive: true,
            storeId: storeid
        });
        if (order) {
            order.orderStatus = "CANCELLED";
            let orderHistory = order?.history || [];
            orderHistory.push({
                status: "CANCELLED",
                date: new Date()
            });
            order.history = orderHistory;
            const orderUpdate = await service.update({ _id: order?._id, storeId: storeid }, order);
            if (orderUpdate) {
                for (let product of order?.products) {
                    let productDetails = await productService.findOne({
                        _id: product?.product,
                        isActive: true,
                        isDelete: false,
                        storeId: storeid
                    });
                    if (productDetails) {
                        productDetails.quantity += product?.quantity;
                        await productService.update({ _id: productDetails?._id, storeId: storeid }, productDetails);
                    }
                }

                // await axios.post('https://private-anon-1cc9f8c8bc-ecofreight.apiary-mock.com/v2/api/client/order/cancel',{order_reference : order.orderNo}).then((response) => {
                //     console.log(response)
                //     if(response.status != 1) {
                //         return helper.deliverResponse(
                //             res,
                //             422,
                //             {},
                //             {
                //                 error_code: messages.ORDERS.FAILED_CANCELLED.error_code,
                //                 error_message: messages.ORDERS.FAILED_CANCELLED.error_message
                //             }
                //         );
                //     }
                // }).catch((error) => {
                //     console.log(error)
                //     return helper.deliverResponse(
                //         res,
                //         422,
                //         {},
                //         {
                //             error_code: messages.ORDERS.FAILED_CANCELLED.error_code,
                //             error_message: messages.ORDERS.FAILED_CANCELLED.error_message
                //         }
                //     );
                // })

                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.ORDERS.CANCELLED.error_code,
                        error_message: messages.ORDERS.CANCELLED.error_message
                    }
                );
            } else {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.ORDERS.FAILED_CANCELLED.error_code,
                        error_message: messages.ORDERS.FAILED_CANCELLED.error_message
                    }
                );
            }
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.ORDERS.NOT_FOUND.error_code,
                    error_message: messages.ORDERS.NOT_FOUND.error_message
                }
            );
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.verifyPayment = async (req, res) => {
    const { body } = req;
    let paymentSuccess = false;

    if (body?.failure) return helper.deliverResponse(
        res,
        422,
        {},
        {
            error_code: 1,
            error_message: "Payment Failed"
        }
    );

    let order;
    let store;
    if (body?.order_number) { // Tamara request
        let orderId = body?.orderId
        let orderNo = body?.order_number
        if (body?.order_id) {
            orderId = body?.order_id
            orderNo = body?.order_number
            try {
                order = await service.findOne({ orderNo: orderNo, isDelete: false });
                store = await db.MultiStore.findOne({ storeId: order?.storeId, isDelete: false })
                const decoded = jwt.verify(req.query?.tamaraToken, store?.envs?.TAMARA_NOTIFICATION_TOKEN);
            } catch (err) {
                // err
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Invalid Tamara Token"
                    }
                );
            }
        } else {
            order = await service.findOne({ orderNo: orderNo, isDelete: false });
            store = await db.MultiStore.findOne({ storeId: order?.storeId, isDelete: false })
        }

        try {
            if (body?.order_id) {
                const response = await axios.post(
                    `${store?.envs?.TAMARA_API_URL}/orders/${orderId}/authorise`,
                    {},
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TAMARA_API_TOKEN}` }
                    }
                );
                paymentSuccess = true;
            } else {
                const response = await axios.get(
                    `${store?.envs?.TAMARA_API_URL}/orders/${orderId}`,
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TAMARA_API_TOKEN}` }
                    }
                );
                if (response?.data?.status == "approved") {
                    const response = await axios.post(
                        `${store?.envs?.TAMARA_API_URL}/orders/${orderId}/authorise`,
                        {},
                        {
                            headers: { Authorization: `Bearer ${store?.envs?.TAMARA_API_TOKEN}` }
                        }
                    );
                    paymentSuccess = true;
                } else {
                    if (response?.data?.status != "new") {
                        helper.deliverResponse(
                            res,
                            200,
                            {},
                            {
                                error_code: messages.SUCCESS_RESPONSE.error_code,
                                error_message: "Payment Success"
                            }
                        );
                        return
                    }
                }
            }

        } catch (error) {
            console.log(error?.response.data)
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "error caught while payment TAMARA"
                }
            );
        }


        order.tamaraOrderId = orderId;

    } else if (body?.payment_id || body?.order?.reference_id) { // tabby request
        if (body?.payment_id) {
            try {
                order = await service.findOne({ orderNo: body?.order_number_tabby, isDelete: false });
                store = await db.MultiStore.findOne({ storeId: order?.storeId, isDelete: false })
                const payment = await axios.get(
                    `https://api.tabby.ai/api/v2/checkout/${order?.tabbySessionId}`,
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TABBY_TOKEN}` }
                    }
                );
                console.log("web", payment.data)
                if (payment.data?.payment?.status === "CLOSED") {
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.SUCCESS_RESPONSE.error_code,
                            error_message: "Payment Success"
                        }
                    );
                    return
                }
                const response = await axios.post(
                    `https://api.tabby.ai/api/v2/payments/${body?.payment_id}/captures`,
                    {
                        amount: order?.total,
                        reference_id: order?.orderNo
                    },
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TABBY_TOKEN}` }
                    }
                );

                paymentSuccess = true;
            } catch (error) {
                console.log("tabby error")
                console.log(error?.response.data)
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "error caught while payment TABBY"
                    }
                );
            }
        }
        if (body?.order?.reference_id) {
            try {
                order = await service.findOne({ orderNo: body?.order?.reference_id, isDelete: false });
                store = await db.MultiStore.findOne({ storeId: order?.storeId, isDelete: false })
                const payment = await axios.get(
                    `https://api.tabby.ai/api/v2/checkout/${order?.tabbySessionId}`,
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TABBY_TOKEN}` }
                    }
                );
                console.log("webhook", payment.data)
                if (payment.data?.payment?.status === "CLOSED") {
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.SUCCESS_RESPONSE.error_code,
                            error_message: "Payment Success"
                        }
                    );
                    return
                }
                const response = await axios.post(
                    `https://api.tabby.ai/api/v2/payments/${body?.id}/captures`,
                    {
                        amount: order?.total,
                        reference_id: body?.order?.reference_id
                    },
                    {
                        headers: { Authorization: `Bearer ${store?.envs?.TABBY_TOKEN}` }
                    }
                );

                paymentSuccess = true;
            } catch (error) {
                console.log("tabby error")
                console.log(error?.response.data)
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "error caught while payment TABBY"
                    }
                );
            }
        }
    } else {
        if (!body?.orderId || !body?.orderNo) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Order Id or OrderNo Not Found"
                }
            );
        }

        try {
            order = await service.findOne({ orderNo: body.orderNo, isDelete: false });
            store = await db.MultiStore.findOne({ storeId: order?.storeId, isDelete: false })
            let paymentBody;
            if (body?.cartType == "subscription") {
                paymentBody = {
                    apiOperation: "CAPTURE",
                    order: {
                        Id: body.orderId
                    },
                    transaction: {
                        amount: order?.total,
                        currency: "AED",
                        TransactionReference: order?.orderNo,
                        description: `Order.No: ${order?.orderNo} Payment`
                    }
                };
            } else {
                paymentBody = {
                    apiOperation: "SALE",
                    order: {
                        Id: body.orderId
                    }
                };
            }

            const response = await axios.post(
                store?.envs?.NOON_API_URL,
                paymentBody,
                {
                    headers: { Authorization: store?.envs?.NOON_API_KEY }
                }
            );
            if (response?.data?.resultCode === 0) paymentSuccess = true;
        } catch (error) {
            console.log(error)
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "error caught while payment"
                }
            );
            return;
        }

    }


    try {

        if (paymentSuccess) {
            let cart = null;
            if (body?.cartType != "subscription") {
                cart = await cartService.update(
                    { _id: order?.cart, isDelete: false, isActive: true, isPurchased: false },
                    { isPurchased: true }
                );
            }
            const customer = await customerService.findOne({
                _id: order?.customer,
                isDelete: false,
                isActive: true
            });
            if (body?.cartType == "subscription") {
                const subscribeSummary = await summaryService.findOne({
                    customer: customer?._id,
                    isDelete: false
                });
                const plan = await subscribePlanService.findOne({
                    _id: subscribeSummary?.plan,
                    isDelete: false
                });

                const duration = plan?.duration?.duration;
                const durationType = plan?.duration?.type;
                let nextOrderDate = null;
                let currentDate = new Date();
                if (durationType === "Weekly") {
                    nextOrderDate = new Date(currentDate.getTime() + duration * 604800000); // 604800000 = 7 * 24 * 60 * 60 * 1000 (milliseconds in a week)
                } else if (durationType === "Monthly") {
                    nextOrderDate = new Date(currentDate.getTime() + duration * 2629800000); // 2629800000 = 30.44 * 24 * 60 * 60 * 1000 (approximate milliseconds in a month)
                } else if (durationType === "Yearly") {
                    nextOrderDate = new Date(currentDate.getTime() + duration * 31557600000); // 31557600000 = 365 * 24 * 60 * 60 * 1000 (approximate milliseconds in a year)
                }
                const subscriptionPayload = {
                    customer: customer._id,
                    product: order.products[0].product,
                    plan: subscribeSummary?.plan,
                    order: order?._id,
                    subscriptionIdentifier: response?.data?.result?.subscription?.identifier,
                    nextOrderDate: nextOrderDate
                };
                const subscribe = await subscriptionService.create(subscriptionPayload);
                if (subscribe instanceof Error) {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "subscription not created"
                        }
                    );
                    return;
                }
            }
            const addressDetails = await addressService.findOne({
                _id: order?.address,
                isDelete: false,
                isActive: true
            });
            order.paymentStatus = "PAID";
            order.isActive = true;
            order.orderStatus = "PLACED";
            const orderHistory = order?.history || [];
            orderHistory.push({
                status: "PLACED",
                date: new Date()
            });
            order.history = orderHistory;
            await service.update({ _id: order?._id }, order);
            if (order?.isTryCart) {
                await customerService.update({ _id: customer?._id }, { tryCart: [] });
            }
            let products = [];
            let emailProducts = [];

            for (let product of order?.products || []) {


                const productDetails = await productService.findOne({
                    _id: product?.product,
                    isActive: true,
                    isDelete: false
                });

                emailProducts.push({
                    product: product?.product,
                    quantity: product?.quantity,
                    price: product?.price,
                    total: product?.total,
                    currency: product?.currency,
                    name: product?.product?.name?.en,
                    size: product?.size,
                    contactSize: product?.contactSize,
                    thumbnail: productDetails?.thumbnail,
                    lensDetails: product?.lensDetails,
                    contactLens: product?.contactLens
                });

                products.push({
                    name: productDetails?.name.en,
                    quantity: product?.quantity,
                    thumbnail: productDetails?.thumbnail,
                    total: product?.total
                });

                if (!order?.isTryCart) {
                    const remainingStock = productDetails?.stock - product?.quantity;
                    await productService.update(
                        { _id: product?.product, isActive: true, isDelete: false },
                        { stock: remainingStock < 0 ? 0 : remainingStock }
                    );
                }
            }

            console.log(emailProducts)

            const ccs = process.env.ORDER_CCS?.split(" ")
            if (customer.email || addressDetails?.email) {
                const orderdata = {
                    status: "Placed",
                    order: order,
                    address: addressDetails,
                    products: emailProducts
                };
                const mailOptions = {
                    from: process.env.EMAIL_USER,
                    to: customer?.email,
                    subject: "Order Placed",
                    html: await template.orderPlaced(orderdata),
                    attachments: [
                        {
                            filename: "logo-light-full.png",
                            path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                            cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                        }
                    ]
                };

                // emailHelper.sendMail(mailOptions, (error, info) => {
                //     if (error) {
                //     } else {
                //     }
                // });
            }

            // const shipping = await shippingCreate(order, addressDetails, order.paymentMethod);

            const orderdata = {
                status: "Placed",
                order: order,
                address: addressDetails,
                products: emailProducts
            };

            const adminMailOptions = {
                from: process.env.EMAIL_USER,
                to: ccs,
                subject: `New Order Received - Order #${orderdata?.order?.orderNo}`,
                html: await template.orderPlacedAdmin(orderdata),
                attachments: [
                    {
                        filename: "logo-light-full.png",
                        path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                        cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                    }
                ]
            }

            emailHelper.sendMail(adminMailOptions, (error, info) => {
                if (error) {
                    console.log(error)
                } else {
                    console.log(info)
                }
            });

            const digitalReceipt = await sendDigitalReceipt(order, customer);

            helper.deliverResponse(
                res,
                200,
                { isGuest: customer.isGuest, order },
                {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: "Payment Success"
                }
            );
        } else {
            const order = await service.update(
                { orderNo: body.orderNo, isDelete: false },
                { isDelete: true }
            );
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Payment Failed"
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: 1,
                error_message: "error caught while payment"
            }
        );
    }
};

exports.emptyTryCart = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const { customerId } = res?.locals?.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isActive: true,
            isDelete: false
        });
        const order = await service.findOne({
            orderNo: body?.orderNo,
            customer: customerDetails?._id,
            isDelete: false,
            isActive: true,
            storeId: storeid
        });
        if (order) {
            if (body.type == "all") {
                order.products.map((product) => {
                    product.notInterested = true;
                });
                order.isDelete = true;
                await service.update({ _id: order?._id, isDelete: false, isActive: true }, order);
                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: 0,
                        error_message: "Cart emptied successfully"
                    }
                );
            } else {
                const productIndex = order.products.findIndex((product) => {
                    if (body?.size)
                        return (
                            String(product?.product) === String(body?.product) &&
                            String(product?.size) === String(body?.size)
                        );
                    return String(product?.product) === String(body?.product);
                });

                if (productIndex !== -1) {
                    order.products[productIndex].notInterested = true;
                    await service.update(
                        { _id: order?._id, isActive: true, isDelete: false },
                        order
                    );

                    const allNotInterested = order.products.every(
                        (product) => product.notInterested === true
                    );
                    if (allNotInterested) {
                        order.isDelete = true;
                        await service.update({ _id: order?._id, storeId: storeid }, { isDelete: true });
                    }

                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: 0,
                            error_message: "Product marked as not interested"
                        }
                    );
                } else {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Product not found in the cart"
                        }
                    );
                }
                // order.products = order.products.filter((product) => {
                //     if (body?.size) return String(product?.product) !== String(body?.product) || String(product?.size) !== String(body?.size);
                //     return String(product?.product) !== String(body?.product);
                // });
                // const orderUpdate = await service.update({ _id: order?._id, isActive: true, isDelete: false }, order);
                // if (orderUpdate.products.length == 0) await service.update({ _id: order?._id }, { isDelete: true });
                // helper.deliverResponse(
                //     res,
                //     200,
                //     {},
                //     {
                //         error_code: 0,
                //         error_message: "Product removed successfully",
                //     }
                // );
            }
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.ORDERS.NOT_FOUND.error_code,
                    error_message: messages.ORDERS.NOT_FOUND.error_message
                }
            );
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.salesReport = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const { fromDate, toDate } = body;

        const matchQuery = {
            isDelete: false,
            isActive: true,
            isTryCart: false,
            orderStatus: "DELIVERED",
            storeId: storeid
        };

        if (fromDate) {
            matchQuery.orderDate = { $gte: new Date(fromDate) };
        }

        if (toDate) {
            matchQuery.orderDate = {
                ...matchQuery.orderDate,
                $lte: new Date(toDate)
            };
        }

        const orders = await service.aggregate([
            {
                $match: matchQuery
            },
            {
                $lookup: {
                    from: "customers", // Replace with the actual name of your customer collection
                    localField: "customer",
                    foreignField: "_id",
                    as: "customerDetails"
                }
            },
            {
                $lookup: {
                    from: "addresses",
                    localField: "address",
                    foreignField: "_id",
                    as: "address"
                }
            },
            {
                $sort: {
                    orderDate: -1 // Sort by orderDate in descending order
                }
            },
            {
                $project: {
                    orderNo: 1,
                    orderDate: 1,
                    customer: { $arrayElemAt: ["$customerDetails.name", 0] },
                    mobile: { $arrayElemAt: ["$customerDetails.mobile", 0] },
                    address: { $arrayElemAt: ["$address.name", 0] },
                    total: 1,
                    productQuantity: {
                        $sum: "$products.quantity"
                    }
                }
            }
        ]);
        helper.deliverResponse(res, 200, orders, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

// exports.salesReport = async (req, res) => {
//     try {
//         const { body } = req;
//         const orders = await service.aggregate([
//             {
//                 $match: {
//                     isDelete: false,
//                     isActive: true,
//                     isTryCart: false,
//                     orderStatus: "DELIVERED"
//                 }
//             },
//             {
//                 $lookup: {
//                     from: "customers", // Replace with the actual name of your customer collection
//                     localField: "customer",
//                     foreignField: "_id",
//                     as: "customerDetails"
//                 }
//             },
//             {
//                 $project: {
//                     orderNo: 1,
//                     orderDate: 1,
//                     customer: { $arrayElemAt: ["$customerDetails.name", 0] },
//                     mobile: { $arrayElemAt: ["$customerDetails.mobile", 0] },
//                     total: 1,
//                     productQuantity: {
//                         $sum: "$products.quantity"
//                     }
//                 }
//             }
//         ]);
//         console.log("orders :: ", orders);
//         helper.deliverResponse(res, 200, orders, {
//             error_code: messages.SUCCESS_RESPONSE.error_code,
//             error_message: messages.SUCCESS_RESPONSE.error_message
//         });
//     } catch (error) {
//         console.log("error :: ", error);
//         helper.deliverResponse(res, 422, error, {
//             error_code: messages.SERVER_ERROR.error_code,
//             error_message: messages.SERVER_ERROR.error_message
//         });
//     }
// };

exports.topSellingReport = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const { fromDate, toDate, brand } = body;

        // Aggregation pipeline to get the top 10 selling products and their sold count
        let pipeline = [
            {
                $match: { storeId: storeid }
            },
            {
                $unwind: "$products" // Unwind the products array
            },
            {
                $lookup: {
                    from: "products", // Replace with your product collection name
                    localField: "products.product",
                    foreignField: "_id",
                    as: "productDetails"
                }
            },
            {
                $unwind: "$productDetails"
            },
            {
                $lookup: {
                    from: "brands", // Replace with your brand collection name
                    localField: "productDetails.brand",
                    foreignField: "_id",
                    as: "brandDetails"
                }
            },
            {
                $unwind: "$brandDetails"
            },
            {
                $group: {
                    _id: "$productDetails._id", // Group by product ID
                    sold_count: { $sum: "$products.quantity" }, // Sum the quantity sold
                    brand: { $first: "$brandDetails" } // Include brand information
                }
            },
            {
                $sort: { sold_count: -1 } // Sort by sold count in descending order
            }
        ];

        // Add date filter conditions to the pipeline
        if (fromDate && toDate) {
            pipeline.unshift({
                $match: {
                    createdAt: {
                        $gte: new Date(fromDate),
                        $lte: new Date(toDate)
                    }
                }
            });
        } else if (fromDate) {
            pipeline.unshift({
                $match: {
                    createdAt: {
                        $gte: new Date(fromDate)
                    }
                }
            });
        } else if (toDate) {
            pipeline.unshift({
                $match: {
                    createdAt: {
                        $lte: new Date(toDate)
                    }
                }
            });
        }

        // Add brand filter condition to the pipeline
        if (brand) {
            pipeline.push({
                $match: {
                    "brand.name.en": brand
                }
            });
        }

        const result = await service.aggregate(pipeline);
        let data = [];

        for (let item of result) {
            const product = await productService.findOne({ _id: item._id, isDelete: false });
            if (product) {
                data.push({
                    _id: product?._id,
                    name: product?.name.en,
                    img: product?.thumbnail,
                    sold: item?.sold_count,
                    sku: product?.sku,
                    brand: product?.brand?.name?.en // Assuming 'name' field exists in the brand document
                });
            }
        }
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.verifySubscription = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { orderId, orderNo } = req.query;

        // Validate request data
        if (!orderId || !orderNo) {
            return res.status(400).json({ message: "Invalid request data" });
        }

        const order = await service.findOne({ orderNo, isDelete: false, storeId: storeid });
        const subscribeSummary = await summaryService.findOne({
            customer: order?.customer,
            isDelete: false,
            storeId: storeid
        });
        const plan = await subscribePlanService.findOne({
            _id: subscribeSummary?.plan,
            isDelete: false,
            storeId: storeid
        });

        if (!order || !subscribeSummary || !plan) {
            return handleError(res, new Error("Order, summary, or plan not found"));
        }

        const authorizePayload = {
            apiOperation: "AUTHORIZE",
            order: {
                Id: orderId
            }
        };

        const authorizeResponse = await axios.post(paymentUrl, authorizePayload, {
            headers: {
                Authorization: paymentKey
            }
        });


        if (authorizeResponse.data.resultCode !== 0) {
            return handleError(
                res,
                new Error(`Error authorizing payment: ${authorizeResponse.data.result.message}`)
            );
        }

        const citData = {
            apiOperation: "INITIATE",
            order: {
                amount: order.total,
                currency: "AED",
                reference: order.orderNo,
                channel: "web",
                category: "3ds",
                name: `Payment for order ${order.orderNo}`
            },
            paymentData: {
                type: "CARD",
                data: {
                    tokenIdentifier: authorizeResponse.data.result.paymentDetails.tokenIdentifier
                }
            },
            configuration: {
                PaymentAction: "AUTHORIZE,SALE",
                tokenizeCC: "True",
                returnUrl:
                    `${process.env.FRONTEND_URL}/verify-order?orderNo=` +
                    orderNo +
                    "&cartType=" +
                    "subscription",
                Locale: "en"
            },
            subscription: {
                type: "Recurring",
                amount: order.total,
                paymentFrequency: plan.duration.type,
                description: `Payment for order ${order.orderNo}`,
                agreement: `payment will be auto debited from customer account on each ${plan.duration.duration} ${plan.duration.type}`,
                name: `Payment for Subscription ${order.orderNo}`
            }
        };

        const recurringResponse = await axios.post(paymentUrl, citData, {
            headers: {
                Authorization: paymentKey
            }
        });

        if (recurringResponse.data.resultCode === 0) {
            res.redirect(recurringResponse.data.result.checkoutData.postUrl);
        } else {
            handleError(
                res,
                new Error(`Error initiating recurring: ${recurringResponse.data.result.message}`)
            );
        }
    } catch (error) {
        handleError(res, error);
    }
};

const handleError = (res, error) => {
    res.status(500).json({ message: `Error: ${error.message}` });
};

const sendDigitalReceipt = async (order, customerDetails) => {
    try {
        // const url = "http://app.optculture.com/subscriber/processReceipt.mqrm";
        const url = "https://app.optculture.com/subscriber/processReceipt.mqrm";
        let items = [];
        let gross = 0;
        let totalDiscount = 0;
        let couponType = "Receipt";
        let promotionItems = [];
        for (let product of order?.products) {
            const productDetails = await productService.findOne({
                _id: product?.product,
                isDelete: false
            });
            let discount = Number(product.price) != Number(product.OGPrice) ? (Math.abs(product.price - product.OGPrice) * product?.quantity) : 0
            if (product?.couponAmount && product?.couponAmount > 0) {
                discount += (product?.couponAmount ?? 0);
                couponType = "Item";
                promotionItems.push({
                    "DiscountType": "Item",
                    "ItemCode": productDetails?.sku,
                    "ItemDiscount": (Number(product?.couponAmount) / product?.quantity).toFixed(2),
                    "DiscountAmount": "",
                    "QuantityDiscounted": product?.quantity,
                    "CouponCode": order?.couponCode,
                    "RewardRatio": ""
                })
            }
            totalDiscount += Number(discount)
            gross += Number(product?.OGPrice) * product?.quantity
            items.push({
                H_SYS_ID: productDetails?._id, // Mandatory
                REF_SYS_ID: "",
                COMP_CODE: "",
                TXN_CODE: "",
                H_LOCN_CODE: "", // Mandatory
                H_LOCN_NAME: "UAE", //Mandatory
                H_SM_CODE: "", //Mandatory
                DOC_NO: order?._id, //Mandatory
                DOC_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("YYYY-MM-DD HH:mm:ss"), //Mandatory
                REF_TXN_CODE: "",
                REF_NO: "",
                ITEM_BAR_CODE: "", //Mandatory
                ITEM_CODE: productDetails?.sku, //Mandatory
                ITEM_ANLY_CODE_01: productDetails?.category?.[0]?.name?.en ? `${productDetails?.category?.[0]?.name?.en}` : "", //Mandatory
                ITEM_ANLY_CODE_02: productDetails?.brand?.name?.en ? `${productDetails?.brand?.name?.en}` : "", //Mandatory
                ITEM_ANLY_CODE_03: "",
                ITEM_ANLY_CODE_04: productDetails?.color?.name?.en ? `${productDetails?.color?.name?.en}` : "",
                ITEM_ANLY_CODE_05: product?.size?.name ? `${product?.size?.name}` : "", //Mandatory
                ITEM_ANLY_CODE_06: product?.contactLens?.sphLeft ? `Sph Left: ${product?.contactLens?.sphLeft?.name}` : "",
                ITEM_ANLY_CODE_07: product?.contactLens?.cylLeft ? `Cyl Left: ${product?.contactLens?.cylLeft?.name}` : "",
                ITEM_ANLY_CODE_08: product?.contactLens?.axisLeft ? `Axis Left: ${product?.contactLens?.axisLeft?.name}` : "",
                ITEM_ANLY_CODE_09: product?.contactLens?.sphRight ? `Sph Right: ${product?.contactLens?.sphRight?.name}` : "",
                ITEM_ANLY_CODE_10: product?.contactLens?.cylRight ? `Cyl Right: ${product?.contactLens?.cylRight?.name}` : "",
                ITEM_ANLY_CODE_11: product?.contactLens?.axisRight ? `Axis Right: ${product?.contactLens?.axisRight?.name}` : "",
                ITEM_ANLY_CODE_12: product?.contactLens?.multiFocal ? `Addition: ${product?.contactLens?.multiFocal}` : "",
                ITEM_DESC: productDetails?.name?.en, //Mandatory
                UOM_CODE: "PCS", //Mandatory
                INVI_QTY: product?.quantity, //Mandatory
                INVI_RATE: product?.OGPrice, //Mandatory
                GROSS: product?.OGPrice * product?.quantity, //Mandatory
                DIS: discount, //Mandatory
                VAT: (product?.tax * product?.quantity).toFixed(3) || "0.00", //Mandatory
                NET: (product?.OGPrice * product?.quantity) - discount, //Mandatory
                RINVI_QTY: "0", //Mandatory
                RINVI_RATE: "0.00", //Mandatory
                RGROSS: "0.00", //Mandatory
                RDIS: "0.00", //Mandatory
                RVAT: "0.00", //Mandatory
                RNET: "0.00", //Mandatory
                H_CR_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("YYYY-MM-DD HH:mm:ss"), //Mandatory
                CUST_NAME: order?.address?.name ?? customerDetails?.name,
                CUST_CODE: customerDetails?._id,
                BILL_MOBILE: order?.address?.mobile ?? customerDetails?.mobile,
                BILL_EMAIL: order?.address?.email ?? customerDetails?.email,
                DIS_OPT: (product?.couponAmount && !isNaN(product?.couponAmount)) ? Number(product?.couponAmount)?.toFixed(2) : "0.00"
            });
        }
        console.log(items)
        let promotion = {};
        if (order?.couponCode && couponType == "Receipt") {
            promotion = {
                "DiscountType": "Receipt",
                "DiscountAmount": order?.savings?.toFixed(2),
                "CouponCode": order?.couponCode
            }
        }
        console.log("items")
        console.log(items)
        let balance = gross - totalDiscount;
        if (order?.couponCode || order?.loyaltyDiscount) {
            if (order?.loyaltyDiscount) {
                balance -= order?.loyaltyDiscount;
            }
            if (order?.couponCode && couponType == "Receipt") {
                balance -= order?.savings
            }
        }
        balance += (order?.paymentCharge ?? 0);
        const payload = {
            Head: {
                // og
                // user: {
                //     userName: "WebcastleQC", // Mandatory field
                //     token: "NGF3TNSBTG8XJ8WG", // Mandatory field
                //     organizationId: "WebcastleQC" // Mandatory field
                // },
                user: {
                    userName: CRM_USERNAME, // Mandatory field
                    token: CRM_TOKEN, // Mandatory field
                    organizationId: CRM_ORG_ID // Mandatory field
                    // userName: "webcastle", // Mandatory field
                    // token: "0J3RIVME081FARD3", // Mandatory field
                    // organizationId: "webcastle" // Mandatory field
                },
                enrollCustomer: "N", // Mandatory field
                isLoyaltyCustomer: "Y", // Mandatory field
                emailReceipt: "Y", // Mandatory field ->If we want send E-receipt through email then it should 'Y' otherwise 'N'
                printReceipt: "N", // Mandatory field
                requestSource: "Orion", // Mandatory field
                requestFormat: "JSON", // Mandatory field
                requestEndPoint: "/processReceipt.mqrm", // Mandatory field
                requestType: "New", // Mandatory field
                receiptType: "SO" // Mandatory field -> If the customer pruchases then receipt type should be'SALE' ,
            },
            Body: {
                Items: items,
                Receipt: {
                    COMP_CODE: "",
                    LOCATION_CODE: "1",
                    LOCN_NAME: "",
                    INV_RCPT_NUM: order?.orderNo, // Mandatory field
                    INV_RCPT_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                        "YYYY-MM-DD HH:mm:ss"
                    ), // Mandatory field
                    INV_BUSINESS_DT: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format(
                        "YYYY-MM-DD HH:mm:ss"
                    ), // Mandatory field
                    INV_RCPT_TM: moment(new Date(), "DD/MM/YYYY hh:mm:ss A").format("DD/MM/YYYY hh:mm:ss a"), // Mandatory field
                    INV_NET_AMT: Number(gross - totalDiscount + (order?.paymentCharge ?? 0) + (order?.shippingCharge ?? 0))?.toFixed(2),
                    INV_NET_TDR_AMT: Number(balance)?.toFixed(2),
                    INV_TAX_AMT: order?.vatAmount,
                    RNET: "0.00",
                    RINV_TAX_AMT: "",
                    // INV_DISC_AMT: order?.savings || "0.00", // Mandatory field
                    INV_DISC_AMT: Number(totalDiscount)?.toFixed(2) || "0.00", // Mandatory field
                    INV_GROSS_AMT: Number(gross)?.toFixed(2), // Mandatory field
                    TXN_CODE: "",
                    CUST_NAME: order?.address?.name ?? customerDetails?.name, // Mandatory field
                    CUST_CODE: customerDetails?._id?.toString(), // Mandatory field
                    BILL_MOBILE: order?.address?.mobile ?? customerDetails?.mobile,
                    BILL_EMAIL: order?.address?.email ?? customerDetails?.email, // Mandatory field
                    DocSID: order?._id?.toString() // Mandatory field
                },
            },
            OptcultureDetails: {
                MembershipNumber: "", // Mandatory field
                Email: customerDetails?.email, // Mandatory field
                Phone: customerDetails?.mobile, // Mandatory field
                LoyaltyRedeem: {
                    DiscountAmount: order?.loyaltyDiscount?.toFixed(2) ?? "" // Mandatory field
                },
                LoyaltyRedeemReversal: "0",
                Promotions: order?.couponCode ? couponType == "Receipt" ? [promotion] : promotionItems : []
            }
        };
        if (order.paymentMethod === "ONLINE") {
            payload.Body["Payment"] = {
                "VISA_CARD": {
                    // Payment details are mandatory
                    CURRENCY_CODE: "AED",
                    TENDER_AMOUNT: order?.total,
                    SOTENDER_AMOUNT: "0.00",
                    RTENDER_AMOUNT: "0.00"
                }
            }
        } else {
            payload.Body["Payment"] = {
                "CASH": {
                    // Payment details are mandatory
                    CURRENCY_CODE: "AED",
                    TENDER_AMOUNT: "0.00",
                    SOTENDER_AMOUNT: "0.00",
                    RTENDER_AMOUNT: "0.00"
                }
            }
            payload.Body.Receipt["COD"] = order?.total;
            payload.Body.Receipt["CHARGES"] = {
                "COD_CHARGES": order?.paymentCharge ?? 0,
                "SHIPPING_CHARGES": order?.shippingCharge ?? 0,
                "HANDELING_CHARGES": 0,
                "CHARGE_01": 0,
                "CHARGE_02": 0,
                "CHARGE_03": 0,
                "CHARGE_04": 0,
                "CHARGE_05": 0
            }
        }
        console.log(payload.Body["Payment"])
        console.log(payload)
        console.log(payload.OptcultureDetails)
        console.log(payload.Body.Receipt["CHARGES"])
        const crmResponse = await axios.post(url, payload);
        console.log("crmResponse.data");
        console.log(crmResponse.data);
        return crmResponse?.data;
    } catch (error) {
        console.log(error);
    }
};

function fixResponseString(str) {
    // Remove the trailing comma inside objects
    let fixedStr = str.replace(/,(\s*\})/g, "$1");

    // Add a comma after "status": "success"
    fixedStr = fixedStr.replace(/("status": "success")(\n)/, "$1,$2");

    // Ensure the closing brace for the entire JSON is correctly placed
    fixedStr = fixedStr.trim();

    // Check if the last character is a closing brace; if not, append one
    if (!fixedStr.endsWith("}")) {
        fixedStr += "\n}";
    }

    return fixedStr;
}
