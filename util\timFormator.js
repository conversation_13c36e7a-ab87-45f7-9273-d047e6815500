const options = {
    timeZone: 'Asia/Dubai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    // hour: '2-digit',
    // minute: '2-digit',
    // second: '2-digit',
    // hour12: true,
};

exports.formatDate = function(date, options = {
    timeZone: 'Asia/Dubai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
}) {
    const formatter = new Intl.DateTimeFormat('en-GB', options);
    return formatter.format(date);
}
