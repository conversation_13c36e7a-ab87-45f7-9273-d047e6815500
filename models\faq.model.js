const mongoose = require('mongoose');

const faqSchema = new mongoose.Schema({
    title: {
        en: { type: String, required: true },
        ar: { type: String },
    },
    description: {
        en: { type: String, required: true },
        ar: { type: String },
    },
    faq: [{
        question: {
            en: { type: String, required: true },
            ar: { type: String },
        },
        answer: {
            en: { type: String, required: true },
            ar: { type: String },
        },
    }],
    type: { type: String, required: true, enum: ['insurance', 'contactLens'] },
    refid: { type: String, required: true },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String },
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('faqs', faqSchema);