const express = require('express');
const router = express.Router();
const controller = require('../../controllers/lens.power.controller');

module.exports = () => {
    router.post('/create-lens-power', controller.create);
    router.get('/get-lens-powers/:type', controller.list);
    router.get('/lens-power-details/:refid', controller.detail);
    router.put('/update-lens-power/:refid', controller.update);
    router.put('/delete-lens-power/:refid', controller.delete);

    return router;
}