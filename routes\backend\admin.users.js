const express = require('express')
const router = express.Router()
const controller = require('../../controllers/admin.users.controller')
const upload = require('../../util/upload')
const roleController = require('../../controllers/roles.controller')
const authorize = require('../../middlewares/authorize')

module.exports = () => {
    router.post('/create-admin', upload.single('image'), controller.validate('create'), controller.create)
    router.get('/get-admins', controller.getAdminUsers)
    router.get('/admin-details/:refid', controller.adminUserdetails)
    router.post('/update-admin', upload.single('image'), controller.update)
    router.put('/delete-admin/:refid', controller.delete)
    router.get('/get-user', controller.findAdmin)

    router.post('/admin-login', controller.validate('login'), controller.adminLogin)

    router.post('/roles', roleController.create)
    router.post('/get-roles', roleController.find)
    router.get('/roles/:refid', roleController.findOne)
    router.put('/roles/:refid', roleController.update)
    router.put('/delete-roles/:refid', roleController.delete)

    router.post('/check-permission', authorize.verifyToken, controller.checkPermission)
    router.post('/forgot-password', controller.forgotPassword)
    router.post('/reset-password', controller.resetPassword)

    return router;
}