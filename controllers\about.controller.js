const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require('../services/about.service')
const axios = require('axios');
const sharp = require('sharp');
const { s3, bucketName } = require('../config/s3');
const { uploadToS3 } = require('../util/uploadToS3');
const { uploadWebp } = require('../util/uploadWebp');
const generateUniqueNumber = require('../util/getRefid');

async function uploadTopImage(req, body) {
    if (req.files?.topImage){
        const name = `about-us/top/${Date.now()}-${req.files["topImage"][0]?.originalname}.webp`
        const { key, fileURL } = await uploadWebp(req.files["topImage"][0]?.path, name);
        body.sectionOne.image = key
    }
}

async function uploadSeoImage(req, body) {
    if (req.files?.ogImage){
        const name = `about-us/ogImage/${Date.now()}-${req.files["ogImage"][0]?.originalname}.webp`
        const { key, fileURL } = await uploadWebp(req.files["ogImage"][0]?.path, name);
        body.seoDetails.ogImage = key
    }
}

async function uploadTimeline(file, body, i) {
    if (file) {
        const name = `about-us/timeline/${Date.now()}-${file?.originalname}.webp`
        const { key, fileURL } = await uploadWebp(file?.path, name)
        body.sectionThree.timeline[i].image = key
    }
}

async function uploadTimelineImages(req, body) {
    if (req?.files?.timelineImage) {
        let timeline = [];
        for (let i = 0; i < body?.sectionThree.timeline.length; i++) {
            timeline.push(uploadTimeline(req.files["timelineImage"][i], body, i))
        }
        await Promise.all(timeline)
    }
}

async function uploadBottom(file, body, i) {
    if (file) {
        const name = `about-us/bottom/${Date.now()}-${file?.originalname}.webp`
        const { key, fileURL } = await uploadWebp(file?.path, name)
        body.sectionFour.data[i].image = key;
    }
}

async function uploadBottomImages(req, body) {
    if (req?.files?.bottomImage) {
        let bottomUploads = [];
        for (let i = 0; i < body?.sectionFour.data.length; i++) {
            bottomUploads.push(uploadBottom(req.files["bottomImage"][i], body, i))
        }
        await Promise.all(bottomUploads)
    }
}

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber();
        body.storeId = storeid;

        await Promise.all([
            uploadTopImage(req, body),
            uploadTimelineImages(req, body),
            uploadBottomImages(req, body),
            uploadSeoImage(req, body)
        ])

        const response = await service.create(body)
        if (response) {
            await axios.post(process.env.REVALIDATE, { tag: "about-us" });
            helper.deliverResponse(res, 200, {}, {
                error_code: messages.ABOUT.CREATED.error_code,
                error_message: messages.ABOUT.CREATED.error_message,
            })
        }
    }
    catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.getAboutUs = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ isDelete: false, storeId: storeid })
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.getAbouts = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';

        const response = await service.findOne({ isDelete: false, storeId: storeid });
        if (!response) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        }

        const mapLanguageProps = (item) => {
            const mappedItem = {
                title: item.title[language],
                description: item.description[language],
                image: process.env.DOMAIN + item.image
            };

            if (item.year) {
                mappedItem.year = item.year;
            }

            return mappedItem;
        };

        const data = {
            ...response._doc,
            sectionOne: mapLanguageProps(response.sectionOne),
            sectionThree: {
                title: response.sectionThree.title[language],
                description: response.sectionThree.description[language],
                timeline: response.sectionThree.timeline.map(mapLanguageProps)
            },
            sectionFour: {
                title: response.sectionFour.title[language],
                data: response.sectionFour.data.map(mapLanguageProps)
            },
            sectionTwo: response.sectionTwo.map(item => ({
                title: item.title[language],
                count: item.count,
                symbol: item.symbol
            }))
        };
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.update = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const aboutus = await service.findOne({ isDelete: false, storeId: storeid });

        async function task1(){
            if (req.files?.topImage){
                await uploadTopImage(req, body)
            }else{ 
                body.sectionOne.image = aboutus.sectionOne.image
            }
        }
        

        async function task2(){
            if (req?.files?.timelineImage) {
                for (let i = 0; i < body?.sectionThree.timeline.length; i++) {
                    if (req.files["timelineImage"][i] && req.files["timelineImage"][i]?.originalname != "empty") {
                        await uploadTimeline(req.files["timelineImage"][i], body, i)
                    }else{
                        body.sectionThree.timeline[i].image = aboutus.sectionThree.timeline[i].image
                    }
                }
            }else{
                for(let i in aboutus.sectionThree.timeline){
                    body.sectionThree.timeline[i].image = aboutus.sectionThree.timeline[i].image
                }
            }
        }

        async function task3(){
            if (req?.files?.bottomImage) {
                for (let i = 0; i < body?.sectionFour.data.length; i++) {
                    if (req.files["bottomImage"][i] && req.files["bottomImage"][i]?.originalname != "empty") {
                        await uploadBottom(req.files["bottomImage"][i], body, i)
                    }else{
                        body.sectionFour.data[i].image = aboutus.sectionFour.data[i].image
                    }
                }
            }else{
                for(let i in aboutus.sectionFour.data){
                    body.sectionFour.data[i].image = aboutus.sectionFour.data[i].image
                }
            }
        }

        async function task4(){
            if (req.files?.ogImage){
                await uploadSeoImage(req, body)
            }else{ 
                body.seoDetails.ogImage = aboutus.seoDetails.ogImage
            }
        }

        await Promise.all([task1(), task2(), task3(), task4()]);

        const response = await service.update({ storeId: storeid, isDelete: false }, body);
        if (response) {
            await axios.post(process.env.REVALIDATE, { tag: "about-us" });
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ABOUT.UPDATED.error_code,
                error_message: messages.ABOUT.UPDATED.error_message,
            })
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}