const File = require("../models/image.model");
const fs = require("fs");
const path = require("path");
const { uploadWebp } = require("../util/uploadWebp");
const { uploadToS3 } = require("../util/uploadToS3");

exports.uploadImage = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }

        let videoName = `${Date.now()}-${req.file.originalname}`
        const { key, fileURL } = await uploadToS3(req.file.path, `video/${Date.now()}-${req.file.originalname}`, req.file.mimetype)

        const newVideo = new File({
            videoUrl: key,
            videoName
        });
        await newVideo.save();
        res.json({ videoUrl: key, videoName });
    }
    catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error saving image details" });
    }
}

exports.multipleUpload = async (req, res) => {
    try {
        if (!req.files) {
            return res.status(400).json({ message: "No files uploaded" });
        }

        const uploads = [];
        const imageUrl = [];
        const imageName = [];
        for (let file of req.files) {
            async function upload(file) {
                const name = `multi-upload/${Date.now()}-${file.originalname}`
                const { key } = await uploadWebp(file.path, name)
                imageUrl.push(key)
                imageName.push(`${Date.now()}-${file.originalname}`)
            }
            uploads.push(upload(file))
        }
        await Promise.all(uploads);
        const newImages = new File({
            imageUrl,
            imageName,
        });

        await newImages.save();
        res.json({ imageUrl, imageName });

    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error saving image details" });
        return;
    }
}

exports.uploadFile = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }

        // const fileUrl = req.file.path;
        // const fileName = req.file.filename;

        // const newFile = new File({
        //     videoUrl: fileUrl,
        //     videoName: fileName,
        // });
        // await newFile.save();
        const name = `media-upload/${Date.now()}-${req.file.originalname}`
        const { key } = await uploadWebp(req.file.path, name, req.file.mimetype)
        res.json({ fileUrl: key, fileName: name });
    }
    catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error saving image details" });
    }
}

exports.deleteFile = async (req, res) => {
    try {
        const { filePath } = req.body
        fs.unlinkSync(path.join(__dirname, '../', filePath));
        res.json({ success: true });
    }
    catch (error) {
        if (error.code === "ENOENT") {
            return res.json({ success: true });
        }
        res.status(500).json({ error, message: "Error deleting file" });
    }
}

// exports.multipleUpload = async (req, res) => {
//     try {
//         if (!req.files) {
//             return res.status(400).json({ message: "No files uploaded" });
//         }

//         const imageUrl = req.files.map(file => file.path);
//         const imageName = req.files.map(file => file.filename);

//         const newImages = new File({
//             imageUrl,
//             imageName,
//         });

//         await newImages.save();
//         res.json({ imageUrl, imageName });

//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ message: "Error saving image details" });
//         return;
//     }
// }

exports.uploadVideo = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }
        let videoName = `${Date.now()}-${req.file.originalname}`
        let fileBuffer = fs.readFileSync(req.file.path);
        const { key, fileURL } = await uploadToS3(fileBuffer, `video/${Date.now()}-${req.file.originalname}`, req.file.mimetype)
        const newVideo = new File({
            videoUrl: key,
            videoName
        });
        await newVideo.save();
        res.json({ videoUrl: key, videoName });
    }
    catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error saving image details" });
    }
}