const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const service = require("../services/type.service");
const productServie = require("../services/products.service");

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = (await service.count({})) + 1;
        body.storeId = storeid;
        const response = await service.create(body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.TYPE.CREATED.error_code,
            error_message: messages.TYPE.CREATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.findOne = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const { refid } = req?.params;
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.TYPE.UPDATED.error_code,
            error_message: messages.TYPE.UPDATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.types = async (req, res) => {
    try {
        let { language, storeid } = req?.headers
        language = language ? language : 'en'
        storeid = storeid ? storeid : 'sa'
        const projection = { name: 1, _id: 1 };
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid }, projection);
        if (response?.length > 0) {
            const responseData = response.map(item => {
                return {
                    name: item.name[language],
                    _id: item._id
                }
            })
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 200, [], {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params;
        const type = await service.findOne({ refid, isDelete: false, storeId: storeid });
        const products = await productServie.find({
            type: type?._id,
            isDelete: false,
            storeId: storeid
        });
        if (products?.length > 0) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Type is used in products"
                }
            );
            return;
        }
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.TYPE.DELETED.error_code,
            error_message: messages.TYPE.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};
