const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.ProductHead(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}, page, limit, sort) => {
    try {
        let response = await db.ProductHead.find(query, projection)
            .populate({
                path: "products", populate: [
                    { path: "brand", select: "name slug" },
                    { path: "category", select: "name slug" },
                    { path: "color", select: "name color" },
                    { path: "label", select: "name" },
                ]
            })
            .sort(sort)
            .skip((page - 1) * limit)
            .limit(limit * 1)
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.ProductHead.findOne(query, projection).populate({
            path: "products", populate: [
                { path: "brand", select: "name slug" },
                { path: "category", select: "name slug" },
                { path: "color", select: "name color" },
                { path: "label", select: "name" },
            ]
        })
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.ProductHead.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.ProductHead.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.pagination = async (query, page, limit, projection = {}, sort = {}) => {
    try {
        let response;
        const sortValue = (sort?.type === "price" || sort?.type === "order") ? sort.value : { isSale: -1, position: -1 }
        const addFields = sort.type === "price" ?
            {
                $addFields: {
                    lastPrice: {
                        $cond: [
                            { $and: [{ $ne: ["$products.offerPrice.aed", null] }, { $gt: ["$products.offerPrice.aed", 0] }] },
                            "$products.offerPrice.aed",
                            "$products.price.aed",
                        ]
                    }
                }
            }
            : {
                $addFields: {
                    isSale: {
                        $cond: [
                            {
                                $and: [
                                    { $gt: [{ $size: "$products.labelDetails" }, 0] }, // Ensure the lookup is not empty
                                    { $eq: [{ $arrayElemAt: ["$products.labelDetails.name.en", 0] }, sort.value] }, // Check if name matches "sale"
                                ],
                            },
                            1,
                            0,
                        ],
                    },
                },
            }
        const price = sort.type === "price" ? { $match: { lastPrice: { $ne: null } } } : { $match: { isDelete: false } }
        response = await db.ProductHead.aggregate([
            {
                $match: {
                    ...query,
                }
            },
            {
                $lookup: {
                    from: "products", // Replace with the actual collection name for the referenced labels
                    localField: "products", // Field in Product that references Label
                    foreignField: "_id", // Field in Label that is matched with Product's `label`
                    pipeline: [
                        {
                            $lookup: {
                                from: "labels", // Replace with the actual collection name for the referenced labels
                                localField: "label", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "labelDetails", // The output array with matched label documents,
                            },
                        },
                        {
                            $lookup: {
                                from: "brands", // Replace with the actual collection name for the referenced labels
                                localField: "brand", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "brand", // The output array with matched label documents,
                            },
                        },
                        {
                            $unwind: "$brand"
                        },
                        {
                            $lookup: {
                                from: "category", // Replace with the actual collection name for the referenced labels
                                localField: "category", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "category", // The output array with matched label documents,
                            },
                        },
                        {
                            $lookup: {
                                from: "colors", // Replace with the actual collection name for the referenced labels
                                localField: "color", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "color", // The output array with matched label documents,
                            },
                        },
                        {
                            $unwind: "$color"
                        },
                    ],
                    as: "products", // The output array with matched label documents,
                },
            },
            addFields,
            price,

            {
                $sort: { ...sortValue, "products.createdAt": -1, "products._id": 1 }, // Prioritize `label: sale`, then sort by `_id`
            },
            {
                $skip: (page - 1) * limit
            },
            {
                $limit: limit * 1
            },
        ])
        // }
        return response;
    } catch (error) {
        throw error;
    }
};