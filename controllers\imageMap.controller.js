const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require("../services/imageMap.service");
const axios = require('axios');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req
        body.refid = await service.count({}) + 1;
        body.storeId = storeid;
        const result = await service.create(body);
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
            return
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" })
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.HOME_UPDATED.error_code,
            "error_message": messages.HOME_UPDATED.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.get = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.findOne({ isDelete: false, storeId: storeid })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
            return
        }
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req
        const result = await service.update({ isDelete: false, storeId: storeid }, body);
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
            return
        }
        await axios.post(process.env.REVALIDATE, { tag: "home" })
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.HOME_UPDATED.error_code,
            "error_message": messages.HOME_UPDATED.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        });
    }
}

exports.imageMap = async (req, res) => {
    let { language, storeid } = req.headers;
    language = language ? language : "en";
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.findOne({ isDelete: false, storeId: storeid })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
            return
        }
        const data = {
            image: process.env.DOMAIN + result?.image || null,
            items: result?.items.map((item) => {
                return {
                    ...item?._doc,
                    title: item?.title?.[language],
                    buttonText: item?.buttonText?.[language],
                    image: process.env.DOMAIN + item.image || null
                }
            })
        }
        helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}