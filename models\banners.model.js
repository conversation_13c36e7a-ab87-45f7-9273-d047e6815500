const mongoose = require('mongoose');

const bannerSchema = new mongoose.Schema({
    title: { type: String },
    refid: { type: String, required: true },
    type: { type: String },
    files: [{
        file: { type: String, required: true },
        mobileFile: { type: String },
        redirectionType: { type: String, enum: ['category', 'product', 'collection', 'brand', 'page'] },
        redirection: { type: String },
        title: { type: String },
        description: { type: String },
        buttonText: { type: String },
    }],
    count: { type: String },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'admin.users' },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('banners', bannerSchema);