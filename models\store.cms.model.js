const mongoose = require('mongoose')

const storeCmsSchema = new mongoose.Schema({
    content: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('store.cms', storeCmsSchema)