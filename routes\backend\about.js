const express = require('express')
const router = express.Router()
const controller = require('../../controllers/about.controller')
const upload = require('../../util/upload')
const { addStoreId } = require('../../middlewares/addStoreId')

module.exports = () => {
    router.post('/create-about', upload.fields([{ name: 'topImage', maxCount: 1 }, { name: 'timelineImage', maxCount: 10 }, { name: 'bottomImage', maxCount: 5 }, { name: 'ogImage', maxCount: 1 }]), controller.create)
    router.get('/get-abouts', controller.getAboutUs)
    router.post('/update-about', upload.fields([{ name: 'topImage', maxCount: 1 }, { name: 'timelineImage', maxCount: 10 }, { name: 'bottomImage', maxCount: 5 }, { name: 'ogImage', maxCount: 1 }]), controller.update)

    return router;
}