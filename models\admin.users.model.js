const mongoose = require('mongoose');

const adminUserSchema = new mongoose.Schema({
    name: { type: String, required: true },
    refid: { type: String, required: true },
    email: { type: String, required: true },
    password: { type: String, required: true },
    countryCode: { type: String, required: true },
    mobile: { type: String, required: true },
    role: { type: mongoose.Schema.Types.ObjectId, ref: 'roles' },
    image: { type: String },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    resetToken: { type: String },
    resetExpiry: { type: Date },
    countryCodeLabel: { type: String },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('admin.users', adminUserSchema);