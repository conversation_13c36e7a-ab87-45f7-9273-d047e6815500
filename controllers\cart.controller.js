const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/cart.service");
const productService = require("../services/products.service");
const customerService = require("../services/customer.service");
const couponService = require("../services/coupons.service");
const jwt = require("jsonwebtoken");
const { KEYS } = require("../config/constants/key");
const lensEnquiryService = require("../services/lens.enquiries.service");
const sizeService = require("../services/size.service");
const contactSizeService = require("../services/contact.size.service");
const lensBrandService = require("../services/lens.brand.service");
const orderService = require("../services/order.service");
const axios = require("axios");
const crypto = require("crypto");
const moment = require("moment");
const bcrypt = require("bcrypt");
const objectHash = require("object-hash");
const tryCartService = require("../services/trycart.service");
const addressService = require("../services/address.service");
const lensPowerService = require("../services/lens.power.service");
const contactLensPowerService = require("../services/contactLens.power.service");
const db = require('../models/index');
const createCsvWriter = require("csv-writer").createObjectCsvWriter;

const emailHelper = require('../util/emailHelper')
const template = require('../util/templates')

const additionalFeeService = require("../services/paymentMethodFee.service");
const { CRM_USERNAME, CRM_TOKEN, CRM_ORG_ID } = require("../config/constants/crm");

function isLensPriceable(lensData) {
    if (lensData?.name != "0.00" && lensData?.name != "None" && lensData?.name != "none" && lensData?.name != "NONE") {
        return true;
    }
}

exports.validate = (method) => {
    switch (method) {
        case "addToCart": {
            return [body("product").exists().withMessage("Product id is required"), body("quantity").exists().withMessage("Quantity is required")];
        }
        case "buyWithLens": {
            return [
                body("vision").exists().withMessage("Vision is required"),
                body("product").exists().withMessage("Product is required"),
                body("quantity").exists().withMessage("Quantity is required"),
                body("brand").exists().withMessage("Brand is required"),
            ];
        }
    }
};

exports.cartList = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({
            isDelete: false,
            isActive: true,
            isPurchased: false,
            storeId: storeid,
            customer: { $exists: true },
        });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.exportCart = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({
            isDelete: false,
            isActive: true,
            isPurchased: false,
            customer: { $exists: true },
            storeId: storeid
        });
        const items = [];
        for (let [key, cart] of response.entries()) {
            // if(!cart?.customer) continue;
            items.push({
                no: key + 1,
                customer: cart?.customer?.name ?? "Guest",
                mobile: cart?.customer?.mobile,
                email: cart?.customer?.email,
                total: cart?.total,
                products: `${cart?.products?.map((product) => product?.product?.sku).join(', ')}`,
            });
        }

        const header = [
            { id: "no", title: "No" },
            { id: "customer", title: "Customer" },
            { id: "mobile", title: "Mobile" },
            { id: "email", title: "Email" },
            { id: "total", title: "Total" },
            { id: "products", title: "Products" },
        ]

        const csvWriter = createCsvWriter({
            path: "cart.csv",
            header,
            alwaysQuote: true,
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="cart.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('cart.csv');

    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.cartDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const response = await service.findOne({
            refid: refid,
            isDelete: false,
            storeId: storeid
        });
        const products = [];
        if (response.products.length > 0) {
            for (let product of response?.products) {
                const productDetails = await productService.findOne({
                    _id: product?.product,
                    isDelete: false,
                });
                const sizeDetails = await sizeService.findOne({
                    _id: product?.size,
                    isDelete: false,
                });
                products.push({
                    product: productDetails,
                    quantity: product?.quantity,
                    currency: product?.currency,
                    priceTotal: product?.priceTotal,
                    size: sizeDetails,
                });
            }
            response.products = products;
        }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addToCart = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }

        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";

        const { authorization, devicetoken } = req.headers;

        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) customerId = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
                isActive: true,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        }

        let { body } = req;

        const [productDetails, translation] = await Promise.all([
            productService.findOne({
                _id: body?.product,
                isDelete: false,
                storeId: storeid
            }),
            db.Texts.findOne({ storeId: storeid })
        ])

        let { contactLens } = cartDetails?.products?.find((product) => product?.product?._id?.toString() == body?.product?.toString())
        let flag = false;
        if (contactLens) {
            if (contactLens?.sphLeft) flag = isLensPriceable(contactLens?.sphLeft);
            if (contactLens?.sphRight) flag = isLensPriceable(contactLens?.sphRight);
            if (contactLens?.cylLeft) flag = isLensPriceable(contactLens?.cylLeft);
            if (contactLens?.cylRight) flag = isLensPriceable(contactLens?.cylRight);
            if (contactLens?.axisLeft) flag = isLensPriceable(contactLens?.axisLeft);
            if (contactLens?.axisRight) flag = isLensPriceable(contactLens?.axisRight);
        }

        let totalPrice;
        let sizeDetails;
        let stock;
        let price;

        price = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;
        if (flag) price += (productDetails?.powerPrice ?? 0);
        totalPrice = price * (contactLens?.multiple ? 2 * body?.quantity : body?.quantity);
        stock = productDetails?.stock;

        let tryCartAmount = null;

        if (stock >= body?.quantity) {
            if (body?.orderNo) {
                const updatedOrder = await orderService.pull(
                    {
                        orderNo: body?.orderNo,
                        isTryCart: true,
                        isDelete: false,
                        isActive: true,
                        storeId: storeid
                    },
                    { $pull: { products: { product: body?.product } } }
                );

                tryCartAmount = updatedOrder?.total;

                if (updatedOrder && updatedOrder?.products?.length === 0) {
                    await orderService.update({ orderNo: body?.orderNo, storeId: storeid }, { isDelete: false });
                }
            }

            if (!cartDetails) {
                const count = await service.count({});
                let products = [];
                if (!isGuest) body.customer = customerDetails?._id;
                else body.devicetoken = devicetoken;

                // body.customer = customerDetails?._id;
                body.products = products;
                body.refid = count + 1;
                body.total = tryCartAmount ? (totalPrice - tryCartAmount).toFixed(2) : totalPrice.toFixed(2);
                body.baseTotal = totalPrice.toFixed(2);
                body.date = { added: new Date().toISOString() };
                body.storeId = storeid
                products.push({
                    product: productDetails?._id,
                    quantity: body?.quantity,
                    priceTotal: totalPrice.toFixed(2),
                    currency: "AED",
                    fromTryCart: body?.orderNo ? true : false,
                    orderNo: body?.orderNo ? body?.orderNo : null,
                });
                // if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
                const cart = await service.create(body);
                if (cart) {
                    helper.deliverResponse(res, 200, cart, {
                        error_code: messages.CART.ADDED.error_code,
                        // error_message: messages.CART.ADDED.error_message,
                        error_message: translation?.popup?.cartAdd[language],
                    });
                } else {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Failed to add cart",
                        }
                    );
                }
            } else {
                const cartid = cartDetails?.refid;
                let updatedProduct = false;

                if (cartDetails?.couponApplied) {
                    await removeCoupon(cartDetails, customerDetails);
                }

                for (let product of cartDetails?.products) {
                    if (String(product._id) == String(body?.lensId)) {
                        // if (String(product.product?._id) === String(productDetails?._id)) {
                        if (productDetails?.stock < product.quantity + body?.quantity)
                            return helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                                    error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                                }
                            );
                        product.quantity += body?.quantity;
                        let productPrice = price;
                        if (product?.lensSum) productPrice = product?.lensSum + productPrice;
                        product.priceTotal = Number((contactLens?.multiple ? 2 * product?.quantity : product?.quantity) * productPrice).toFixed(2);
                        updatedProduct = true;
                        break;
                        // }
                    }
                }

                if (updatedProduct) {
                    const updatedCartData = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                    console.log(updatedCartData)
                    if (updatedCartData) {
                        let totalSum = 0;
                        const cartProducts = updatedCartData?.products;
                        for (let product of cartProducts) {
                            totalSum += Number(product?.priceTotal);
                        }

                        let cartTotal = totalSum.toFixed(2);
                        if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
                        if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
                        if (tryCartAmount) {
                            updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                            cartTotal = (totalSum - tryCartAmount).toFixed(2);
                        }

                        let cartData = {
                            tryCartDeduct: updatedCartData?.tryCartDeduct,
                            baseTotal: totalSum.toFixed(2),
                            total: cartTotal,
                            savings: 0,
                        };
                        // const cartData = {
                        //     baseTotal: totalSum.toFixed(2),
                        //     total: updatedCartData?.savings ? (totalSum - updatedCartData?.savings).toFixed(2) : totalSum.toFixed(2),
                        // };

                        if (updatedCartData?.couponApplied) {
                            const { savings, total, products, error } = await reApplyCoupon({ ...updatedCartData?._doc, ...cartData }, customerDetails, res);
                            if (error) {
                                console.log(error)
                                return;
                            }
                            cartData.total = Number(total) - Number(savings);
                            cartData.savings = Number(savings);
                            cartData.products = products;
                            if (updatedCartData?.loyaltyApplied) cartData.total = (cartData.total - updatedCartData?.loyaltyAmount).toFixed(2);
                        }

                        const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                        if (result) {
                            helper.deliverResponse(res, 200, result, {
                                error_code: messages.CART.ADDED.error_code,
                                // error_message: messages.CART.ADDED.error_message,
                                error_message: translation?.popup?.cartAdd[language],
                            });
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                        }
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to update cart",
                            }
                        );
                    }
                } else {
                    cartDetails.products.push({
                        product: productDetails?._id,
                        quantity: body?.quantity,
                        priceTotal: totalPrice.toFixed(2),
                        currency: "AED",
                    });

                    const updatedCart = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                    if (updatedCart) {
                        let totalSum = 0;
                        const cartProducts = updatedCart?.products;
                        for (let product of cartProducts) {
                            totalSum += Number(product?.priceTotal);
                        }

                        let cartTotal = totalSum.toFixed(2);
                        if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
                        if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
                        if (tryCartAmount) {
                            updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                            cartTotal = (totalSum - tryCartAmount).toFixed(2);
                        }

                        const cartData = {
                            tryCartDeduct: updatedCart?.tryCartDeduct,
                            baseTotal: totalSum.toFixed(2),
                            total: cartTotal,
                        };

                        // const cartData = {
                        //     baseTotal: totalSum.toFixed(2),
                        //     total: updatedCart?.savings ? (totalSum - updatedCart?.savings).toFixed(2) : totalSum.toFixed(2),
                        // };

                        const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                        if (result) {
                            helper.deliverResponse(res, 200, result, {
                                error_code: messages.CART.ADDED.error_code,
                                // error_message: messages.CART.ADDED.error_message,
                                error_message: translation?.popup?.cartAdd[language],
                            });
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                        }
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to add new product to cart",
                            }
                        );
                    }
                }
            }

        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                    error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                }
            );
        }

    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.getCart = async (req, res) => {
    try {
        const { authorization, devicetoken, language, storeid } = req.headers;
        let { shipping } = req.query;
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
        } else if (devicetoken) {
            isGuest = true;
        }
        if (!isGuest) {
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isActive: true,
                isDelete: false,
                isPurchased: false,
                storeId: storeid
            });
        } else {
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isActive: true,
                isDelete: false,
                isPurchased: false,
                storeId: storeid
            });
        }
        console.log("cartDetails", cartDetails)
        if (cartDetails) {
            const products = [];
            let summary = [];
            let appliedOrderNo = null;
            let totalTryCartDeduction = 0;

            const vat = await additionalFeeService.findOne({ type: "vat", storeId: storeid })
            console.log("vat", vat)

            const taxConstant = (100 + Number(vat?.fee)) / 100

            for (const product of cartDetails?.products) {
                let price;
                const productDetails = await productService.findOne({
                    _id: product?.product?._id,
                    isDelete: false,
                    storeId: storeid
                });
                let selectedSize = null;
                let selectedContactSize = null
                let stock = productDetails?.stock;
                lensDetails = {}
                if (product?.lensDetails) {
                    let leftSph = null
                    let rightSph = null
                    let leftCyl = null
                    let rightCyl = null
                    let leftAxis = null
                    let rightAxis = null
                    if (product?.lensDetails?.prescription && typeof product.lensDetails.prescription === 'object') {
                        if (product?.lensDetails?.prescription?.leftSph) {
                            const leftSphData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftSph,
                                storeId: storeid
                            })
                            if (leftSphData) leftSph = leftSphData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightSph) {
                            const rightSphData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightSph,
                                storeId: storeid
                            })
                            if (rightSphData) rightSph = rightSphData?.name
                        }

                        if (product?.lensDetails?.prescription?.leftCyl) {
                            const leftCylData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftCyl,
                                storeId: storeid
                            })
                            if (leftCylData) leftCyl = leftCylData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightCyl) {
                            const rightCylData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightCyl,
                                storeId: storeid
                            })
                            if (rightCylData) rightCyl = rightCylData?.name
                        }

                        if (product?.lensDetails?.prescription?.leftAxis) {
                            const leftAxisData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.leftAxis,
                                storeId: storeid
                            })
                            if (leftAxisData) leftAxis = leftAxisData?.name
                        }

                        if (product?.lensDetails?.prescription?.rightAxis) {
                            const rightAxisData = await lensPowerService.findOne({
                                _id: product?.lensDetails?.prescription?.rightAxis,
                                storeId: storeid
                            })
                            if (rightAxisData) rightAxis = rightAxisData?.name
                        }
                    }
                    lensDetails = {
                        vision: product?.lensDetails?.vision,
                        prescription: {
                            leftSph: leftSph,
                            leftCyl: leftCyl,
                            leftAxis: leftAxis,
                            rightSph: rightSph,
                            rightCyl: rightCyl,
                            rightAxis: rightAxis,
                            pd: product?.lensDetails?.prescription?.pd || null
                        },
                        lensType: product?.lensDetails?.lensType || null,
                        brand: product?.lensDetails?.brand?.name[language] || null,
                        photocromic: product?.lensDetails?.photocromic || null,
                        index: product?.lensDetails?.index?.name || null,
                        coating: product?.lensDetails?.coating?.name || null
                    }
                }
                let flag = false
                if (product?.contactLens) {
                    if (product?.contactLens?.sphLeft) {
                        const leftSphData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.sphLeft,
                            storeId: storeid
                        })
                        if (leftSphData) {
                            leftSph = leftSphData?.name
                            flag = isLensPriceable(leftSphData)
                        }
                    }

                    if (product?.contactLens?.sphRight) {
                        const rightSphData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.sphRight,
                            storeId: storeid
                        })
                        if (rightSphData) {
                            rightSph = rightSphData?.name
                            flag = isLensPriceable(rightSphData)
                        }
                    }

                    if (product?.contactLens?.cylLeft) {
                        const leftCylData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.cylLeft,
                            storeId: storeid
                        })
                        if (leftCylData) {
                            leftCyl = leftCylData?.name
                            flag = isLensPriceable(leftCylData)
                        }
                    }

                    if (product?.contactLens?.cylRight) {
                        const rightCylData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.cylRight,
                            storeId: storeid
                        })
                        if (rightCylData) {
                            rightCyl = rightCylData?.name
                            flag = isLensPriceable(rightCylData)
                        }
                    }

                    if (product?.contactLens?.axisLeft) {
                        const leftAxisData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.axisLeft,
                            storeId: storeid
                        })
                        if (leftAxisData) {
                            leftAxis = leftAxisData?.name
                            flag = isLensPriceable(leftAxisData)
                        }
                    }

                    if (product?.contactLens?.axisRight) {
                        const rightAxisData = await contactLensPowerService.findOne({
                            _id: product?.contactLens?.axisRight,
                            storeId: storeid
                        })
                        if (rightAxisData) {
                            rightAxis = rightAxisData?.name
                            flag = isLensPriceable(rightAxisData)
                        }
                    }
                }

                price = productDetails?.offerPrice?.aed && (productDetails?.offerPrice?.aed > 0 || productDetails?.offerPrice?.aed < productDetails?.price?.aed)
                    ? productDetails?.offerPrice?.aed
                    : productDetails?.price?.aed;
                console.log("flag", productDetails?.offerPrice?.aed < 1 || productDetails?.offerPrice?.aed >= productDetails?.price?.aed)
                console.log("price", price)
                if (flag) {
                    price += (productDetails?.powerPrice ?? 0);
                }
                let tax = price - (price / taxConstant)
                // if (product?.contactLens?.multiple) {
                //     tax = tax * 2
                // }

                if (product.fromTryCart) {
                    if (product.orderNo !== appliedOrderNo) {
                        const orderAmount = await orderService.findOne({
                            orderNo: product.orderNo,
                            storeId: storeid
                        });
                        totalTryCartDeduction += orderAmount?.total;
                        appliedOrderNo = product.orderNo;
                    }
                }

                products.push({
                    slug: productDetails?.slug,
                    sku: productDetails?.sku,
                    brand: productDetails?.brand?.name[language] || productDetails?.brand?.name?.en,
                    cashback: {
                        brand: {
                            isEnabled: productDetails?.brand?.isCashbackEnabled ?? false,
                            percentage: productDetails?.brand?.cashbackPercentage ?? 0,
                        },
                        category: (productDetails?.category?.[0] && productDetails?.category?.[0]?.isCashbackEnabled) ?
                            { isEnabled: productDetails?.category?.[0]?.isCashbackEnabled, percentage: productDetails?.category?.[0]?.cashbackPercentage }
                            : (productDetails?.category?.[1] && productDetails?.category?.[1]?.isCashbackEnabled) ?
                                { isEnabled: productDetails?.category?.[1]?.isCashbackEnabled, percentage: productDetails?.category?.[1]?.cashbackPercentage }
                                : (productDetails?.category?.[2] && productDetails?.category?.[2]?.isCashbackEnabled) ?
                                    { isEnabled: productDetails?.category?.[2]?.isCashbackEnabled, percentage: productDetails?.category?.[2]?.cashbackPercentage }
                                    : { isEnabled: false, percentage: 0 },
                        product: {
                            isEnabled: productDetails?.isCashbackEnabled,
                            percentage: productDetails?.cashbackPercentage
                        }
                    },
                    category: productDetails?.category?.map(item => ({ name: item.name[language] })),
                    customizable: productDetails?.customizable,
                    productType: productDetails?.productType,
                    tax: productDetails?.isTaxIncluded ? tax : 0,
                    productid: productDetails?._id,
                    name: language ? productDetails?.name[language] : productDetails?.name.en,
                    thumbnail: process.env.DOMAIN + productDetails?.thumbnail,
                    currency: product?.currency,
                    quantity: product?.quantity,
                    priceTotal: Number(price) * product?.quantity,
                    price: Number(price),
                    color: productDetails?.color?.name[language],
                    size: productDetails?.productType == "frame" ? productDetails?.size : productDetails?.contactSize,

                    lensDetails: lensDetails,
                    stock: stock,
                    lensId: product?._id,
                    contactLensDetails: product?.contactLens || null,
                    couponAmount: product?.couponAmount ?? 0,
                    couponCode: product?.couponCode ?? "",
                });
            }

            const totalTax = Number(products?.reduce((total, product) => Number(total + (product?.tax * (product?.quantity * (product?.contactLensDetails?.multiple ? 2 : 1)))), 0))
            const translation = await db.Texts.findOne({ storeId: storeid })
            let total = products?.reduce((total, product) => total + (product?.priceTotal * (product?.contactLensDetails?.multiple ? 2 : 1)), 0)

            if (cartDetails?.baseTotal) {

                summary.push({
                    text: translation?.cartPage?.subtotal[language] ?? "Subtotal",
                    value: Number(total - totalTax.toFixed(2)).toFixed(2),
                    currency: "AED",
                    sign: ''
                });
            }
            if (vat && vat.fee > 0) {
                summary.push({
                    text: `VAT(${vat.fee}%)`,
                    value: totalTax.toFixed(2),
                    currency: "AED",
                    sign: ''
                });
            }
            if (cartDetails?.savings) {
                summary.push({
                    text: translation?.cartPage?.savings[language] ?? "Savings",
                    value: Number(cartDetails?.savings).toFixed(2),
                    currency: "AED",
                    sign: '-'
                });
            }
            if (cartDetails?.loyaltyAmount) {
                summary.push({
                    text: translation?.cartPage?.loyaltyDiscount[language] ?? "Loyalty Discount",
                    value: Number(cartDetails?.loyaltyAmount).toFixed(2),
                    currency: "AED",
                    sign: '-'
                });
            }
            if (totalTryCartDeduction > 0) {
                summary.push({
                    text: translation?.cartPage?.tryCartDeduction[language] ?? "Try Cart Deduction",
                    value: Number(totalTryCartDeduction).toFixed(2),
                    currency: "AED",
                    sign: '-'
                });
            }
            // need to fetch the shipping charge from here, not from the frontend
            // if (shipping) {
            //     summary.push({
            //         text: translation.cartPage.shipping[language] ?? "Shipping",
            //         value: Number(shipping).toFixed(2),
            //         currency: "AED",
            //         sign: '+'
            //     })
            // }
            if (cartDetails?.total) {
                if (totalTryCartDeduction > 0) total -= totalTryCartDeduction
                if (cartDetails?.loyaltyAmount > 0) total -= cartDetails?.loyaltyAmount
                if (cartDetails?.savings > 0) total -= cartDetails?.savings;
                if (shipping) total += Number(shipping)
                console.log(cartDetails?.savings)

                summary.push({
                    text: translation?.cartPage?.total[language] ?? "Total",
                    value: total.toFixed(2),
                    currency: "AED",
                    sign: ''
                });
            }

            const response = {
                count: cartDetails?.products?.length,
                products: products,
                summary: summary,
                total,
                tax: totalTax.toFixed(2),
                shipping: Number(shipping).toFixed(2),
                couponApplied: cartDetails?.couponApplied,
                couponCode: cartDetails?.couponCode || null,
                loyaltyApplied: cartDetails?.loyaltyApplied,
            };

            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.NO_DATA.error_code,
                    error_message: messages.NO_DATA.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.removeFromCart = async (req, res) => {
    try {
        const { authorization, devicetoken } = req.headers;

        let { language, storeid } = req?.headers;
        const translation = await db.Texts.findOne({ storeId: storeid })
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";

        let customerId = null;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }
        if (customerId) {
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isPurchased: false,
                storeId: storeid
            });
        }

        const { body } = req;

        const products = cartDetails?.products;
        let cartProducts = [];
        let cartBody = { products: cartProducts };
        let baseTotal = Number(cartDetails?.baseTotal);
        let total = Number(cartDetails?.total);
        // let tryCartDeduct = cartDetails?.tryCartDeduct ? Number(cartDetails?.tryCartDeduct) : "";
        let savings = cartDetails?.savings ? Number(cartDetails?.savings) : "";

        // for (let product of products) {
        //     console.log("product :: ", product);
        //     // Check if size is provided in body and matches, or if no size is provided
        //     if (String(product?.product?._id) === String(body?.product) && (!body?.size || String(product?.size) === String(body?.size))) {
        //         baseTotal -= Number(product?.priceTotal);
        //         total -= Number(product?.priceTotal);
        //     } else {
        //         cartProducts.push({
        //             product: product?.product?._id,
        //             quantity: product?.quantity,
        //             priceTotal: product?.priceTotal,
        //             currency: product?.currency,
        //             size: product?.size,
        //         });
        //     }
        // }

        for (let product of products) {
            // const shouldRemoveProduct =
            // String(product?.product?._id) === String(body?.product) &&
            // (!body?.size || String(product?.size) === String(body?.size)) &&
            // (body?.lensId
            //   ? String(product?.lensId) === String(body?.lensId)
            //   : !product?.lensId);

            const shouldRemoveProduct = String(product?._id) == String(body?.lensId);
            if (shouldRemoveProduct) {
                // const tryCartPrice = await tryCartService.findOne({ isDelete: false });
                // if (product?.fromTryCart) {
                //     tryCartDeduct -= Number(tryCartPrice?.amount);
                // }
                baseTotal -= Number(product?.priceTotal);
                total -= Number(product?.priceTotal);
                // if (product?.fromTryCart) total += Number(tryCartPrice?.amount);
            } else {
                cartProducts.push({
                    ...product?._doc
                });
            }
        }

        // cartBody["tryCartDeduct"] = tryCartDeduct;
        cartBody["baseTotal"] = baseTotal.toFixed(2);
        cartBody["savings"] = 0;
        cartBody["couponApplied"] = false;
        cartBody["couponCode"] = "";
        cartBody["total"] = total < 0 ? 0 : total.toFixed(2);

        if (cartDetails?.couponApplied) {
            await removeCoupon(cartDetails, customerDetails);
            const { savings, total, products, error, isDeleted } = await reApplyCoupon({ ...cartBody, products: cartProducts, couponCode: cartDetails?.couponCode, _id: cartDetails?._id, total: Number(cartBody["total"]) }, customerDetails, res);
            if (error) return;
            if (!isDeleted) {
                cartBody["couponApplied"] = true;
                cartBody["couponCode"] = cartDetails?.couponCode;
                cartBody["savings"] = savings;
                cartBody["total"] = total - savings;
                cartBody["products"] = products;
                if (cartDetails?.loyaltyApplied) cartBody["total"] = (cartBody["total"] - cartDetails?.loyaltyAmount).toFixed(2);

            }
        }


        let query = { isDelete: false, isActive: true, _id: cartDetails?._id, storeId: storeid };

        const result = await service.update(query, cartBody);

        helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.CART.REMOVED.error_code,
                // error_message: messages.CART.REMOVED.error_message,
                error_message: translation?.popup?.cartRemove[language],
            }
        );
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.updateCart = async (req, res) => {
    try {
        const { authorization, devicetoken } = req.headers;

        const translation = await db.Texts.findOne({})
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";

        let customerId = null;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }

        if (customerId) {
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                storeId: storeid
            });
        }

        const { body } = req;

        const productDetails = await productService.findOne({
            _id: body?.product,
            isActive: true,
            isDelete: false,
            storeId: storeid
        });
        let cartProducts = cartDetails?.products;
        if (cartDetails) {
            let products = [];
            let baseTotal = 0;
            let total = 0;

            if (cartDetails?.couponApplied) {
                await removeCoupon(cartDetails, customerDetails)
            }

            for (let product of cartProducts) {
                if (productDetails?.stock >= product?.quantity + body?.quantity) {
                    if (String(product?.product?._id) == String(productDetails?._id)) {
                        let quantity = product?.quantity + body?.quantity;
                        let priceTotal = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed * quantity : productDetails?.price?.aed * quantity;
                        products.push({
                            product: product?.product?._id,
                            quantity: quantity,
                            priceTotal: priceTotal,
                            currency: product?.currency,
                        });
                    } else {
                        products.push({
                            product: product?.product?._id,
                            quantity: product?.quantity,
                            priceTotal: product?.priceTotal,
                            currency: product?.currency,
                        });
                    }
                } else {
                    helper.deliverResponse.res(
                        200,
                        {},
                        {
                            error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                            error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                        }
                    );
                }
            }

            for (let product of products) baseTotal += Number(product?.priceTotal);
            total = baseTotal;


            const data = {
                products: products,
                baseTotal: baseTotal.toFixed(2),
                total: total.toFixed(2),
            };
            let query = { isDelete: false, isActive: true, storeId: storeid };

            if (cartDetails?.couponApplied) {

                const { savings, total, products, error } = await reApplyCoupon({ ...cartDetails?._doc, ...data }, customerDetails, res);
                if (error) {
                    console.log(error)
                    return
                };
                data.total = total - savings;
                data.savings = savings;
                data.products = products;
                if (cartDetails?.loyaltyApplied) data.total = (data.total - cartDetails?.loyaltyAmount).toFixed(2);
            }

            if (customerId) query.customer = customerDetails?._id;
            else if (devicetoken) query.devicetoken = devicetoken;

            const result = await service.update(query, data);
            if (result) {
                return helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.CART.UPDATED.error_code,
                        error_message: translation?.popup?.cartUpdate[language],
                    }
                );
            } else {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    }
                );
            }
        } else {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.CART.EMPTY.error_code,
                    error_message: messages.CART.EMPTY.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

const getCustomerCart = async (customerId) => {
    return await service.findOne({
        customer: customerId,
        isDelete: false,
        isActive: true,
        isPurchased: false
    });
};

const getGuestCart = async (devicetoken) => {
    return await service.findOne({
        devicetoken,
        customer: { $exists: false },
        isDelete: false,
        isActive: true,
        isPurchased: false
    });
};

function isLensPriceable(lensData) {
    if (lensData?.name != "0.00" && lensData?.name != "None" && lensData?.name != "none" && lensData?.name != "NONE") {
        return true;
    }
}

exports.getCoupons = async (req, res) => {
    try {
        const { authorization, devicetoken, storeid } = req.headers;

        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
            });
            cartDetails = await getCustomerCart(customerDetails?._id);
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await getGuestCart(devicetoken);
        }

        const products = [];

        for (let product of cartDetails?.products) {
            let flag = false;
            if (product?.contactLens) {
                if (product?.contactLens?.sphLeft) flag = isLensPriceable(product?.contactLens?.sphLeft);
                if (product?.contactLens?.sphRight) flag = isLensPriceable(product?.contactLens?.sphRight);
                if (product?.contactLens?.cylLeft) flag = isLensPriceable(product?.contactLens?.cylLeft);
                if (product?.contactLens?.cylRight) flag = isLensPriceable(product?.contactLens?.cylRight);
                if (product?.contactLens?.axisLeft) flag = isLensPriceable(product?.contactLens?.axisLeft);
                if (product?.contactLens?.axisRight) flag = isLensPriceable(product?.contactLens?.axisRight);
            }
            const productDetails = await productService.findOne({
                _id: product?.product,
                isActive: true,
                isDelete: false
            });
            let price;

            price = productDetails?.offerPrice?.aed < 1 || productDetails?.offerPrice?.aed >= productDetails?.price?.aed
                ? productDetails?.price?.aed
                : productDetails?.offerPrice?.aed;

            if (flag) {
                price += (productDetails?.powerPrice ?? 0);
            }

            products.push({
                "ITEMPRICE": price?.toFixed(2),
                "ITEMDISCOUNT": "0",
                "QUANTITY": product?.quantity * (product?.contactLens?.multiple ? 2 : 1),
                "ITEMCODE": productDetails?.sku,
            });
        }

        const payload = {
            COUPONCODEENQREQ: {
                HEADERINFO: {
                    REQUESTID: Math.floor(Math.random() * 10000),
                },
                COUPONCODEINFO: {
                    COUPONCODE: "ALL", //BALANCES
                    STORENUMBER: "1",
                    SOURCETYPE: "eComm",
                    CUSTOMERID: "",
                    CARDNUMBER: "",
                    PHONE: customerDetails?.mobile || "",
                    EMAIL: customerDetails?.email || "",
                },
                PURCHASEDITEMS: products,
                USERDETAILS: {
                    // USERNAME: "webcastle",
                    // ORGID: "webcastle",
                    // TOKEN: "0J3RIVME081FARD3",
                    USERNAME: CRM_USERNAME, // Mandatory field
                    TOKEN: CRM_TOKEN, // Mandatory field
                    ORGID: CRM_ORG_ID, // Mandatory field
                },
            },
        };
        console.log(payload.COUPONCODEENQREQ)
        const response = await axios.post("http://app.optculture.com/subscriber/CouponCodeEnquiryRequestOPT.mqrm", payload);
        if (response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "0" || response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "100022") {
            let coupons = [];
            console.log(response?.data?.COUPONCODERESPONSE?.COUPONDISCOUNTINFO[2]?.DISCOUNTINFO[0]?.ITEMCODEINFO?.map((item) => item?.ITEMCODE));
            for (let coupon of response?.data?.COUPONCODERESPONSE?.COUPONDISCOUNTINFO) {
                const minimumValue = Number(coupon?.DISCOUNTINFO?.[0]?.MINPURCHASEVALUE);
                const total = products?.reduce((total, product) => total + (product?.["ITEMPRICE"] * product?.["QUANTITY"]), 0)

                if (total < minimumValue) continue;
                coupons.push({
                    title: coupon?.COUPONNAME,
                    code: coupon?.COUPONCODE,
                    description: coupon?.DESCRIPTION,
                });
            }
            // console.log(response?.data?.COUPONCODERESPONSE)
            helper.deliverResponse(res, 200, coupons, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            console.log(response?.data?.COUPONCODERESPONSE)
            helper.deliverResponse(res, 200, [], {
                error_code: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE,
                error_message: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.MESSAGE,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.applyCoupon = async (req, res) => {
    try {
        const { body } = req;
        const { storeid } = req?.headers
        const { customerId } = res?.locals?.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false,
        });

        const payProducts = [];
        let cartDetails = null;
        let baseTotal = 0;
        if (customerDetails) {
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });

            for (let product of cartDetails?.products) {

                let flag = false;
                if (product?.contactLens) {
                    if (product?.contactLens?.sphLeft) flag = isLensPriceable(product?.contactLens?.sphLeft);
                    if (product?.contactLens?.sphRight) flag = isLensPriceable(product?.contactLens?.sphRight);
                    if (product?.contactLens?.cylLeft) flag = isLensPriceable(product?.contactLens?.cylLeft);
                    if (product?.contactLens?.cylRight) flag = isLensPriceable(product?.contactLens?.cylRight);
                    if (product?.contactLens?.axisLeft) flag = isLensPriceable(product?.contactLens?.axisLeft);
                    if (product?.contactLens?.axisRight) flag = isLensPriceable(product?.contactLens?.axisRight);
                }
                const productDetails = await productService.findOne({
                    _id: product?.product,
                    isActive: true,
                    isDelete: false
                });
                let price = productDetails?.offerPrice?.aed < 1 || productDetails?.offerPrice?.aed >= productDetails?.price?.aed
                    ? productDetails?.price?.aed
                    : productDetails?.offerPrice?.aed;


                if (flag) {
                    price += (productDetails?.powerPrice ?? 0);
                }
                baseTotal += Number(price) * (product?.quantity * (product?.contactLens?.multiple ? 2 : 1));
                console.log("price", baseTotal)

                payProducts.push({
                    "ITEMPRICE": price?.toFixed(2),
                    "ITEMDISCOUNT": "0",
                    "QUANTITY": product?.quantity * (product?.contactLens?.multiple ? 2 : 1),
                    "ITEMCODE": productDetails?.sku,
                });
            }
        }

        console.log("baseTotal", baseTotal)

        const payload = {
            COUPONCODEENQREQ: {
                HEADERINFO: {
                    REQUESTID: Math.floor(Math.random() * 10000),
                },
                COUPONCODEINFO: {
                    COUPONCODE: "ALL", //BALANCES
                    STORENUMBER: "1",
                    SOURCETYPE: "eComm",
                    CUSTOMERID: "",
                    CARDNUMBER: "",
                    PHONE: customerDetails?.mobile,
                    EMAIL: customerDetails?.email,
                },
                PURCHASEDITEMS: payProducts,
                USERDETAILS: {
                    // USERNAME: "webcastle",
                    // ORGID: "webcastle",
                    // TOKEN: "0J3RIVME081FARD3",
                    USERNAME: CRM_USERNAME, // Mandatory field
                    TOKEN: CRM_TOKEN, // Mandatory field
                    ORGID: CRM_ORG_ID, // Mandatory field
                },
            },
        };
        const response = await axios.post("http://app.optculture.com/subscriber/CouponCodeEnquiryRequestOPT.mqrm", payload);
        if ((response?.data && response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "0") || response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "100022") {
            const cdata = response?.data?.COUPONCODERESPONSE?.COUPONDISCOUNTINFO;
            if (customerDetails) {
                if (cartDetails && cartDetails?.products?.length > 0) {
                    if (body?.coupon) {
                        const coupon = cdata?.find((coupon) => coupon?.COUPONCODE == body?.coupon);
                        if (!coupon) {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: messages.SERVER_ERROR.error_code,
                                    error_message: "Invalid coupon code",
                                }
                            );
                            return;
                        }
                        const minimumValue = Number(coupon?.DISCOUNTINFO?.[0]?.MINPURCHASEVALUE);
                        let total = payProducts?.reduce((total, product) => total + (product?.["ITEMPRICE"] * product?.["QUANTITY"]), 0)
                        const type = coupon?.DISCOUNTCRITERIA?.split("-")[1];
                        if (total < minimumValue) {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: messages.SERVER_ERROR.error_code,
                                    error_message: "Total amount cannot be less than minimum purchase value",
                                }
                            );
                            return;
                        }

                        let savings = 0;
                        let products = cartDetails?.products;
                        if (cartDetails?.loyaltyApplied) total -= cartDetails?.loyaltyAmount;

                        if (type == "MVP") {
                            if (coupon?.DISCOUNTINFO[0]?.VALUECODE == "P") {
                                savings += (total * (coupon?.DISCOUNTINFO[0].VALUE / 100)).toFixed(2);
                                total = (total - savings).toFixed(2);
                            } else {
                                if (cartDetails?.total < coupon?.DISCOUNTINFO[0]?.VALUE) {
                                    helper.deliverResponse(
                                        res,
                                        422,
                                        {},
                                        {
                                            error_code: messages.SERVER_ERROR.error_code,
                                            error_message: "Total amount cannot be less than coupon value",
                                        }
                                    );
                                    return;
                                }
                                savings += coupon?.DISCOUNTINFO[0]?.VALUE;
                                total = (total - coupon?.DISCOUNTINFO[0]?.VALUE).toFixed(2);
                            }
                        } else {
                            const itemCodes = coupon?.DISCOUNTINFO[0]?.ITEMCODEINFO?.map((item) => item?.ITEMCODE);
                            let itemTotal = 0;
                            for (let product of products) {
                                if (itemCodes.includes(product?.product?.sku)) {
                                    const pay = payProducts?.find((item) => item?.ITEMCODE == product?.product?.sku);
                                    if (coupon?.DISCOUNTINFO[0]?.VALUECODE != "P") {
                                        itemTotal += (Number(pay?.ITEMPRICE) * pay?.QUANTITY);
                                    }
                                }
                            }
                            // console.log(itemCodes)
                            for (let product of products) {
                                if (itemCodes.includes(product?.product?.sku)) {
                                    const pay = payProducts?.find((item) => item?.ITEMCODE == product?.product?.sku);
                                    // console.log(pay)
                                    const productTotal = Number(pay?.ITEMPRICE) * pay?.QUANTITY;
                                    product.couponCode = body?.coupon;

                                    if (coupon?.DISCOUNTINFO[0]?.VALUECODE == "P") {
                                        const itemSaving = (productTotal * (coupon?.DISCOUNTINFO[0].VALUE / 100))
                                        savings += Number(itemSaving);
                                        total = Number(total - savings);
                                        product.couponAmount = Number(itemSaving);
                                    } else {
                                        let value = Number(coupon?.DISCOUNTINFO[0]?.VALUE) * ((Number(pay?.ITEMPRICE) * pay?.QUANTITY) / itemTotal);
                                        console.log("value", value)
                                        savings += value;
                                        total = Number(total - value);
                                        product.couponAmount = Number(value)?.toFixed(2);
                                    }
                                }
                            }

                        }
                        console.log("savings", savings)
                        const date = new Date()
                        const data = {
                            products: products,
                            total: Number(total),
                            baseTotal: baseTotal.toFixed(2),
                            savings: Number(savings),
                            couponApplied: true,
                            couponCode: body?.coupon,
                            couponDate: date,
                        };
                        const payload = {
                            COUPONCODEREDEEMREQ: {
                                HEADERINFO: {
                                    REQUESTID: customerDetails?._id?.toString(),
                                },
                                COUPONCODEINFO: {
                                    COUPONCODE: body?.coupon,
                                    RECEIPTNUMBER: "1",
                                    DOCSID: cartDetails?._id?.toString(),
                                    SUBSIDIARYNUMBER: "0",
                                    STORENUMBER: "1",
                                    CUSTOMERID: "",
                                    PHONE: customerDetails?.mobile,
                                    EMAIL: customerDetails?.email,
                                },
                                PURCHASECOUPONINFO: {
                                    TOTALAMOUNT: cartDetails?.total,
                                    TOTALDISCOUNT: Number(savings),
                                    USEDLOYALTYPOINTS: "",
                                },
                                USERDETAILS: {
                                    // USERNAME: "webcastle",
                                    // ORGID: "webcastle",
                                    // TOKEN: "0J3RIVME081FARD3",
                                    USERNAME: CRM_USERNAME, // Mandatory field
                                    TOKEN: CRM_TOKEN, // Mandatory field
                                    ORGID: CRM_ORG_ID, // Mandatory field
                                },
                            },
                        };

                        console.log({
                            COUPONCODEREDEEMREQ: {
                                HEADERINFO: {
                                    REQUESTID: customerDetails?._id?.toString(),
                                },
                                COUPONCODEINFO: {
                                    COUPONCODE: body?.coupon,
                                    RECEIPTNUMBER: "1",
                                    DOCSID: cartDetails?._id?.toString(),
                                    SUBSIDIARYNUMBER: "0",
                                    STORENUMBER: "1",
                                    CUSTOMERID: customerDetails?._id?.toString(),
                                    PHONE: customerDetails?.mobile,
                                    EMAIL: customerDetails?.email,
                                },
                                PURCHASECOUPONINFO: {
                                    TOTALAMOUNT: cartDetails?.total,
                                    TOTALDISCOUNT: savings,
                                    USEDLOYALTYPOINTS: "",
                                },
                                USERDETAILS: {
                                    // USERNAME: "webcastle",
                                    // ORGID: "webcastle",
                                    // TOKEN: "0J3RIVME081FARD3",
                                    USERNAME: CRM_USERNAME, // Mandatory field
                                    TOKEN: CRM_TOKEN, // Mandatory field
                                    ORGID: CRM_ORG_ID, // Mandatory field
                                },
                            },
                        })
                        const response = await axios.post("https://app.optculture.com/subscriber/CouponCodeRedeemRequestOPT.mqrm", payload);

                        if (response?.data?.COUPONCODEREDEEMRESPONSE?.STATUSINFO?.ERRORCODE == "0") {
                            const result = await service.update(
                                {
                                    customer: customerDetails?._id,
                                    isPurchased: false,
                                    isDelete: false,
                                    isActive: true,
                                    storeId: storeid
                                },
                                data
                            );
                            if (result) {
                                return helper.deliverResponse(
                                    res,
                                    200,
                                    {},
                                    {
                                        error_code: messages.COUPONS.APPLIED.error_code,
                                        error_message: messages.COUPONS.APPLIED.error_message,
                                    }
                                );
                            } else {
                                return helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: messages.SERVER_ERROR.error_code,
                                        error_message: messages.SERVER_ERROR.error_message,
                                    }
                                );
                            }
                        } else {
                            return helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Internal Server Error",
                                }
                            );
                        }
                    } else {
                        return helper.deliverResponse(
                            res,
                            200,
                            {},
                            {
                                error_code: messages.COUPONS.INVALID_COUPON.error_code,
                                error_message: messages.COUPONS.INVALID_COUPON.error_message,
                            }
                        );
                    }
                } else {
                    return helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.CART.EMPTY.error_code,
                            error_message: messages.CART.EMPTY.error_message,
                        }
                    );
                }
            } else {
                return helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.NO_DATA.error_code,
                        error_message: messages.NO_DATA.error_message,
                    }
                );
            }
        } else {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE,
                    error_message: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.MESSAGE,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.buyWithLens = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'ae';
        const translation = await db.Texts.findOne({ storeId: storeid })
        // const { authorization, devicetoken } = req.headers;

        // let customerId = null;
        // let isGuest = false;
        // let customerDetails = null;
        // let cartDetails = null;

        // if (authorization) {
        //     let token = authorization.split("Bearer ")[1];
        //     jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
        //         if (err) customerId = null;
        //         customerId = decoded?.customerId;
        //     });
        //     isGuest = false;
        //     customerDetails = await customerService.findOne({
        //         refid: customerId,
        //         isDelete: false,
        //         isActive: true,
        //     });
        //     cartDetails = await service.findOne({
        //         customer: customerDetails?._id,
        //         isDelete: false,
        //         isActive: true,
        //         isPurchased: false,
        //     });
        // } else if (devicetoken) {
        //     isGuest = true;
        //     cartDetails = await service.findOne({
        //         devicetoken: devicetoken,
        //         customer: { $exists: false },
        //         isDelete: false,
        //         isActive: true,
        //         isPurchased: false,
        //     });
        // }

        let { body } = req;

        const product = await productService.findOne({
            _id: body?.product,
            isActive: true,
            isDelete: false,
            storeId: storeid
        });
        if (!product) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
            return;
        }

        let productStock = null;
        let productPrice = null;
        body.storeId = storeid;

        if (body?.size) {
            const selectedSize = product.sizes.find((size) => size?.size?._id == body?.size);
            productStock = selectedSize?.stock;
            productPrice = selectedSize?.offerPrice ? selectedSize?.offerPrice : selectedSize?.price;
        } else {
            if (product?.customizable) {
                productStock = product.sizes[0].stock;
                productPrice = product.sizes[0].offerPrice ? product.sizes[0].offerPrice : product.sizes[0].price;
            } else {
                productStock = product?.stock;
                productPrice = product?.offerPrice?.aed ? product?.offerPrice?.aed : product?.price?.aed;
            }
        }

        if (productStock < body?.quantity) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                    error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                }
            );
            return;
        }

        const lensId = crypto.randomUUID();

        // if (!cartDetails) {
        if (body?.vision == "single") {
            if (body?.prescriptionType == "file") {
                //create lens enquiry
                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;
                body.prescriptionFile = body?.prescription;

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;
                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }
                sentEnquiryMail(lensEnquiry, "file")

                // const count = await service.count({});
                // let products = [];

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),                        currency: "AED",
                //     size: body?.size ? body?.size : null,
                //     lensDetails: {
                //         vision: body?.vision,
                //         prescription: body?.prescription,
                //         lensType: body?.lensType,
                //         brand: body?.brand,
                //         photocromic: body?.photocromic ? body?.photocromic : null,
                //         index: body?.index,
                //         coating: body?.coating,
                //         lensEnquiry: lensEnquiry._id,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // });
                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    // error_message: messages.CART.ADDED.error_message,
                    error_message: translation?.popup?.cartAdd[language],
                });
            } else if (body?.prescriptionType == "skip") {
                //skip for now (frame to cart and lens enquiry)
                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;

                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }
                sentEnquiryMail(lensEnquiry, "skip")

                // const count = await service.count({});
                // let products = [];

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
                //     size: body?.size ? body?.size : null,
                //     lensDetails: {
                //         vision: body?.vision,
                //         lensEnquiry: lensEnquiry._id,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // })

                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });

            } else {
                // prescription manual (lens and frame to cart)

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;

                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }
                sentEnquiryMail(lensEnquiry, "manual")
                // const count = await service.count({});
                // let products = [];

                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),       
                //     currency: "AED",
                //     size: body?.size,
                //     lensDetails: {
                //         vision: body?.vision,
                //         prescription: body?.prescription,
                //         lensType: body?.lensType,
                //         brand: body?.brand,
                //         photocromic: body?.photocromic ? body?.photocromic : null,
                //         index: body?.index,
                //         coating: body?.coating,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // });
                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });
            }
        } else {
            // progressive lens
            if (body?.prescriptionType == "file") {
                //create lens enquiry
                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;
                body.prescriptionFile = body.prescription;
                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }
                sentEnquiryMail(lensEnquiry, "file")

                // const count = await service.count({});
                // let products = [];

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
                //     currency: "AED",
                //     size: body?.size,
                //     lensDetails: {
                //         vision: body?.vision,
                //         prescription: body?.prescription,
                //         lensType: body?.lensType,
                //         brand: body?.brand,
                //         photocromic: body?.photocromic ? body?.photocromic : null,
                //         index: body?.index,
                //         coating: body?.coating,
                //         lensEnquiry: lensEnquiry._id,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // });
                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });
            } else if (body?.prescriptionType == "skip") {
                // prescription skip (create cart and enquiry)
                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;
                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }

                sentEnquiryMail(lensEnquiry, "skip")
                // const count = await service.count({});
                // let products = [];

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
                //     currency: "AED",
                //     size: body?.size,
                //     lensDetails: {
                //         vision: body?.vision,
                //         lensEnquiry: lensEnquiry._id,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // })

                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                // helper.deliverResponse(res, 200, {success: true}, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });
            } else {
                // prescription manual (create enquiry and frame to cart)
                // if (!isGuest) body.customer = customerDetails?._id;
                // else body.devicetoken = devicetoken;

                if (body.hasOwnProperty("coating")) delete body.coating;
                if (body.hasOwnProperty("index")) delete body.index;
                if (body.hasOwnProperty("lensType")) delete body.lensType;
                if (body.hasOwnProperty("photocromic")) delete body.photocromic;

                const lensEnquiry = await lensEnquiryService.create(body);
                if (lensEnquiry instanceof Error) {
                    helper.deliverResponse(res, 422, lensEnquiry, {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    });
                    return;
                }

                sentEnquiryMail(lensEnquiry, "manual")
                // const count = await service.count({});
                // let products = [];

                // body.refid = count + 1;
                // body.products = products;
                // let total = 0;
                // total = Number((productPrice * body?.quantity));
                // if(body?.sum) total += Number(body?.sum);
                // body.total = total.toFixed(2);
                // // body.total = Number(productPrice * body?.quantity).toFixed(2);
                // let baseTotal = 0;
                // baseTotal = Number((productPrice * body?.quantity));
                // if(body?.sum) baseTotal += Number(body?.sum);
                // body.baseTotal = baseTotal.toFixed(2);
                // // body.baseTotal = Number(productPrice * body?.quantity).toFixed(2);
                // body.date = { added: new Date().toISOString() };
                // products.push({
                //     product: product._id,
                //     quantity: body?.quantity,
                //     priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
                //     currency: "AED",
                //     size: body?.size,
                //     lensDetails: {
                //         vision: body?.vision,
                //         prescription: body?.prescription,
                //         lensType: body?.lensType,
                //         brand: body?.brand,
                //         photocromic: body?.photocromic ? body?.photocromic : null,
                //         index: body?.index,
                //         coating: body?.coating,
                //         lensEnquiry: lensEnquiry._id,
                //     },
                //     lensId: lensId,
                //     lensSum: body?.sum ? Number(body?.sum) : null,
                // });
                // const cart = await service.create(body);
                // if (cart instanceof Error) {
                //     helper.deliverResponse(res, 422, cart, {
                //         error_code: messages.SERVER_ERROR.error_code,
                //         error_message: messages.SERVER_ERROR.error_message,
                //     });
                //     return;
                // }
                // helper.deliverResponse(res, 200, cart, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                // helper.deliverResponse(res, 200, {success: true}, {
                //     error_code: messages.CART.ADDED.error_code,
                //     error_message: messages.CART.ADDED.error_message,
                // });
                helper.deliverResponse(res, 200, { success: true }, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });
            }
        }
        // } else {
        //     // cart already exist (update cart)
        //     const cartid = cartDetails?.refid;
        //     if (body?.vision == "single") {
        //         if (body?.prescriptionType == "file") {
        //             // create lens enquiry
        //             body.prescriptionFile = body.prescription;

        //             if (body.hasOwnProperty("coating")) delete body.coating;
        //             if (body.hasOwnProperty("index")) delete body.index;
        //             if (body.hasOwnProperty("lensType")) delete body.lensType;
        //             if (body.hasOwnProperty("photocromic")) delete body.photocromic;
        //             const lensEnquiry = await lensEnquiryService.create(body);
        //             if (lensEnquiry instanceof Error) {
        //                 helper.deliverResponse(res, 422, lensEnquiry, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 currency: "AED",
        //                 size: body?.size ? body?.size : null,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     prescription: body?.prescription,
        //                     lensType: body?.lensType,
        //                     brand: body?.brand,
        //                     photocromic: body?.photocromic ? body?.photocromic : null,
        //                     index: body?.index,
        //                     coating: body?.coating,
        //                     lensEnquiry: lensEnquiry._id,
        //                 },
        //                 lensId: lensId,
        //                 lensSum: body?.sum ? Number(body?.sum) : null,
        //             });
        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if(body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);
        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });
        //         } else if (body?.prescriptionType == "skip") {
        //            // prescription skip (frame to cart and create enquiry)
        //             if (cartDetails?.customer) body.customer = cartDetails?.customer;
        //             else if (cartDetails?.devicetoken) body.devicetoken = cartDetails?.devicetoken;


        //             if (body.hasOwnProperty("coating")) delete body.coating;
        //             if (body.hasOwnProperty("index")) delete body.index;
        //             if (body.hasOwnProperty("lensType")) delete body.lensType;
        //             if (body.hasOwnProperty("photocromic")) delete body.photocromic;
        //             const lensEnquiry = await lensEnquiryService.create(body);
        //             if (lensEnquiry instanceof Error) {
        //                 helper.deliverResponse(res, 422, lensEnquiry, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 currency : "AED",
        //                 size: body?.size ? body?.size : null,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     lensEnquiry: lensEnquiry._id,
        //                 },
        //                 lensId: lensId,
        //             })
        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if(body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);
        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });          
        //         } else {
        //             // prescription manual
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 // priceTotal: Number(productPrice * body?.quantity).toFixed(2),
        //                 currency: "AED",
        //                 size: body?.size ? body?.size : null,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     prescription: body?.prescription,
        //                     lensType: body?.lensType,
        //                     brand: body?.brand,
        //                     photocromic: body?.photocromic ? body?.photocromic : null,
        //                     index: body?.index,
        //                     coating: body?.coating,
        //                 },
        //                 lensId: lensId,
        //                 lensSum: body?.sum ? Number(body?.sum) : null,
        //             });
        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if (body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);

        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });
        //         }
        //     } else {
        //         // progressive lens
        //         if (body?.prescriptionType == "file") {
        //             // create lens enquiry
        //             if (body.hasOwnProperty("coating")) delete body.coating;
        //             if (body.hasOwnProperty("index")) delete body.index;
        //             if (body.hasOwnProperty("lensType")) delete body.lensType;
        //             if (body.hasOwnProperty("photocromic")) delete body.photocromic;

        //             body.prescriptionFile = body?.prescription;
        //             const lensEnquiry = await lensEnquiryService.create(body);
        //             if (lensEnquiry instanceof Error) {
        //                 helper.deliverResponse(res, 422, lensEnquiry, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 // priceTotal: Number(productPrice * body?.quantity).toFixed(2),
        //                 currency: "AED",
        //                 size: body?.size,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     prescription: body?.prescription,
        //                     lensType: body?.lensType,
        //                     brand: body?.brand,
        //                     photocromic: body?.photocromic ? body?.photocromic : null,
        //                     index: body?.index,
        //                     coating: body?.coating,
        //                     lensEnquiry: lensEnquiry._id,
        //                 },
        //                 lensId: lensId,
        //                 lensSum: body?.sum ? Number(body?.sum) : null,
        //             });

        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if(body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);

        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });
        //         } else if (body?.prescriptionType == "skip") {
        //             // create lens enquiry and add to cart
        //             if (body.hasOwnProperty("coating")) delete body.coating;
        //             if (body.hasOwnProperty("index")) delete body.index;
        //             if (body.hasOwnProperty("lensType")) delete body.lensType;
        //             if (body.hasOwnProperty("photocromic")) delete body.photocromic;

        //             if (cartDetails?.customer) body.customer = cartDetails?.customer;
        //             else if (cartDetails?.devicetoken) body.devicetoken = cartDetails?.devicetoken;

        //             const lensEnquiry = await lensEnquiryService.create(body);
        //             if (lensEnquiry instanceof Error) {
        //                 helper.deliverResponse(res, 422, lensEnquiry, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 // priceTotal: Number(productPrice * body?.quantity).toFixed(2),
        //                 currency: "AED",
        //                 size: body?.size,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     lensEnquiry: lensEnquiry._id,
        //                 },
        //                 lensId: lensId,
        //                 lensSum: body?.sum ? Number(body?.sum) : null,
        //             });

        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if(body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);

        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }

        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });
        //         } else {
        //             // prescription manual
        //             if (body.hasOwnProperty("coating")) delete body.coating;
        //             if (body.hasOwnProperty("index")) delete body.index;
        //             if (body.hasOwnProperty("lensType")) delete body.lensType;
        //             if (body.hasOwnProperty("photocromic")) delete body.photocromic;


        //             const lensEnquiry = await lensEnquiryService.create(body);
        //             if (lensEnquiry instanceof Error) {
        //                 helper.deliverResponse(res, 422, lensEnquiry, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             cartDetails.products.push({
        //                 product: product._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: body?.sum ? (Number(productPrice) * body?.quantity + Number(body?.sum)).toFixed(2) : (Number(productPrice) * body?.quantity).toFixed(2),
        //                 // priceTotal: Number(productPrice * body?.quantity).toFixed(2),
        //                 currency: "AED",
        //                 size: body?.size,
        //                 lensDetails: {
        //                     vision: body?.vision,
        //                     prescription: body?.prescription,
        //                     lensType: body?.lensType,
        //                     brand: body?.brand,
        //                     photocromic: body?.photocromic ? body?.photocromic : null,
        //                     index: body?.index,
        //                     coating: body?.coating,
        //                     lensEnquiry: lensEnquiry._id,
        //                 },
        //                 lensId: lensId,
        //                 lensSum: body?.sum ? Number(body?.sum) : null,
        //             });

        //             let total = 0;
        //             total = Number(cartDetails?.total) + Number((productPrice * body?.quantity));
        //             if(body?.sum) total += Number(body?.sum);
        //             cartDetails.total = total.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             let baseTotal = 0;
        //             baseTotal = Number(cartDetails?.baseTotal) + Number((productPrice * body?.quantity));
        //             if(body?.sum) baseTotal += Number(body?.sum);
        //             cartDetails.baseTotal = baseTotal.toFixed(2);
        //             // cartDetails.total = cartDetails?.total + Number(productPrice * body?.quantity);
        //             // cartDetails.baseTotal = cartDetails?.baseTotal + Number(productPrice * body?.quantity);

        //             const cart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //             if (cart instanceof Error) {
        //                 helper.deliverResponse(res, 422, cart, {
        //                     error_code: messages.SERVER_ERROR.error_code,
        //                     error_message: messages.SERVER_ERROR.error_message,
        //                 });
        //                 return;
        //             }
        //             helper.deliverResponse(res, 200, cart, {
        //                 error_code: messages.CART.UPDATED.error_code,
        //                 error_message: translation?.popup?.cartUpdate[language],
        //             });
        //         }
        //     }
        // }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

async function sentEnquiryMail(enquiry, type) {
    const lensCcs = process.env.LENS_CCS?.split(" ");
    const orderCcs = process.env.ORDER_CCS?.split(" ");
    const ccs = [...(lensCcs ?? []), ...(orderCcs ?? [])]
    const mailOptions = {
        from: process.env.EMAIL_USER,
        to: ccs,
        subject: 'LENS ENQUIRY',
        html: await template.lensEnquiry(enquiry, type)
    }
    emailHelper.sendMail(mailOptions, (error, info) => {
        if (error) {
        } else {
        }
    });

    const userMailOptions = {
        from: process.env.EMAIL_USER,
        to: enquiry?.userDetails?.email,
        subject: 'LENS ENQUIRY',
        html: await template.lensEnquiry(enquiry, type)
    }
    emailHelper.sendMail(userMailOptions, (error, info) => {
        if (error) {
        } else {
        }
    });
}

exports.getShippingCharge = async (req, res) => {
    try {
        const { authorization, devicetoken, storeid } = req.headers;
        const { body } = req;
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) customerId = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
                isActive: true,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        }

        const address = await addressService.findOne({ _id: body?.address, isDelete: false });

        let totalQty = 0;
        let totaWeight = 0;
        cartDetails?.products?.forEach((product) => {
            totalQty = totalQty + product?.quantity;
            const productDetail = productService.findOne({
                _id: product?.product,
                isDelete: false,
            });
            totaWeight = totaWeight + productDetail?.weight * product?.quantity;
        });

        const url = "https://app.ecofreight.ae/api/webservices/client/checkprice";
        const payload = {
            rec_city: address?.city,
            service_type: "normal_delivery",
            qty: totalQty,
            weight: Number(totaWeight).toFixed(2),
        };

        let headers = {
            "Content-Type": "application/json",
            Accept: "application/json",
        };
        await axios
            .post(url, payload, { headers: headers })
            .then((response) => {
                let fixedResponseData = fixResponseString(response.data);
                let jsonObject = JSON.parse(fixedResponseData);
                if (jsonObject?.status == "success") {
                    helper.deliverResponse(res, 200, jsonObject, {
                        error_code: 1,
                        error_message: "success",
                    });
                }
            })
            .catch((error) => {
                helper.deliverResponse(res, 422, error, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                });
                return;
            });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

async function getCRMProducts(product) {
    let flag = false;
    let sizeDetails;
    if (product?.contactLens) {
        if (product?.contactLens?.sphLeft) flag = isLensPriceable(product?.contactLens?.sphLeft);
        if (product?.contactLens?.sphRight) flag = isLensPriceable(product?.contactLens?.sphRight);
        if (product?.contactLens?.cylLeft) flag = isLensPriceable(product?.contactLens?.cylLeft);
        if (product?.contactLens?.cylRight) flag = isLensPriceable(product?.contactLens?.cylRight);
        if (product?.contactLens?.axisLeft) flag = isLensPriceable(product?.contactLens?.axisLeft);
        if (product?.contactLens?.axisRight) flag = isLensPriceable(product?.contactLens?.axisRight);
    }
    let crmProductPrice;
    const crmProdDetails = await productService.findOne({
        _id: product?.product,
        isActive: true,
        isDelete: false
    });

    crmProductPrice = crmProdDetails?.offerPrice?.aed < 1 || crmProdDetails?.offerPrice?.aed >= crmProdDetails?.price?.aed
        ? crmProdDetails?.price?.aed
        : crmProdDetails?.offerPrice?.aed;

    if (flag) {
        crmProductPrice += (crmProdDetails?.powerPrice ?? 0);
    }
    let quantity = product?.quantity;
    return {
        "ITEMPRICE": crmProductPrice?.toFixed(2),
        "ITEMDISCOUNT": "0",
        "QUANTITY": quantity * (product?.contactLens?.multiple ? 2 : 1),
        "ITEMCODE": crmProdDetails?.sku,
    }
}

async function reApplyCoupon(cart, customerDetails, res) {
    let pricesPromise = [];
    for (let product of cart?.products) {
        pricesPromise.push(getCRMProducts(product));
    }
    const productPrices = await Promise.all(pricesPromise);
    const payload = {
        COUPONCODEENQREQ: {
            HEADERINFO: {
                REQUESTID: Math.floor(Math.random() * 10000),
            },
            COUPONCODEINFO: {
                COUPONCODE: "ALL", //BALANCES
                STORENUMBER: "1",
                SOURCETYPE: "eComm",
                CUSTOMERID: "",
                CARDNUMBER: "",
                PHONE: customerDetails?.mobile,
                EMAIL: customerDetails?.email,
            },
            PURCHASEDITEMS: productPrices,
            USERDETAILS: {
                // USERNAME: "webcastle",
                // ORGID: "webcastle",
                // TOKEN: "0J3RIVME081FARD3",
                USERNAME: CRM_USERNAME, // Mandatory field
                TOKEN: CRM_TOKEN, // Mandatory field
                ORGID: CRM_ORG_ID, // Mandatory field
            },
        },
    };
    const response = await axios.post("http://app.optculture.com/subscriber/CouponCodeEnquiryRequestOPT.mqrm", payload);
    if ((response?.data && response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "0") || response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == "100022") {
        const cdata = response?.data?.COUPONCODERESPONSE?.COUPONDISCOUNTINFO;
        if (customerDetails) {
            if (cart && cart?.products?.length > 0) {
                if (cart?.couponCode) {
                    const coupon = cdata?.find((coupon) => coupon?.COUPONCODE == cart?.couponCode);
                    if (!coupon) {
                        return { isDeleted: true };
                    }
                    let total = productPrices?.reduce((total, product) => total + (product?.["ITEMPRICE"] * product?.["QUANTITY"]), 0)
                    const minimumValue = Number(coupon?.DISCOUNTINFO?.[0]?.MINPURCHASEVALUE);
                    const type = coupon?.DISCOUNTCRITERIA?.split("-")[1];

                    if (total < minimumValue) {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: messages.SERVER_ERROR.error_code,
                                error_message: "Total amount cannot be less than minimum purchase value",
                            }
                        );
                        return { error: { message: "Total amount cannot be less than minimum purchase value", code: 422 } };
                    }

                    let savings = 0;
                    let products = cart?.products;
                    if (cart?.loyaltyApplied) total -= cart?.loyaltyAmount;

                    if (type == "MVP") {
                        if (coupon?.DISCOUNTINFO[0]?.VALUECODE == "P") {
                            savings += (total * (coupon?.DISCOUNTINFO[0].VALUE / 100)).toFixed(2);
                            total = (total - savings).toFixed(2);
                        } else {
                            if (cart?.total < coupon?.DISCOUNTINFO[0]?.VALUE) {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: messages.SERVER_ERROR.error_code,
                                        error_message: "Total amount cannot be less than coupon value",
                                    }
                                );
                                return { error: { message: "Total amount cannot be less than coupon value", code: 422 } };
                            }
                            savings += coupon?.DISCOUNTINFO[0]?.VALUE;
                            total = (total - coupon?.DISCOUNTINFO[0]?.VALUE).toFixed(2);
                        }
                    } else {
                        const itemCodes = coupon?.DISCOUNTINFO[0]?.ITEMCODEINFO?.map((item) => item?.ITEMCODE);
                        for (let product of products) {
                            console.log(product?.product?.sku)
                            if (itemCodes.includes(product?.product?.sku)) {
                                const pay = productPrices?.find((item) => item?.ITEMCODE == product?.product?.sku);
                                console.log(pay)
                                const productTotal = pay?.ITEMPRICE * pay?.QUANTITY;
                                product.couponCode = body?.coupon;

                                if (coupon?.DISCOUNTINFO[0]?.VALUECODE == "P") {
                                    const itemSaving = (productTotal * (coupon?.DISCOUNTINFO[0].VALUE / 100))
                                    console.log("itemSaving", itemSaving)
                                    savings += itemSaving.toFixed(2);
                                    total = (total - savings).toFixed(2);
                                    product.couponAmount = itemSaving;
                                } else {
                                    savings += coupon?.DISCOUNTINFO[0]?.VALUE;
                                    total = (total - coupon?.DISCOUNTINFO[0]?.VALUE).toFixed(2);
                                    product.couponAmount = coupon?.DISCOUNTINFO[0]?.VALUE;
                                }
                            }
                        }

                    }
                    console.log("savings", savings)
                    const date = new Date()
                    const data = {
                        products: products,
                        // total: total,
                        // baseTotal: baseTotal.toFixed(2),
                        savings: savings,
                        couponApplied: true,
                        couponCode: cart?.couponCode,
                        couponDate: date,
                    };
                    const payload = {
                        COUPONCODEREDEEMREQ: {
                            HEADERINFO: {
                                REQUESTID: customerDetails?._id?.toString(),
                            },
                            COUPONCODEINFO: {
                                COUPONCODE: cart?.couponCode,
                                RECEIPTNUMBER: "1",
                                DOCSID: cart?._id?.toString(),
                                SUBSIDIARYNUMBER: "0",
                                STORENUMBER: "1",
                                CUSTOMERID: "",
                                PHONE: customerDetails?.mobile,
                                EMAIL: customerDetails?.email,
                            },
                            PURCHASECOUPONINFO: {
                                TOTALAMOUNT: cart?.total,
                                TOTALDISCOUNT: savings,
                                USEDLOYALTYPOINTS: "",
                            },
                            USERDETAILS: {
                                // USERNAME: "webcastle",
                                // ORGID: "webcastle",
                                // TOKEN: "0J3RIVME081FARD3",
                                USERNAME: CRM_USERNAME, // Mandatory field
                                TOKEN: CRM_TOKEN, // Mandatory field
                                ORGID: CRM_ORG_ID, // Mandatory field
                            },
                        },
                    };

                    console.log(payload)
                    const response = await axios.post("https://app.optculture.com/subscriber/CouponCodeRedeemRequestOPT.mqrm", payload);
                    console.log(response?.data?.COUPONCODEREDEEMRESPONSE?.STATUSINFO)
                    if (response?.data?.COUPONCODEREDEEMRESPONSE?.STATUSINFO?.ERRORCODE == "0") {
                        return {
                            savings: savings,
                            total: total,
                            products,
                        };
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Internal Server Error",
                            }
                        );
                        return { error: { message: "Internal Server Error", code: 422 } };
                    }
                } else {
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.COUPONS.INVALID_COUPON.error_code,
                            error_message: messages.COUPONS.INVALID_COUPON.error_message,
                        }
                    );
                    return { error: { message: messages.COUPONS.INVALID_COUPON.error_message, code: 422 } };
                }
            } else {
                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.CART.EMPTY.error_code,
                        error_message: messages.CART.EMPTY.error_message,
                    }
                );
                return { error: { message: messages.CART.EMPTY.error_message, code: 422 } };
            }
        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.NO_DATA.error_code,
                    error_message: messages.NO_DATA.error_message,
                }
            );
            return { error: { message: messages.NO_DATA.error_message, code: 422 } };
        }
    } else {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE,
                error_message: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.MESSAGE,
            }
        );
        return { error: { message: response?.data?.COUPONCODERESPONSE?.STATUSINFO?.MESSAGE, code: 422 } };
    }

}

async function removeCoupon(cart, customerDetails) {
    const payload = {
        COUPONCODEREDEEMREQ: {
            HEADERINFO: {
                REQUESTID: customerDetails?._id?.toString(),
            },
            COUPONCODEINFO: {
                COUPONCODE: cart?.couponCode,
                RECEIPTNUMBER: "1",
                DOCSID: cart?._id?.toString(),
                SUBSIDIARYNUMBER: "0",
                STORENUMBER: "1",
                CUSTOMERID: "",
                PHONE: customerDetails?.mobile,
                EMAIL: customerDetails?.email,
            },
            PURCHASECOUPONINFO: {
                TOTALAMOUNT: cart?.total,
                TOTALDISCOUNT: cart?.savings,
                USEDLOYALTYPOINTS: "",
                TYPE: "Void"
            },
            USERDETAILS: {
                // USERNAME: "webcastle",
                // ORGID: "webcastle",
                // TOKEN: "0J3RIVME081FARD3",
                USERNAME: CRM_USERNAME, // Mandatory field
                TOKEN: CRM_TOKEN, // Mandatory field
                ORGID: CRM_ORG_ID // Mandatory field
            },
        },
    };

    const response = await axios.post("https://app.optculture.com/subscriber/CouponCodeRedeemRequestOPT.mqrm", payload);
    console.log(response?.data)
}

exports.addCart = async (req, res) => {

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }
        const { authorization, devicetoken } = req.headers;
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";

        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) customerId = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
                isActive: true,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        }

        let { body } = req;

        const [productDetails, translation, store] = await Promise.all([
            productService.findOne({
                _id: body?.product,
                isDelete: false,
                storeId: storeid
            }),
            db.Texts.findOne({ storeId: storeid }),
            db.MultiStore.findOne({ isDelete: false, isActive: true, storeId: storeid })
        ]);
        let totalPrice = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed * body?.quantity : productDetails?.price?.aed * body?.quantity;
        let sizeDetails;
        let stock = productDetails?.stock;
        let price = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;

        if (stock >= body?.quantity) {
            let tryCartAmount = null;
            if (body?.orderNo) {
                const updatedOrder = await orderService.pull(
                    {
                        orderNo: body?.orderNo,
                        isTryCart: true,
                        isDelete: false,
                        isActive: true,
                        storeId: storeid
                    },
                    { products: { product: body?.product, size: body?.size } }
                );

                tryCartAmount = updatedOrder?.total;

                if (updatedOrder && updatedOrder?.products?.length == 0) {
                    await orderService.update({ orderNo: body?.orderNo, storeId: storeid }, { isDelete: true });
                }
            }

            if (!cartDetails) {
                const count = await service.count({});
                let products = [];
                if (!isGuest) body.customer = customerDetails?._id;
                else body.devicetoken = devicetoken;

                body.products = products;
                body.refid = count + 1;
                body.total = totalPrice.toFixed(2);
                body.baseTotal = totalPrice.toFixed(2);
                body.date = { added: new Date().toISOString() };
                body.storeId = storeid
                let product = {
                    product: productDetails?._id,
                    quantity: body?.quantity,
                    priceTotal: totalPrice.toFixed(2),
                    currency: "AED",
                    size: sizeDetails?.size?._id,
                    fromTryCart: body?.orderNo ? true : false,
                    orderNo: body?.orderNo ? body?.orderNo : null,
                }
                if (sizeDetails) product.size = sizeDetails?.size?._id;
                products.push(product);

                const cart = await service.create(body);
                if (cart) {
                    helper.deliverResponse(res, 200, { ...cart?._doc, currency: store.currencyCode }, {
                        error_code: messages.CART.ADDED.error_code,
                        error_message: translation?.popup?.cartAdd[language],
                    });
                } else {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Failed to add cart",
                        }
                    );
                }
            } else {
                const cartid = cartDetails?.refid;
                let updatedProduct = false;

                if (cartDetails?.couponApplied) {
                    await removeCoupon(cartDetails, customerDetails);
                }

                for (let product of cartDetails?.products) {
                    if (String(product.product?._id) === String(productDetails?._id)) {
                        let flag = true;
                        if (sizeDetails) flag = String(product?.size?._id) == String(sizeDetails?.size?._id);
                        if (flag) {
                            if (stock < product?.quantity + body?.quantity) {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                                        error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                                    }
                                );
                                return;
                            }

                            product.quantity += body?.quantity;
                            let productPrice = price * product?.quantity;
                            product.priceTotal = Number(productPrice).toFixed(2);
                            product.productPrice = productPrice;
                            product.fromTryCart = body?.orderNo ? true : false;
                            product.orderNo = body?.orderNo ? body?.orderNo : null;
                            updatedProduct = true;
                            break;

                        }
                    }
                }


                if (updatedProduct) {
                    const updatedCartData = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                    if (updatedCartData) {
                        let totalSum = 0;
                        const cartProducts = updatedCartData?.products;
                        for (let product of cartProducts) {
                            totalSum += Number(product?.priceTotal);
                        }

                        let cartTotal = totalSum.toFixed(2);
                        if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
                        if (updatedCartData?.loyaltyApplied) cartTotal = (cartTotal - updatedCartData?.loyaltyAmount).toFixed(2);
                        // if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
                        // if (tryCartAmount) {
                        //     updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                        //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
                        // }
                        const cartData = {
                            // tryCartDeduct: updatedCartData?.tryCartDeduct,
                            baseTotal: totalSum.toFixed(2),
                            total: cartTotal,
                        };

                        if (updatedCartData?.couponApplied) {
                            const { savings, total, products, error } = await reApplyCoupon({ ...updatedCartData?._doc, ...cartData }, customerDetails, res);
                            if (error) return;
                            cartData.total = total - savings;
                            cartData.savings = savings;
                            cartData.products = products;
                            if (updatedCartData?.loyaltyApplied) cartData.total = (cartData.total - updatedCartData?.loyaltyAmount).toFixed(2);
                        }

                        // const cartData = {
                        //     baseTotal: totalSum.toFixed(2),
                        //     total: updatedCartData?.savings ? (totalSum - updatedCartData?.savings).toFixed(2) : totalSum.toFixed(2),
                        // };

                        const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                        if (result) {
                            helper.deliverResponse(res, 200, { ...result?._doc, currency: store.currencyCode }, {
                                error_code: messages.CART.UPDATED.error_code,
                                error_message: translation?.popup?.cartUpdate[language],
                            });
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                        }
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to update cart",
                            }
                        );
                    }
                } else {
                    cartDetails.products.push({
                        product: productDetails?._id,
                        quantity: body?.quantity,
                        priceTotal: totalPrice.toFixed(2),
                        currency: "AED",
                        size: sizeDetails?.size?._id,
                        fromTryCart: body?.orderNo ? true : false,
                        orderNo: body?.orderNo ? body?.orderNo : null,
                    });

                    const updatedCart = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                    if (updatedCart) {
                        let totalSum = 0;
                        const cartProducts = updatedCart?.products;
                        for (let product of cartProducts) {
                            totalSum += Number(product?.priceTotal);
                        }

                        let cartTotal = totalSum.toFixed(2);
                        if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
                        // if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
                        // if (tryCartAmount) {
                        //     updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                        //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
                        // }

                        const cartData = {
                            // tryCartDeduct: updatedCart?.tryCartDeduct,
                            baseTotal: totalSum.toFixed(2),
                            total: cartTotal,
                        };

                        if (updatedCart?.couponApplied) {
                            const { savings, total, products, error } = await reApplyCoupon({ ...updatedCart?._doc, ...cartData }, customerDetails, res);
                            if (error) return;
                            cartData.total = total - savings;
                            cartData.savings = savings;
                            cartData.products = products;
                            if (updatedCart?.loyaltyApplied) cartData.total = (cartData.total - updatedCart?.loyaltyAmount).toFixed(2);
                        }

                        // const cartData = {
                        //     baseTotal: totalSum.toFixed(2),
                        //     total: updatedCart?.savings ? (totalSum - updatedCart?.savings).toFixed(2) : totalSum.toFixed(2),
                        // };

                        const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                        if (result) {
                            helper.deliverResponse(res, 200, { ...result?._doc, currency: store.currencyCode }, {
                                error_code: messages.CART.ADDED.error_code,
                                error_message: translation?.popup?.cartAdd[language],
                            });
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                        }
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to add new product to cart",
                            }
                        );
                    }
                }
            }
        } else {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                    error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                }
            );
        }

        // if (body?.size && productDetails?.customizable) {
        //     const sizeDetails = productDetails?.sizes?.find((size) => size?.size?._id == body?.size);
        //     totalPrice = sizeDetails?.offerPrice ? sizeDetails?.offerPrice * body?.quantity : sizeDetails?.price * body?.quantity;

        //     if (sizeDetails?.stock >= body?.quantity) {
        //         let tryCartAmount = null;
        //         if (body?.orderNo) {
        //             const updatedOrder = await orderService.pull(
        //                 {
        //                     orderNo: body?.orderNo,
        //                     isTryCart: true,
        //                     isDelete: false,
        //                     isActive: true,
        //                 },
        //                 { products: { product: body?.product, size: body?.size } }
        //             );

        //             tryCartAmount = updatedOrder?.total;

        //             if (updatedOrder && updatedOrder?.products?.length == 0) {
        //                 await orderService.update({ orderNo: body?.orderNo }, { isDelete: true });
        //             }
        //         }

        //         if (!cartDetails) {
        //             const count = await service.count({});
        //             let products = [];
        //             if (!isGuest) body.customer = customerDetails?._id;
        //             else body.devicetoken = devicetoken;

        //             body.products = products;
        //             body.refid = count + 1;
        //             body.total = totalPrice.toFixed(2);
        //             body.baseTotal = totalPrice.toFixed(2);
        //             body.date = { added: new Date().toISOString() };
        //             products.push({
        //                 product: productDetails?._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: totalPrice.toFixed(2),
        //                 currency: "AED",
        //                 size: sizeDetails?.size?._id,
        //                 fromTryCart: body?.orderNo ? true : false,
        //                 orderNo: body?.orderNo ? body?.orderNo : null,
        //             });
        //             // if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
        //             const cart = await service.create(body);
        //             if (cart) {
        //                 helper.deliverResponse(res, 200, cart, {
        //                     error_code: messages.CART.ADDED.error_code,
        //                     error_message: translation?.popup?.cartAdd[language],
        //                 });
        //             } else {
        //                 helper.deliverResponse(
        //                     res,
        //                     422,
        //                     {},
        //                     {
        //                         error_code: 1,
        //                         error_message: "Failed to add cart",
        //                     }
        //                 );
        //             }
        //         } else {
        //             const cartid = cartDetails?.refid;
        //             let updatedProduct = false;
        //             for (let product of cartDetails?.products) {
        //                 if (product?.lensId) {
        //                     continue;
        //                 }

        //                 // console.log( product?._id, body?.lensId);
        //                 // console.log(String(product?._id) === String(body?.lensId));
        //                 // if(String(product?._id) === String(body?.lensId)) {
        //                 if (String(product.product?._id) === String(productDetails?._id)) {
        //                     if (String(product?.size?._id) == String(sizeDetails?.size?._id)) {
        //                         if (sizeDetails?.stock < product?.quantity + body?.quantity) {
        //                             helper.deliverResponse(
        //                                 res,
        //                                 422,
        //                                 {},
        //                                 {
        //                                     error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
        //                                     error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
        //                                 }
        //                             );
        //                             return;
        //                         }
        //                         product.quantity += body?.quantity;
        //                         let productPrice = sizeDetails?.offerPrice ? sizeDetails?.offerPrice * product?.quantity : sizeDetails?.price * product?.quantity;
        //                         product.priceTotal = Number(productPrice).toFixed(2);
        //                         product.productPrice = productPrice;
        //                         product.fromTryCart = body?.orderNo ? true : false;
        //                         product.orderNo = body?.orderNo ? body?.orderNo : null;
        //                         updatedProduct = true;
        //                         break;
        //                     }
        //                 }
        //             }

        //             if (updatedProduct) {
        //                 const updatedCartData = await service.update({ _id: cartDetails?._id }, cartDetails);
        //                 if (updatedCartData) {
        //                     let totalSum = 0;
        //                     let totalExcContact = 0
        //                     const cartProducts = updatedCartData?.products;
        //                     for (let product of cartProducts) {
        //                         totalSum += Number(product?.priceTotal);
        //                         if (product?.type !== "contactLens") {
        //                             totalExcContact += Number(product?.priceTotal);
        //                         }
        //                     }
        //                     let cartTotal = totalSum.toFixed(2);
        //                     if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
        //                     // if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
        //                     // if (tryCartAmount) {
        //                     //     updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
        //                     //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
        //                     // }
        //                     const cartData = {
        //                         // tryCartDeduct: updatedCartData?.tryCartDeduct,
        //                         baseTotal: totalSum.toFixed(2),
        //                         total: cartTotal,
        //                         totalExcContact
        //                     };

        //                     // const cartData = {
        //                     //     baseTotal: totalSum.toFixed(2),
        //                     //     total: updatedCartData?.savings ? (totalSum - updatedCartData?.savings).toFixed(2) : totalSum.toFixed(2),
        //                     // };

        //                     const result = await service.update({ _id: cartDetails?._id }, cartData);
        //                     if (result) {
        //                         helper.deliverResponse(res, 200, result, {
        //                             error_code: messages.CART.UPDATED.error_code,
        //                             error_message: translation?.popup?.cartUpdate[language],
        //                         });
        //                     } else {
        //                         helper.deliverResponse(
        //                             res,
        //                             422,
        //                             {},
        //                             {
        //                                 error_code: 1,
        //                                 error_message: "Failed to update cart",
        //                             }
        //                         );
        //                     }
        //                 } else {
        //                     helper.deliverResponse(
        //                         res,
        //                         422,
        //                         {},
        //                         {
        //                             error_code: 1,
        //                             error_message: "Failed to update cart",
        //                         }
        //                     );
        //                 }
        //             } else {
        //                 cartDetails.products.push({
        //                     product: productDetails?._id,
        //                     quantity: body?.quantity,
        //                     priceTotal: totalPrice.toFixed(2),
        //                     currency: "AED",
        //                     size: sizeDetails?.size?._id,
        //                     fromTryCart: body?.orderNo ? true : false,
        //                     orderNo: body?.orderNo ? body?.orderNo : null,
        //                 });

        //                 const updatedCart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //                 if (updatedCart) {
        //                     let totalSum = 0;
        //                     let totalExcContact = 0;
        //                     const cartProducts = updatedCart?.products;
        //                     for (let product of cartProducts) {
        //                         totalSum += Number(product?.priceTotal);
        //                         if (product?.type !== "contactLens") {
        //                             totalExcContact += Number(product?.priceTotal);
        //                         }
        //                     }
        //                     let cartTotal = totalSum.toFixed(2);
        //                     if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
        //                     // if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
        //                     // if (tryCartAmount) {
        //                     //     updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
        //                     //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
        //                     // }

        //                     const cartData = {
        //                         // tryCartDeduct: updatedCart?.tryCartDeduct,
        //                         baseTotal: totalSum.toFixed(2),
        //                         total: cartTotal,
        //                         totalExcContact
        //                     };
        //                     // const cartData = {
        //                     //     baseTotal: totalSum.toFixed(2),
        //                     //     total: updatedCart?.savings ? (totalSum - updatedCart?.savings).toFixed(2) : totalSum.toFixed(2),
        //                     // };

        //                     const result = await service.update({ _id: cartDetails?._id }, cartData);
        //                     if (result) {
        //                         helper.deliverResponse(res, 200, result, {
        //                             error_code: messages.CART.ADDED.error_code,
        //                             error_message: translation?.popup?.cartAdd[language],
        //                         });
        //                     } else {
        //                         helper.deliverResponse(
        //                             res,
        //                             422,
        //                             {},
        //                             {
        //                                 error_code: 1,
        //                                 error_message: "Failed to update cart",
        //                             }
        //                         );
        //                     }
        //                 } else {
        //                     helper.deliverResponse(
        //                         res,
        //                         422,
        //                         {},
        //                         {
        //                             error_code: 1,
        //                             error_message: "Failed to add new product to cart",
        //                         }
        //                     );
        //                 }
        //             }
        //         }
        //     } else {
        //         return helper.deliverResponse(
        //             res,
        //             422,
        //             {},
        //             {
        //                 error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
        //                 error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
        //             }
        //         );
        //     }
        // } else {
        //     if (productDetails?.stock >= body?.quantity) {
        //         totalPrice = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed * body?.quantity : productDetails?.price?.aed * body?.quantity;
        //         let tryCartAmount = null;

        //         if (body?.orderNo) {
        //             const updatedOrder = await orderService.pull(
        //                 {
        //                     orderNo: body?.orderNo,
        //                     isTryCart: true,
        //                     isDelete: false,
        //                     isActive: true,
        //                 },
        //                 { $pull: { products: { product: body?.product } } }
        //             );

        //             tryCartAmount = updatedOrder?.total;

        //             if (updatedOrder && updatedOrder?.products?.length === 0) {
        //                 await orderService.update({ orderNo: body?.orderNo }, { isDelete: false });
        //             }
        //         }

        //         if (!cartDetails) {
        //             const count = await service.count({});
        //             let products = [];
        //             if (!isGuest) body.customer = customerDetails?._id;
        //             else body.devicetoken = devicetoken;

        //             // body.customer = customerDetails?._id;
        //             body.products = products;
        //             body.refid = count + 1;
        //             body.total = totalPrice.toFixed(2);
        //             body.baseTotal = totalPrice.toFixed(2);
        //             body.date = { added: new Date().toISOString() };
        //             products.push({
        //                 product: productDetails?._id,
        //                 quantity: body?.quantity,
        //                 priceTotal: totalPrice.toFixed(2),
        //                 currency: "AED",
        //                 fromTryCart: body?.orderNo ? true : false,
        //                 orderNo: body?.orderNo ? body?.orderNo : null,
        //             });
        //             // if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
        //             const cart = await service.create(body);
        //             if (cart) {
        //                 helper.deliverResponse(res, 200, cart, {
        //                     error_code: messages.CART.ADDED.error_code,
        //                     error_message: translation?.popup?.cartAdd[language],
        //                 });
        //             } else {
        //                 helper.deliverResponse(
        //                     res,
        //                     422,
        //                     {},
        //                     {
        //                         error_code: 1,
        //                         error_message: "Failed to add cart",
        //                     }
        //                 );
        //             }
        //         } else {
        //             const cartid = cartDetails?.refid;
        //             let updatedProduct = false;

        //             for (let product of cartDetails?.products) {
        //                 // if(String(product._id) == String(body?.lensId)) {
        //                 if (String(product.product?._id) === String(productDetails?._id)) {
        //                     if (productDetails?.stock < product.quantity + body?.quantity)
        //                         return helper.deliverResponse(
        //                             res,
        //                             422,
        //                             {},
        //                             {
        //                                 error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
        //                                 error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
        //                             }
        //                         );
        //                     product.quantity += body?.quantity;
        //                     let productPrice = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;
        //                     product.priceTotal = (product.quantity * productPrice).toFixed(2);
        //                     product.fromTryCart = body?.orderNo ? true : false;
        //                     product.orderNo = body?.orderNo ? body?.orderNo : null;
        //                     updatedProduct = true;
        //                     break;
        //                 }
        //                 // }
        //             }

        //             if (updatedProduct) {
        //                 const updatedCartData = await service.update({ _id: cartDetails?._id }, cartDetails);
        //                 if (updatedCartData) {
        //                     let totalSum = 0;
        //                     const cartProducts = updatedCartData?.products;
        //                     for (let product of cartProducts) {
        //                         totalSum += Number(product?.priceTotal);
        //                     }

        //                     let cartTotal = totalSum.toFixed(2);
        //                     if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
        //                     // if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
        //                     // if (tryCartAmount) {
        //                     //     updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
        //                     //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
        //                     // }

        //                     const cartData = {
        //                         // tryCartDeduct: updatedCartData?.tryCartDeduct,
        //                         baseTotal: totalSum.toFixed(2),
        //                         total: cartTotal,
        //                     };
        //                     // const cartData = {
        //                     //     baseTotal: totalSum.toFixed(2),
        //                     //     total: updatedCartData?.savings ? (totalSum - updatedCartData?.savings).toFixed(2) : totalSum.toFixed(2),
        //                     // };

        //                     const result = await service.update({ _id: cartDetails?._id }, cartData);
        //                     if (result) {
        //                         helper.deliverResponse(res, 200, result, {
        //                             error_code: messages.CART.ADDED.error_code,
        //                             error_message: translation?.popup?.cartAdd[language],
        //                         });
        //                     } else {
        //                         helper.deliverResponse(
        //                             res,
        //                             422,
        //                             {},
        //                             {
        //                                 error_code: 1,
        //                                 error_message: "Failed to update cart",
        //                             }
        //                         );
        //                     }
        //                 } else {
        //                     helper.deliverResponse(
        //                         res,
        //                         422,
        //                         {},
        //                         {
        //                             error_code: 1,
        //                             error_message: "Failed to update cart",
        //                         }
        //                     );
        //                 }
        //             } else {
        //                 cartDetails.products.push({
        //                     product: productDetails?._id,
        //                     quantity: body?.quantity,
        //                     priceTotal: totalPrice.toFixed(2),
        //                     currency: "AED",
        //                     fromTryCart: body?.orderNo ? true : false,
        //                     orderNo: body?.orderNo ? body?.orderNo : null,
        //                 });

        //                 const updatedCart = await service.update({ _id: cartDetails?._id }, cartDetails);
        //                 if (updatedCart) {
        //                     let totalSum = 0;
        //                     const cartProducts = updatedCart?.products;
        //                     for (let product of cartProducts) {
        //                         totalSum += Number(product?.priceTotal);
        //                     }

        //                     let cartTotal = totalSum.toFixed(2);
        //                     if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
        //                     // if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
        //                     // if (tryCartAmount) {
        //                     //     updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
        //                     //     cartTotal = (totalSum - tryCartAmount).toFixed(2);
        //                     // }

        //                     const cartData = {
        //                         // tryCartDeduct: updatedCart?.tryCartDeduct,
        //                         baseTotal: totalSum.toFixed(2),
        //                         total: cartTotal,
        //                     };

        //                     // const cartData = {
        //                     //     baseTotal: totalSum.toFixed(2),
        //                     //     total: updatedCart?.savings ? (totalSum - updatedCart?.savings).toFixed(2) : totalSum.toFixed(2),
        //                     // };

        //                     const result = await service.update({ _id: cartDetails?._id }, cartData);
        //                     if (result) {
        //                         helper.deliverResponse(res, 200, result, {
        //                             error_code: messages.CART.ADDED.error_code,
        //                             error_message: translation?.popup?.cartAdd[language],
        //                         });
        //                     } else {
        //                         helper.deliverResponse(
        //                             res,
        //                             422,
        //                             {},
        //                             {
        //                                 error_code: 1,
        //                                 error_message: "Failed to update cart",
        //                             }
        //                         );
        //                     }
        //                 } else {
        //                     helper.deliverResponse(
        //                         res,
        //                         422,
        //                         {},
        //                         {
        //                             error_code: 1,
        //                             error_message: "Failed to add new product to cart",
        //                         }
        //                     );
        //                 }
        //             }
        //         }
        //     } else {
        //         helper.deliverResponse(
        //             res,
        //             200,
        //             {},
        //             {
        //                 error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
        //                 error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
        //             }
        //         );
        //     }
        // }

    } catch (error) {
        console.log("error caught in add to cart -->", error);
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.loyaltyPoints = async (req, res) => {
    try {
        const { customerId } = res?.locals?.user;
        const customer = await customerService.findOne({ refid: customerId, isDelete: false });
        if (!customer) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
            return;
        }

        const payload = {
            COUPONCODEENQREQ: {
                HEADERINFO: {
                    REQUESTID: Math.floor(Math.random() * 10000),
                },
                COUPONCODEINFO: {
                    COUPONCODE: "BALANCES",
                    STORENUMBER: "1",
                    SOURCETYPE: "eComm",
                    CUSTOMERID: "",
                    CARDNUMBER: "",
                    PHONE: customer?.mobile,
                    EMAIL: customer?.email,
                },
                USERDETAILS: {
                    // USERNAME: "webcastle",
                    // ORGID: "webcastle",
                    // TOKEN: "0J3RIVME081FARD3",
                    USERNAME: CRM_USERNAME, // Mandatory field
                    TOKEN: CRM_TOKEN, // Mandatory field
                    ORGID: CRM_ORG_ID, // Mandatory field
                },
            },
        };
        console.log(payload)
        const response = await axios.post("http://app.optculture.com/subscriber/CouponCodeEnquiryRequestOPT.mqrm", payload);
        if (response?.data?.COUPONCODERESPONSE?.STATUSINFO?.ERRORCODE == 0) {
            const data = {
                redeemableAmount: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.REDEEMABLEAMOUNT,
                pointsEarned: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.POINTSEARNED,
                lifetimePoints: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.LIFETIMEPOINTS,
                issuanceDesc: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.ISSUANCEDESC,
                redeemDesc: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.REDEEMDESC,
                cardNumber: response?.data?.COUPONCODERESPONSE?.LOYALTYINFO?.CARDNUMBER,
            };

            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.redeemLoyalty = async (req, res) => {
    try {
        const { storeid } = req?.headers;
        const { customerId } = res?.locals?.user;
        const { body } = req;
        const customer = await customerService.findOne({ refid: customerId, isDelete: false });
        if (!customer) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
            return;
        }
        const cartDetails = await service.findOne({
            customer: customer?._id,
            isDelete: false,
            isActive: true,
            isPurchased: false,
            storeId: storeid
        });
        if (cartDetails && cartDetails?.products?.length > 0) {
            if (body?.enteredValue) {
                const loyaltyAmount = body?.enteredValue > cartDetails?.total ? cartDetails?.total : body?.enteredValue;
                const payload = {
                    header: {
                        requestId: Math.floor(Math.random() * 10000),
                        requestDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                        storeNumber: "1",
                        docSID: cartDetails?._id?.toString(),
                        sourceType: "Store",
                    },
                    membership: {
                        cardNumber: body?.cardNumber,
                        phoneNumber: customer?.mobile,
                    },
                    amount: {
                        type: "Reward",
                        enteredValue: loyaltyAmount,
                        valueCode: "Currency",
                    },
                    discounts: {
                        appliedPromotion: "Y",
                        promotions: [
                            {
                                name: "WC",
                            },
                        ],
                    },
                    user: {
                        // userName: "webcastle",
                        // organizationId: "webcastle",
                        // token: "0J3RIVME081FARD3",
                        userName: CRM_USERNAME, // Mandatory field
                        token: CRM_TOKEN, // Mandatory field
                        organizationId: CRM_ORG_ID // Mandatory field
                    },
                };
                const response = await axios.post("https://app.optculture.com/subscriber/OCLoyaltyRedemption.mqrm", payload);
                console.log(payload)
                console.log(response.data)
                if (response?.data?.status?.errorCode == "0") {
                    let total = 0;
                    // let baseTotal = 0;
                    let loyaltyDiscount = 0;
                    // let products = cartDetails?.products;
                    // for (let product of products) {
                    //     baseTotal += product?.priceTotal;
                    // }
                    // total = baseTotal;
                    loyaltyDiscount = loyaltyAmount;
                    // total = (total - loyaltyDiscount).toFixed(2);
                    const cartPayload = {
                        // products: products,
                        // total: total,
                        loyaltyAmount: loyaltyDiscount,
                        // baseTotal: baseTotal,
                        loyaltyApplied: true,
                        loyaltyDate: new Date(),
                        cardNumber: body?.cardNumber,
                    };
                    const cartUpdate = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartPayload);
                    if (!cartUpdate) {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: messages.SERVER_ERROR.error_code,
                                error_message: "Failed to update cart",
                            }
                        );
                        return;
                    }
                    helper.deliverResponse(res, 200, response?.data, {
                        error_code: response?.data?.status?.errorCode,
                        error_message: response?.data?.status?.message,
                    });
                } else {
                    helper.deliverResponse(res, 422, response?.data?.status, {
                        error_code: response?.data?.status?.errorCode,
                        error_message: response?.data?.status?.message,
                    });
                    return;
                }
            } else {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Please enter a value",
                    }
                );
                return;
            }
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CART.EMPTY.error_code,
                    error_message: messages.CART.EMPTY.error_message,
                }
            );
            return;
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.contactLensCart = async (req, res) => {
    try {
        const { authorization, devicetoken } = req.headers;
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const translation = await db.Texts.findOne({ storeId: storeid });
        let customerId = null;
        let isGuest = false;
        let customerDetails = null;
        let cartDetails = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) customerId = null;
                customerId = decoded?.customerId;
            });
            isGuest = false;
            customerDetails = await customerService.findOne({
                refid: customerId,
                isDelete: false,
                isActive: true,
            });
            cartDetails = await service.findOne({
                customer: customerDetails?._id,
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        } else if (devicetoken) {
            isGuest = true;
            cartDetails = await service.findOne({
                devicetoken: devicetoken,
                customer: { $exists: false },
                isDelete: false,
                isActive: true,
                isPurchased: false,
                storeId: storeid
            });
        }

        let { body } = req;

        const productDetails = await productService.findOne({
            _id: body?.product,
            isDelete: false,
            storeId: storeid
        });


        let flag = false
        if (body?.contactLens) {
            if (body.contactLens?.sphLeft) {
                const leftSphData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.sphLeft,
                    storeId: storeid
                })
                if (leftSphData) {
                    leftSph = leftSphData?.name
                    flag = isLensPriceable(leftSphData)
                }
            }

            if (body.contactLens?.sphRight) {
                const rightSphData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.sphRight,
                    storeId: storeid
                })
                if (rightSphData) {
                    rightSph = rightSphData?.name
                    flag = isLensPriceable(rightSphData)
                }
            }

            if (body.contactLens?.cylLeft) {
                const leftCylData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.cylLeft,
                    storeId: storeid
                })
                if (leftCylData) {
                    leftCyl = leftCylData?.name
                    flag = isLensPriceable(leftCylData)
                }
            }

            if (body.contactLens?.cylRight) {
                const rightCylData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.cylRight,
                    storeId: storeid
                })
                if (rightCylData) {
                    rightCyl = rightCylData?.name
                    flag = isLensPriceable(rightCylData)
                }
            }

            if (body.contactLens?.axisLeft) {
                const leftAxisData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.axisLeft,
                    storeId: storeid
                })
                if (leftAxisData) {
                    leftAxis = leftAxisData?.name
                    flag = isLensPriceable(leftAxisData)
                }
            }

            if (body.contactLens?.axisRight) {
                const rightAxisData = await contactLensPowerService.findOne({
                    _id: body.contactLens?.axisRight,
                    storeId: storeid
                })
                if (rightAxisData) {
                    rightAxis = rightAxisData?.name
                    flag = isLensPriceable(rightAxisData)
                }
            }
        }
        console.log('flag', flag)
        let totalPrice;
        let sizeDetails;
        let stock;
        let price;
        let tryCartAmount = null;

        price = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;
        if (flag) price += (productDetails?.powerPrice ?? 0);
        stock = productDetails?.stock;
        totalPrice = price * (body?.contactLens?.multiple ? 2 * body?.quantity : body?.quantity);


        if (body?.orderNo) {
            const updatedOrder = await orderService.pull(
                {
                    orderNo: body?.orderNo,
                    isTryCart: true,
                    isDelete: false,
                    isActive: true,
                },
                { $pull: { products: { product: body?.product } } }
            );

            tryCartAmount = updatedOrder?.total;

            if (updatedOrder && updatedOrder?.products?.length === 0) {
                await orderService.update({ orderNo: body?.orderNo }, { isDelete: false });
            }
        }

        if (!cartDetails) {
            const count = await service.count({});
            let products = [];
            if (!isGuest) body.customer = customerDetails?._id;
            else body.devicetoken = devicetoken;
            let contactLensHash = null;
            const hash = await bcrypt.hash(body.contactLens?.toString(), 10);

            contactLensHash = hash;

            body.products = products;
            body.refid = count + 1;
            body.total = tryCartAmount ? (totalPrice - tryCartAmount).toFixed(2) : totalPrice.toFixed(2);
            body.baseTotal = totalPrice.toFixed(2);
            body.date = { added: new Date().toISOString() };
            body.storeId = storeid;
            products.push({
                product: productDetails?._id,
                quantity: body?.quantity,
                priceTotal: totalPrice.toFixed(2),
                currency: "AED",
                contactSize: sizeDetails?.size?._id,
                contactLens: body?.contactLens,
                contactLensHash: contactLensHash,
            });
            if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
            const cart = await service.create(body);
            if (cart) {
                helper.deliverResponse(res, 200, cart, {
                    error_code: messages.CART.ADDED.error_code,
                    error_message: translation?.popup?.cartAdd[language],
                });
                return;

            } else {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: "Failed to add cart",
                    }
                );
                return
            }
        } else {
            const cartid = cartDetails?.refid;
            let updatedProduct = false;
            let shouldAddAsNew = false;

            if (cartDetails?.couponApplied) {
                await removeCoupon(cartDetails, customerDetails);
            }

            for (let product of cartDetails?.products) {
                if (String(product.product?._id) === String(productDetails?._id)) {
                    try {
                        const result = objectHash(body?.contactLens) === product?.contactLensHash;

                        if (result) {
                            if (sizeDetails?.stock < product.quantity + body?.quantity) {
                                return helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                                        error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                                    }
                                );
                            }
                            product.quantity += body?.quantity;
                            product.priceTotal = price * product.quantity;
                            updatedProduct = true;
                            break;
                        } else {
                            shouldAddAsNew = true;
                        }
                    } catch (error) {
                        shouldAddAsNew = true;
                        break;
                    }
                } else {
                    shouldAddAsNew = true;
                }
            }

            if (updatedProduct) {
                const updatedCartData = await service.update({ _id: cartDetails?._id }, cartDetails);
                if (updatedCartData) {
                    let totalSum = 0;
                    const cartProducts = updatedCartData?.products;
                    for (let product of cartProducts) {
                        totalSum += Number(product?.priceTotal);
                    }

                    let cartTotal = totalSum.toFixed(2);
                    if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
                    if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
                    if (tryCartAmount) {
                        updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                        cartTotal = (totalSum - tryCartAmount).toFixed(2);
                    }

                    const cartData = {
                        tryCartDeduct: updatedCartData?.tryCartDeduct,
                        baseTotal: totalSum.toFixed(2),
                        total: cartTotal,
                    };

                    if (updatedCartData?.couponApplied) {
                        const { savings, total, products, error } = await reApplyCoupon({ ...updatedCartData?._doc, ...cartData }, customerDetails, res);
                        if (error) return;
                        cartData.total = total - savings;
                        cartData.savings = savings;
                        cartData.products = products;
                        if (updatedCartData?.loyaltyApplied) cartData.total = (cartData.total - updatedCartData?.loyaltyAmount).toFixed(2);
                    }

                    const result = await service.update({ _id: cartDetails?._id }, cartData);
                    if (result) {
                        helper.deliverResponse(res, 200, result, {
                            error_code: messages.CART.ADDED.error_code,
                            error_message: translation?.popup?.cartAdd[language],
                        });
                        return;
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to update cart",
                            }
                        );
                        return;
                    }
                } else {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Failed to update cart",
                        }
                    );
                    return;
                }
            } else {
                let hashed = objectHash(body.contactLens);
                cartDetails.products.push({
                    product: productDetails?._id,
                    quantity: body?.quantity,
                    priceTotal: totalPrice.toFixed(2),
                    currency: "AED",
                    contactSize: sizeDetails?.size?._id,
                    contactLens: body?.contactLens,
                    contactLensHash: hashed,
                });

                const updatedCart = await service.update({ _id: cartDetails?._id }, cartDetails);
                if (updatedCart) {
                    let totalSum = 0;
                    const cartProducts = updatedCart?.products;
                    for (let product of cartProducts) {
                        totalSum += Number(product?.priceTotal);
                    }

                    let cartTotal = totalSum.toFixed(2);
                    if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
                    if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
                    if (tryCartAmount) {
                        updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                        cartTotal = (totalSum - tryCartAmount).toFixed(2);
                    }

                    const cartData = {
                        tryCartDeduct: updatedCart?.tryCartDeduct,
                        baseTotal: totalSum.toFixed(2),
                        total: cartTotal,
                    };

                    if (updatedCart?.couponApplied) {
                        const { savings, total, products, error } = await reApplyCoupon({ ...updatedCart?._doc, ...cartData }, customerDetails, res);
                        if (error) return;
                        cartData.total = total - savings;
                        cartData.savings = savings;
                        cartData.products = products;
                        if (updatedCart?.loyaltyApplied) cartData.total = (cartData.total - updatedCart?.loyaltyAmount).toFixed(2);
                    }

                    const result = await service.update({ _id: cartDetails?._id }, cartData);
                    if (result) {
                        helper.deliverResponse(res, 200, result, {
                            error_code: messages.CART.ADDED.error_code,
                            error_message: translation?.popup?.cartAdd[language],
                        });
                        return;
                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to update cart",
                            }
                        );
                        return;
                    }
                } else {
                    helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Failed to add new product to cart",
                        }
                    );
                    return;
                }
            }

        }

        ////////////////

        if (body?.size && productDetails?.customizable) {
            console.log(sizeDetails)
            if (sizeDetails?.stock >= body?.quantity) {
                const powerPrice = flag ? productDetails?.powerPrice ? productDetails?.powerPrice : 0 : 0;
                // const total = body?.contactLens?.multiple ? 2 * (productPrice + powerPrice) : 1 * (productPrice + powerPrice);
                const total = productPrice + powerPrice;
                totalPrice = total * (body?.contactLens?.multiple ? 2 * body?.quantity : body?.quantity);
                let tryCartAmount = null;

                if (body?.orderNo) {
                    const updatedOrder = await orderService.pull(
                        {
                            orderNo: body?.orderNo,
                            isTryCart: true,
                            isDelete: false,
                            isActive: true,
                            storeId: storeid
                        },
                        { $pull: { products: { product: body?.product, contactSize: body?.size } } }
                    );

                    tryCartAmount = updatedOrder?.total;

                    if (updatedOrder && updatedOrder?.products?.length === 0) {
                        await orderService.update({ orderNo: body?.orderNo, storeId: storeid }, { isDelete: false });
                    }
                }

                if (!cartDetails) {
                    const count = await service.count({});
                    let products = [];
                    if (!isGuest) body.customer = customerDetails?._id;
                    else body.devicetoken = devicetoken;
                    let contactLensHash = null;
                    const hash = await bcrypt.hash(body.contactLens?.toString(), 10);

                    contactLensHash = hash;

                    body.products = products;
                    body.refid = count + 1;
                    body.total = tryCartAmount ? (totalPrice - tryCartAmount).toFixed(2) : totalPrice.toFixed(2);
                    body.baseTotal = totalPrice.toFixed(2);
                    body.date = { added: new Date().toISOString() };
                    body.storeId = storeid;
                    products.push({
                        product: productDetails?._id,
                        quantity: body?.quantity,
                        priceTotal: totalPrice.toFixed(2),
                        currency: "AED",
                        contactSize: sizeDetails?.size?._id,
                        contactLens: body?.contactLens,
                        contactLensHash: contactLensHash,
                    });
                    if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
                    const cart = await service.create(body);
                    if (cart) {
                        helper.deliverResponse(res, 200, cart, {
                            error_code: messages.CART.ADDED.error_code,
                            error_message: translation?.popup?.cartAdd[language],
                        });
                        return;

                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to add cart",
                            }
                        );
                        return
                    }
                } else {
                    const cartid = cartDetails?.refid;
                    let updatedProduct = false;
                    let shouldAddAsNew = false;


                    for (let product of cartDetails?.products) {
                        if (String(product.product?._id) === String(productDetails?._id)) {
                            try {
                                const result = objectHash(body?.contactLens) === product?.contactLensHash;

                                if (result) {
                                    if (sizeDetails?.stock < product.quantity + body?.quantity) {
                                        return helper.deliverResponse(
                                            res,
                                            422,
                                            {},
                                            {
                                                error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                                                error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                                            }
                                        );
                                    }
                                    product.quantity += body?.quantity;
                                    product.priceTotal = total * (body?.contactLens?.multiple ? 2 * product.quantity : product.quantity);
                                    updatedProduct = true;
                                    break;
                                } else {
                                    shouldAddAsNew = true;
                                }
                            } catch (error) {
                                shouldAddAsNew = true;
                                break;
                            }
                        } else {
                            shouldAddAsNew = true;
                        }
                    }

                    if (updatedProduct) {
                        const updatedCartData = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                        if (updatedCartData) {
                            let totalSum = 0;
                            const cartProducts = updatedCartData?.products;
                            for (let product of cartProducts) {
                                totalSum += Number(product?.priceTotal);
                            }

                            let cartTotal = totalSum.toFixed(2);
                            if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
                            if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
                            if (tryCartAmount) {
                                updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                                cartTotal = (totalSum - tryCartAmount).toFixed(2);
                            }

                            const cartData = {
                                tryCartDeduct: updatedCartData?.tryCartDeduct,
                                baseTotal: totalSum.toFixed(2),
                                total: cartTotal,
                            };

                            const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                            if (result) {
                                helper.deliverResponse(res, 200, result, {
                                    error_code: messages.CART.ADDED.error_code,
                                    error_message: translation?.popup?.cartAdd[language],
                                });
                                return;
                            } else {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: 1,
                                        error_message: "Failed to update cart",
                                    }
                                );
                                return;
                            }
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                            return;
                        }
                    } else {
                        let hashed = objectHash(body.contactLens);
                        cartDetails.products.push({
                            product: productDetails?._id,
                            quantity: body?.quantity,
                            priceTotal: totalPrice.toFixed(2),
                            currency: "AED",
                            contactSize: sizeDetails?.size?._id,
                            contactLens: body?.contactLens,
                            contactLensHash: hashed,
                        });

                        const updatedCart = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                        if (updatedCart) {
                            let totalSum = 0;
                            const cartProducts = updatedCart?.products;
                            for (let product of cartProducts) {
                                totalSum += Number(product?.priceTotal);
                            }

                            let cartTotal = totalSum.toFixed(2);
                            if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
                            if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
                            if (tryCartAmount) {
                                updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                                cartTotal = (totalSum - tryCartAmount).toFixed(2);
                            }

                            const cartData = {
                                tryCartDeduct: updatedCart?.tryCartDeduct,
                                baseTotal: totalSum.toFixed(2),
                                total: cartTotal,
                            };

                            const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                            if (result) {
                                helper.deliverResponse(res, 200, result, {
                                    error_code: messages.CART.ADDED.error_code,
                                    error_message: translation?.popup?.cartAdd[language],
                                });
                                return;
                            } else {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: 1,
                                        error_message: "Failed to update cart",
                                    }
                                );
                                return;
                            }
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to add new product to cart",
                                }
                            );
                            return;
                        }
                    }
                }

            } else {
                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                        error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                    }
                );
            }
        } else {
            if (productDetails?.stock >= body?.quantity) {
                const productPrice = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;
                console.log('productPrice', productPrice)
                const powerPrice = flag ? productDetails?.powerPrice ? productDetails?.powerPrice : 0 : 0;
                // const total = body?.contactLens?.multiple ? 2 * (productPrice + powerPrice) : 1 * (productPrice + powerPrice);
                const total = productPrice + powerPrice;
                totalPrice = total * (body?.contactLens?.multiple ? 2 * body?.quantity : body?.quantity);
                console.log('total', totalPrice)
                let tryCartAmount = null;

                if (body?.orderNo) {
                    const updatedOrder = await orderService.pull(
                        {
                            orderNo: body?.orderNo,
                            isTryCart: true,
                            isDelete: false,
                            isActive: true,
                            storeId: storeid
                        },
                        { $pull: { products: { product: body?.product } } }
                    );

                    tryCartAmount = updatedOrder?.total;

                    if (updatedOrder && updatedOrder?.products?.length === 0) {
                        await orderService.update({ orderNo: body?.orderNo, storeId: storeid }, { isDelete: false });
                    }
                }

                if (!cartDetails) {
                    const count = await service.count({});
                    let products = [];
                    if (!isGuest) body.customer = customerDetails?._id;
                    else body.devicetoken = devicetoken;
                    let contactLensHash = null;
                    console.log(body.contactLens?.toString())
                    const hash = await bcrypt.hash(body.contactLens?.toString(), 10);

                    // if (err) {
                    //     helper.deliverResponse(res, 422, err, {
                    //         error_code: messages.SERVER_ERROR.error_code,
                    //         error_message: messages.SERVER_ERROR.error_message,
                    //     });
                    //     return;
                    // }
                    contactLensHash = hash;

                    body.products = products;
                    body.refid = count + 1;
                    body.total = tryCartAmount ? (totalPrice - tryCartAmount).toFixed(2) : totalPrice.toFixed(2);
                    body.baseTotal = totalPrice.toFixed(2);
                    body.date = { added: new Date().toISOString() };
                    body.storeId = storeid;
                    products.push({
                        product: productDetails?._id,
                        quantity: body?.quantity,
                        priceTotal: totalPrice.toFixed(2),
                        currency: "AED",
                        contactLens: body?.contactLens,
                        contactLensHash: contactLensHash,
                    });
                    if (tryCartAmount) body.tryCartDeduct = tryCartAmount;
                    const cart = await service.create(body);
                    if (cart) {
                        helper.deliverResponse(res, 200, cart, {
                            error_code: messages.CART.ADDED.error_code,
                            error_message: translation?.popup?.cartAdd[language],
                        });
                        return;

                    } else {
                        helper.deliverResponse(
                            res,
                            422,
                            {},
                            {
                                error_code: 1,
                                error_message: "Failed to add cart",
                            }
                        );
                        return
                    }
                } else {
                    const cartid = cartDetails?.refid;
                    let updatedProduct = false;
                    let shouldAddAsNew = false;

                    for (let product of cartDetails?.products) {
                        if (String(product.product?._id) === String(productDetails?._id)) {
                            try {
                                const result = objectHash(body?.contactLens) === product?.contactLensHash;

                                if (result) {
                                    if (productDetails?.stock < product.quantity + body?.quantity) {
                                        return helper.deliverResponse(
                                            res,
                                            422,
                                            {},
                                            {
                                                error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                                                error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                                            }
                                        );
                                    }
                                    product.quantity += body?.quantity;
                                    let productPrice = productDetails?.offerPrice?.aed ? productDetails?.offerPrice?.aed : productDetails?.price?.aed;
                                    let powerPrice = productDetails?.powerPrice ? productDetails?.powerPrice : 0;
                                    let total = body?.contactLens?.multiple ? 2 * (productPrice + powerPrice) : 1 * (productPrice + powerPrice);
                                    product.priceTotal = total * (body?.contactLens?.multiple ? 2 * product.quantity : product.quantity);
                                    updatedProduct = true;
                                    break;
                                } else {
                                    shouldAddAsNew = true;
                                }
                            } catch (error) {
                                shouldAddAsNew = true;
                                break;
                            }
                        } else {
                            shouldAddAsNew = true;
                        }
                    }

                    if (updatedProduct) {
                        const updatedCartData = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                        if (updatedCartData) {
                            let totalSum = 0;
                            const cartProducts = updatedCartData?.products;
                            for (let product of cartProducts) {
                                totalSum += Number(product?.priceTotal);
                            }

                            let cartTotal = totalSum.toFixed(2);
                            if (updatedCartData?.savings) cartTotal = (totalSum - updatedCartData?.savings).toFixed(2);
                            if (updatedCartData?.tryCartDeduct) cartTotal = (totalSum - updatedCartData?.tryCartDeduct).toFixed(2);
                            if (tryCartAmount) {
                                updatedCartData.tryCartDeduct = updatedCartData?.tryCartDeduct ? Number(updatedCartData?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                                cartTotal = (totalSum - tryCartAmount).toFixed(2);
                            }

                            const cartData = {
                                tryCartDeduct: updatedCartData?.tryCartDeduct,
                                baseTotal: totalSum.toFixed(2),
                                total: cartTotal,
                            };

                            const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                            if (result) {
                                helper.deliverResponse(res, 200, result, {
                                    error_code: messages.CART.ADDED.error_code,
                                    error_message: translation?.popup?.cartAdd[language],
                                });
                                return;
                            } else {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: 1,
                                        error_message: "Failed to update cart",
                                    }
                                );
                                return;
                            }
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to update cart",
                                }
                            );
                            return;
                        }
                    } else {
                        let hashed = objectHash(body.contactLens);
                        cartDetails.products.push({
                            product: productDetails?._id,
                            quantity: body?.quantity,
                            priceTotal: totalPrice.toFixed(2),
                            currency: "AED",
                            contactLens: body?.contactLens,
                            contactLensHash: hashed,
                        });

                        const updatedCart = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartDetails);
                        if (updatedCart) {
                            let totalSum = 0;
                            const cartProducts = updatedCart?.products;
                            for (let product of cartProducts) {
                                totalSum += Number(product?.priceTotal);
                            }

                            let cartTotal = totalSum.toFixed(2);
                            if (updatedCart?.savings) cartTotal = (totalSum - updatedCart?.savings).toFixed(2);
                            if (updatedCart?.tryCartDeduct) cartTotal = (totalSum - updatedCart?.tryCartDeduct).toFixed(2);
                            if (tryCartAmount) {
                                updatedCart.tryCartDeduct = updatedCart?.tryCartDeduct ? Number(updatedCart?.tryCartDeduct) + Number(tryCartAmount) : tryCartAmount;
                                cartTotal = (totalSum - tryCartAmount).toFixed(2);
                            }

                            const cartData = {
                                tryCartDeduct: updatedCart?.tryCartDeduct,
                                baseTotal: totalSum.toFixed(2),
                                total: cartTotal,
                            };

                            const result = await service.update({ _id: cartDetails?._id, storeId: storeid }, cartData);
                            if (result) {
                                helper.deliverResponse(res, 200, result, {
                                    error_code: messages.CART.ADDED.error_code,
                                    error_message: translation?.popup?.cartAdd[language],
                                });
                                return;
                            } else {
                                helper.deliverResponse(
                                    res,
                                    422,
                                    {},
                                    {
                                        error_code: 1,
                                        error_message: "Failed to update cart",
                                    }
                                );
                                return;
                            }
                        } else {
                            helper.deliverResponse(
                                res,
                                422,
                                {},
                                {
                                    error_code: 1,
                                    error_message: "Failed to add new product to cart",
                                }
                            );
                            return;
                        }
                    }
                }
            } else {
                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                        error_message: messages.PRODUCTS.LIMITED_STOCK.error_message,
                    }
                );
            }
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.removeCoupon = async (req, res) => {
    try {
        const { storeid } = req?.headers
        const { customerId } = res?.locals?.user;
        const customer = await customerService.findOne({ refid: customerId, isDelete: false });
        if (!customer) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
            return;
        }
        const cartDetails = await service.findOne({
            customer: customer?._id,
            isDelete: false,
            isActive: true,
            isPurchased: false,
            storeId: storeid
        });
        if (!cartDetails) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Cart not found",
                }
            );
            return;
        }
        if (!cartDetails?.couponApplied && !cartDetails?.couponCode) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Coupon not applied",
                }
            );
            return;
        }

        const payload = {
            COUPONCODEREDEEMREQ: {
                HEADERINFO: {
                    REQUESTID: customer?._id?.toString(),
                },
                COUPONCODEINFO: {
                    COUPONCODE: cartDetails?.couponCode,
                    RECEIPTNUMBER: "1",
                    DOCSID: cartDetails?._id?.toString(),
                    SUBSIDIARYNUMBER: "0",
                    STORENUMBER: "1",
                    CUSTOMERID: customer?._id?.toString(),
                    PHONE: customer?.mobile,
                    EMAIL: customer?.email,
                },
                PURCHASECOUPONINFO: {
                    TOTALAMOUNT: cartDetails?.total,
                    TOTALDISCOUNT: cartDetails?.savings,
                    USEDLOYALTYPOINTS: "",
                    TYPE: "Void"
                },
                USERDETAILS: {
                    // USERNAME: "webcastle",
                    // ORGID: "webcastle",
                    // TOKEN: "0J3RIVME081FARD3",
                    USERNAME: CRM_USERNAME, // Mandatory field
                    TOKEN: CRM_TOKEN, // Mandatory field
                    ORGID: CRM_ORG_ID // Mandatory field
                },
            },
        };

        const response = await axios.post("https://app.optculture.com/subscriber/CouponCodeRedeemRequestOPT.mqrm", payload);
        console.log(response?.data)
        if (response?.data?.COUPONCODEREDEEMRESPONSE?.STATUSINFO?.ERRORCODE == "0") {

            let total = 0;
            let baseTotal = 0;
            let savings = 0;
            let products = cartDetails?.products;
            for (let product of products) {
                baseTotal += Number(product?.priceTotal);
                product.couponAmount = 0;
                product.couponCode = "";
            }
            total = baseTotal;
            const data = {
                products: products,
                couponApplied: false,
                couponCode: null,
                total: total,
                baseTotal: baseTotal,
                savings: savings,
            };
            const cartUpdate = await service.update({ _id: cartDetails?._id, storeId: storeid }, data);
            if (cartUpdate) {
                helper.deliverResponse(
                    res,
                    200,
                    {},
                    {
                        error_code: messages.COUPONS.REMOVED.error_code,
                        error_message: messages.COUPONS.REMOVED.error_message,
                    }
                );
                return;
            } else {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: "Failed to remove coupon",
                    }
                );
                return;
            }

        } else {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Internal Server Error",
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.removeLoyalty = async (req, res) => {
    try {
        const { storeid } = req?.headers;
        const { customerId } = res?.locals?.user;
        const customer = await customerService.findOne({ refid: customerId, isDelete: false });
        if (!customer) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.CUSTOMERS.NOT_FOUND.error_code,
                    error_message: messages.CUSTOMERS.NOT_FOUND.error_message,
                }
            );
            return;
        }
        const cartDetails = await service.findOne({
            customer: customer?._id,
            isDelete: false,
            isActive: true,
            isPurchased: false,
            storeId: storeid
        });
        if (!cartDetails) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Cart not found",
                }
            );
            return;
        }
        if (!cartDetails?.loyaltyApplied && !cartDetails?.loyaltyAmount) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Loyalty not applied",
                }
            );
            return;
        }
        const voidPayload = {
            header: {
                requestId: Math.floor(Math.random() * 10000),
                storeNumber: "1",
                requestDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                docSID: cartDetails?._id,
            },
            amount: {
                type: "Void",
                valueCode: "Currency",
                enteredValue: cartDetails?.loyaltyAmount,
            },
            membership: {
                cardNumber: cartDetails?.cardNumber,
                phoneNumber: customer?.mobile,
            },
            discounts: {
                appliedPromotion: "N",
            },
            user: {
                // userName: "webcastle",
                // organizationId: "webcastle",
                // token: "0J3RIVME081FARD3",
                userName: CRM_USERNAME, // Mandatory field
                token: CRM_TOKEN, // Mandatory field
                organizationId: CRM_ORG_ID // Mandatory field
            },
            customer: {
                customerId: "",
                firstName: "",
                lastName: "",
                emailAddress: "",
                addressLine1: "",
                addressLine2: "",
                city: "",
                state: "",
                postal: "",
                country: "",
                birthday: "",
                anniversary: "",
                gender: "",
                phone: customer?.mobile,
            },
        };
        const url = "https://app.optculture.com/subscriber/OCLoyaltyRedemption.mqrm";
        const crmResponse = await axios.post(url, voidPayload);
        if (crmResponse.data && crmResponse.data.status?.errorCode == "0") {
            // let total = 0;
            // let baseTotal = 0;
            let loyaltyAmount = 0;
            // let products = cartDetails?.products;
            // for (let product of products) {
            //     baseTotal += Number(product?.priceTotal);
            // }
            // total = baseTotal;
            const data = {
                // products: products,
                loyaltyApplied: false,
                // total: total,
                // baseTotal: baseTotal,
                loyaltyAmount: loyaltyAmount,
                cardNumber: "",
            };
            const cartUpdate = await service.update({ _id: cartDetails?._id, storeId: storeid }, data);
            if (cartUpdate) {
                helper.deliverResponse(res, 200, crmResponse.data, {
                    error_code: 0,
                    error_message: "Loyalty removed successfully",
                });
            } else {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: "Failed to remove loyalty",
                    }
                );
            }
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.loyaltySchedule = async (req, res) => {
    try {

        const currentDate = new Date()
        const expDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - 1, currentDate.getHours(), currentDate.getMinutes());
        console.log(expDate)
        const carts = await service.find({
            isDelete: false,
            isActive: true,
            isPurchased: false,
            loyaltyApplied: true,
            loyaltyDate: { $lte: expDate }
        });

        if (carts.length < 1) return;

        for (let cart of carts) {
            console.log(cart)
            const customer = await customerService.findOne({ _id: cart.customer });
            if (!customer) return;

            const voidPayload = {
                header: {
                    requestId: Math.floor(Math.random() * 10000),
                    storeNumber: "1",
                    requestDate: moment().format("YYYY-MM-DD HH:mm:ss"),
                    docSID: cart?._id,
                },
                amount: {
                    type: "Void",
                    valueCode: "Currency",
                    enteredValue: cart?.loyaltyAmount,
                },
                membership: {
                    cardNumber: customer?.loyaltyNumber,
                    phoneNumber: customer?.mobile,
                },
                discounts: {
                    appliedPromotion: "N",
                },
                user: {
                    // userName: "webcastle",
                    // organizationId: "webcastle",
                    // token: "0J3RIVME081FARD3",
                    userName: CRM_USERNAME, // Mandatory field
                    token: CRM_TOKEN, // Mandatory field
                    organizationId: CRM_ORG_ID // Mandatory field
                },
                customer: {
                    customerId: "",
                    firstName: "",
                    lastName: "",
                    emailAddress: "",
                    addressLine1: "",
                    addressLine2: "",
                    city: "",
                    state: "",
                    postal: "",
                    country: "",
                    birthday: "",
                    anniversary: "",
                    gender: "",
                    phone: customer?.mobile,
                },
            };
            const url = "https://app.optculture.com/subscriber/OCLoyaltyRedemption.mqrm";
            const crmResponse = await axios.post(url, voidPayload);
            if (crmResponse.data && crmResponse.data.status?.errorCode == "0") {
                // let total = 0;
                // let baseTotal = 0;
                let loyaltyAmount = 0;
                // let products = cartDetails?.products;
                // for (let product of products) {
                //     baseTotal += Number(product?.priceTotal);
                // }
                // total = baseTotal;
                const data = {
                    // products: products,
                    loyaltyApplied: false,
                    // total: total,
                    // baseTotal: baseTotal,
                    loyaltyAmount: loyaltyAmount,
                };
                const cartUpdate = await service.update({ _id: cart?._id }, data);
                if (cartUpdate) {
                    console.log("Loyalty removed successfully")
                } else {
                    console.log("Failed to remove loyalty")
                }
            }

        }

    } catch (error) {
        console.log(error)
    }
};

exports.couponSchedule = async (req, res) => {
    try {

        const currentDate = new Date()
        const expDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - 1, currentDate.getHours(), currentDate.getMinutes());
        console.log(expDate)
        const carts = await service.find({
            isDelete: false,
            isActive: true,
            isPurchased: false,
            couponApplied: true,
            couponDate: { $lte: expDate }
        });

        if (carts.length < 1) return;

        for (let cart of carts) {
            console.log(cart)
            const customer = await customerService.findOne({ _id: cart.customer });
            if (!customer) return;

            const payload = {
                COUPONCODEREDEEMREQ: {
                    HEADERINFO: {
                        REQUESTID: customer?._id?.toString(),
                    },
                    COUPONCODEINFO: {
                        COUPONCODE: cart?.couponCode,
                        RECEIPTNUMBER: "1",
                        DOCSID: cart?._id?.toString(),
                        SUBSIDIARYNUMBER: "0",
                        STORENUMBER: "1",
                        CUSTOMERID: customer?._id?.toString(),
                        PHONE: customer?.mobile,
                        EMAIL: customer?.email,
                    },
                    PURCHASECOUPONINFO: {
                        TOTALAMOUNT: cart?.total,
                        TOTALDISCOUNT: cart?.savings,
                        USEDLOYALTYPOINTS: "",
                        TYPE: "Void"
                    },
                    USERDETAILS: {
                        // USERNAME: "webcastle",
                        // ORGID: "webcastle",
                        // TOKEN: "0J3RIVME081FARD3",
                        USERNAME: CRM_USERNAME, // Mandatory field
                        TOKEN: CRM_TOKEN, // Mandatory field
                        ORGID: CRM_ORG_ID // Mandatory field
                    },
                },
            };

            const response = await axios.post("https://app.optculture.com/subscriber/CouponCodeRedeemRequestOPT.mqrm", payload);

            if (response?.data?.COUPONCODEREDEEMRESPONSE?.STATUSINFO?.ERRORCODE == "0") {

                let total = 0;
                let baseTotal = 0;
                let savings = 0;
                let products = cart?.products;
                for (let product of products) {
                    baseTotal += Number(product?.priceTotal);
                }
                total = baseTotal;
                const data = {
                    products: products,
                    couponApplied: false,
                    couponCode: null,
                    total: total,
                    baseTotal: baseTotal,
                    savings: savings,
                };
                const cartUpdate = await service.update({ _id: cart?._id }, data);
                if (cartUpdate) {
                    console.log("Sccess remove coupon")
                    return;
                } else {
                    console.log("Failed to remove coupon")
                }

            } else {
                console.log("Internal Server Error");
            }

        }

    } catch (error) {
        console.log(error)
    }
};

function fixResponseString(str) {
    // Remove the trailing comma inside objects
    let fixedStr = str.replace(/,(\s*\})/g, '$1');

    // Add a comma after "status": "success"
    fixedStr = fixedStr.replace(/("status": "success")(\n)/, '$1,$2');

    // Ensure the closing brace for the entire JSON is correctly placed
    fixedStr = fixedStr.trim();

    // Check if the last character is a closing brace; if not, append one
    if (!fixedStr.endsWith('}')) {
        fixedStr += '\n}';
    }

    return fixedStr;
}
