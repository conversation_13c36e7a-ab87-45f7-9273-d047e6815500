const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/subscription.service");
const summaryService = require("../services/subscribe.summary.service");
const productService = require("../services/products.service");
const customerService = require("../services/customer.service");
const subscribePlanService = require("../services/subscribe.plan.service");
const axios = require("axios");
const { NOONAPI } = require("../config/constants/noon");

exports.subscribe = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { customerId } = res.locals.user;
        const { body } = req;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false
        });
        const productDetail = await productService.findOne({ _id: body?.product, isDelete: false, storeId: storeid });
        if (productDetail) {
            if (productDetail?.stock < 1) {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.PRODUCTS.LIMITED_STOCK.error_code,
                        error_message: messages.PRODUCTS.LIMITED_STOCK.error_message
                    }
                );
                return;
            }
            console.log(body)
            body.refid = (await summaryService.count({})) + 1;
            body.customer = customerDetails?._id;
            body.storeId = storeid;
            const summaryData = await summaryService.findOne({ customer: customerDetails?._id, storeId: storeid });
            let response;
            if (summaryData) {
                response = await summaryService.update({ customer: customerDetails?._id, storeId: storeid }, body);
            } else {
                response = await summaryService.create(body);
            }

            if (response instanceof Error) {
                helper.deliverResponse(res, 422, response, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message
                });
                return;
            }
            helper.deliverResponse(res, 200, response?._id, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message
                }
            );
            return;
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.subscribeSummary = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language: "en";
        storeid = storeid ? storeid: "ae";
        const { customerId } = res.locals.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false
        });
        const { body } = req;
        const subscribeSummary = await summaryService.findOne({ customer: customerDetails?._id, storeId: storeid });
        const productDetails = await productService.findOne({
            _id: subscribeSummary?.product,
            isDelete: false,
            storeId: storeid
        });
        const subscriptionPlan = await subscribePlanService.findOne({
            _id: subscribeSummary?.plan,
            isDelete: false,
            storeId: storeid
        });
        if (productDetails) {
            let products = [];
            let summary = [];
            const productPrice = productDetails?.offerPrice?.aed
                ? productDetails?.offerPrice?.aed
                : productDetails?.price?.aed;
            const powerPrice = productDetails?.powerPrice ? productDetails?.powerPrice : 0;
            const total = subscribeSummary?.contactLens?.multiple
                ? 2 * (productPrice + powerPrice)
                : 1 * (productPrice + powerPrice);
            const totalPrice = total * 1;

            products.push({
                slug: productDetails?.slug,
                productid: productDetails?._id,
                name: productDetails?.name[language],
                thumbnail: productDetails?.thumbnail
                    ? process.env.DOMAIN + productDetails?.thumbnail
                    : null,
                currency: "AED",
                quantity: 1,
                priceTotal: Number(totalPrice).toFixed(2),
                color: productDetails?.color?.name ? productDetails?.color?.name?.[language] : null
            });

            summary.push({
                text: "Subtotal",
                value: Number(totalPrice).toFixed(2),
                currency: "AED"
            });

            const subscriptionDeduction = total * (subscriptionPlan?.discountPercent / 100);
            summary.push({
                text: "Savings",
                value: Number(subscriptionDeduction).toFixed(2),
                currency: "AED"
            });

            summary.push({
                text: "Total",
                value: Number(totalPrice - subscriptionDeduction).toFixed(2),
                currency: "AED"
            });

            const reponse = {
                count: products.length,
                products: products,
                summary: summary
            };
            helper.deliverResponse(res, 200, reponse, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message
                }
            );
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.mySubscriptions = async (req, res) => {
    try {
        const { customerId } = res.locals.user;
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false
        });
        const subscriptions = await service.find({
            customer: customerDetails?._id,
            isDelete: false,
            storeId: storeid
        });
        let response = [];
        for (let subscription of subscriptions) {
            response.push({
                image: subscription?.product?.thumbnail
                    ? process.env.DOMAIN + subscription?.product?.thumbnail
                    : null,
                title: subscription?.product?.name[language],
                price: subscription?.product?.price?.aed,
                offer_price: subscription?.product?.offerPrice?.aed
                    ? subscription?.product?.offerPrice?.aed
                    : null,
                percentage: subscription?.product?.offerPercentage
                    ? Math.round(subscription?.product?.offerPercentage)
                    : null,
                subscribed_date: subscription?.date,
                label: subscription?.plan?.name,
                _id: subscription?._id,
                unsubscribed: subscription?.unsubscribed
            });
        }
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.unsubscribe = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { customerId } = res.locals.user;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false
        });
        const { id } = req.params;
        const subscription = await service.findOne({ _id: id, customer: customerDetails?._id, storeId: storeid });
        if (subscription) {
            const payload = {
                apiOperation: "CANCEL_SUBSCRIPTION",
                subscription: {
                    identifier: subscription?.subscriptionIdentifier
                }
            };
            try {
                const response = await axios.post(
                    NOONAPI,
                    payload,
                    {
                        headers: { Authorization: process.env.PAYMENT_KEY }
                    }
                );
                if (response?.data?.resultCode === 0) {
                    await service.update({ _id: subscription?._id }, { unsubscribed: true });
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.SUCCESS_RESPONSE.error_code,
                            error_message: "Unsubscribed successfully"
                        }
                    );
                }
            } catch (error) {
                helper.deliverResponse(res, 422, error, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Failed to cancel subscription"
                });
                return;
            }
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Subscription not found"
                }
            );
            return;
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const subscription = await service.adminFind({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, subscription, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};
