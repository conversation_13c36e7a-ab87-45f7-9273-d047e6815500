const messages = require('../config/constants/messages');
const service = require('../services/attribute.service');
const helper = require('../util/responseHelper');
const adminService = require('../services/admin.users.service')
const valueService = require('../services/attribute.value.service')

exports.create = async (req, res) => {
    try {
        let { data } = req.body;
        let { storeId } = req?.headers;
        data = JSON.parse(data)
        const { email } = res?.locals?.user
        const adminDetails = await adminService.findOne({ email, isDelete: false })
        let attributes = []
        if (req?.files) {
            for (let item of req?.files) {
                data.values.push(item?.path)
            }
        }
        const response = await service.create({
            name: data?.name,
            refid: await service.count({}) + 1,
            type: data?.type,
            // products: data?.products,
            isFiltered: data?.isFiltered,
            createdBy: adminDetails?._id,
            storeId: storeId
        });
        if (response) {
            for (let item of data?.values) {
                let value = ''
                switch (data.type) {
                    case 'Text':
                        value = item
                        break
                    case 'Color':
                        value = item
                        break
                    case 'File':
                        value = item
                        break
                }

                const valueResponse = await valueService.create({
                    attribute: response?._id,
                    value: value,
                    refid: await valueService.count({}) + 1,
                    // product: response?.products
                })
                if (valueResponse instanceof Error) {
                    helper.deliverResponse(res, 422, valueResponse, {
                        "error_code": messages.SERVER_ERROR.error_code,
                        "error_message": messages.SERVER_ERROR.error_message
                    });
                } else {
                    attributes.push(valueResponse)
                }
            }
        }
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.ATTRIBUTE.CREATED.error_code,
            error_message: messages.ATTRIBUTE.CREATED.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getAttributes = async (req, res) => {
    try {
        const response = await service.find({ isDelete: false })
        if (response) {
            return helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.attributeDetails = async (req, res) => {
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false })
        if (response) {
            const values = await valueService.find({ attribute: response?._id, isDelete: false })
            const attributes = {
                ...response._doc,
                values
            }
            return helper.deliverResponse(res, 200, attributes, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    try {
        let { data } = req.body;
        data = JSON.parse(data)
        let attributes = []
        const response = await service.update({ refid: req?.params?.refid, isDelete: false }, {
            name: data?.name, type: data?.type,
            isFiltered: data?.isFiltered,
        });
        const attributeValues = await valueService.find({ attribute: response?._id, isDelete: false })
        for (let item of attributeValues) attributes.push({ key: item?.refid, value: item?.value })
        let removeValues = []
        for (let attribute of attributes) {
            { !data.values.includes(attribute.value) ? removeValues.push(attribute.key) : null }
        }
        const addAttributes = data.values.filter(valueToCheck => !attributes.some(attribute => attribute.value === valueToCheck));
        for (let attribute of addAttributes) await valueService.create({
            value: attribute,
            refid: await valueService.count({}) + 1,
            attribute: response?._id,
        })
        for (let attribute of removeValues) {
            const removed = await valueService.delete({ refid: attribute })
        }

        helper.deliverResponse(res, 200, {}, {
            error_code: messages.ATTRIBUTE.UPDATED.error_code,
            error_message: messages.ATTRIBUTE.UPDATED.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getAttributeValues = async (req, res) => {
    try {
        let { body } = req
        const values = await valueService.find({ attribute: body?.attribute, isDelete: false })
        return helper.deliverResponse(res, 200, values, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}