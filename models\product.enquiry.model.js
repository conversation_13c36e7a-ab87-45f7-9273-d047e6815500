const mongoose = require("mongoose");

const productEnquirySchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String},
    countryCode: { type: String, required: true },
    mobile: { type: String, required: true },
    brand: { type : mongoose.Schema.Types.ObjectId, ref: "non.list", required: true },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("product.enquiry", productEnquirySchema)