const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/frame.types.service");
const db = require("../models");
const slug = require('../util/slug')
const productServie = require('../services/products.service')
const sharp = require('sharp');
const { s3, bucketName } = require('../config/s3');
const { uploadToS3 } = require('../util/uploadToS3');
const path = require('path');
const fs = require('fs');
const { uploadWebp } = require('../util/uploadWebp');
const generateUniqueNumber = require('../util/getRefid');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('name', 'Name is required').exists(),
                body('image', 'Image is required').exists(),
            ]
        }
    }
}

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message
            });
        }
        let { body } = req;
        body.slug = await slug.createSlug(db.FrameTypes, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        body.refid = generateUniqueNumber()
        body.storeId = storeid;

        if (!req.file) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: "Image is required"
            });
        }

        const name = `frame-types/${body.slug}/${Date.now()}-${req.file.originalname}.webp`
        const { key, fileURL } = await uploadWebp(req.file.path, name);

        body.image = key;
        const response = await service.create(body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_TYPES.CREATED.error_code,
            error_message: messages.FRAME_TYPES.CREATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.getFrameTypes = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.frameTypeDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const frameTypeDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.name?.en !== frameTypeDetails?.name?.en) body.slug = await slug.createSlug(db.FrameTypes, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid })
        if (body?.image) delete body.image
        if (req?.file){
            const name = `frame-types/${body.slug}/${Date.now()}-${req.file.originalname}.webp`
            const { key, fileURL } = await uploadWebp(req.file.path, name);
            body.image = key
        };
        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_TYPES.UPDATED.error_code,
            error_message: messages.FRAME_TYPES.UPDATED.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.frameTypes = async (req, res) => {
    try {
        let { language, storeid } = req?.headers
        language = language ? language: 'en'
        storeid = storeid ? storeid: 'sa'
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        if (response?.length > 0) {
            const responseData = response.map(item => {
                return {
                    name: item.name[language],
                    image: item.image ? process.env.DOMAIN + item.image : null,
                    refid: item.refid,
                    _id: item._id
                }
            })
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 200, [], {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.activeFrameTypes = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params
        const frameType = await service.findOne({ refid, isDelete: false, storeId: storeid });
        const products = await productServie.find({ frameType: frameType?._id, isDelete: false, storeId: storeid })
        if (products?.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Frame type is used in products"
            });
            return;
        }
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_TYPES.DELETED.error_code,
            error_message: messages.FRAME_TYPES.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}