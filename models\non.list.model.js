const mongoose = require('mongoose');

const nonListSchema = new mongoose.Schema(
  {
    name: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    stores: [
      { type: mongoose.Schema.Types.ObjectId, ref: "stores", required: true },
    ],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
  },
  { timestamps: true }
);

module.exports = mongoose.model('non.list', nonListSchema)