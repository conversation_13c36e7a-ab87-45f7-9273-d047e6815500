const messages = require('../config/constants/messages')
const helper = require('../util/responseHelper')
const service = require('../services/coupons.service')
const adminService = require('../services/admin.users.service')
const customerService = require('../services/customer.service')
const cartService = require('../services/cart.service')

exports.create = async (req, res) => {
    try {
        let { body } = req;
        const { email } = res?.locals?.user
        const adminDetail = await adminService.findOne({ email: email, isDelete: false })
        body.createdBy = adminDetail?._id
        body.refid = await service.count({}) + 1
        const response = await service.create(body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.COUPONS.CREATED.error_code,
                error_message: messages.COUPONS.CREATED.error_message
            })
        } else {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.list = async (req, res) => {
    try {
        const response = await service.find({ isDelete: false });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.NO_DATA.error_code,
                error_message: messages.NO_DATA.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.detail = async (req, res) => {
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.NO_DATA.error_code,
                error_message: messages.NO_DATA.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    try {
        let { body } = req
        const response = await service.update({ refid: req?.params?.refid }, body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.NO_DATA.error_code,
                error_message: messages.NO_DATA.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}
