const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Texts(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Texts.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}


exports.find = async (query, projection = {}, limit) => {
    try {
        let response = await db.Texts.find(query, projection).sort({ createdAt: -1 }).limit(limit)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Texts.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: true,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.upsert = async (query, data) => {
    try {
        let response = await db.Texts.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: true,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Texts.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}