const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Carts(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.Carts.find(query, projection).sort({ updatedAt: -1 })
            .populate('customer', 'name refid mobile email')
            .populate({ path: 'products.product', select: 'name thumbnail color sku', populate: { path: 'color', select: 'name color' } })
            .populate('products.lensDetails.brand', 'name')
            .populate('products.lensDetails.index', 'name')
            .populate('products.lensDetails.coating', 'name')
            .populate('products.contactLens.sphLeft', 'name')
            .populate('products.contactLens.cylLeft', 'name')
            .populate('products.contactLens.axisLeft', 'name')
            .populate('products.contactLens.sphRight', 'name')
            .populate('products.contactLens.cylRight', 'name')
            .populate('products.contactLens.axisRight', 'name')
        return response;
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Carts.findOne(query, projection)
            .populate('products.product', 'name thumbnail sku')
            .populate('products.lensDetails.brand', 'name')
            .populate('products.lensDetails.index', 'name')
            .populate('products.lensDetails.coating', 'name')
            .populate('products.contactLens.sphLeft', 'name')
            .populate('products.contactLens.cylLeft', 'name')
            .populate('products.contactLens.axisLeft', 'name')
            .populate('products.contactLens.sphRight', 'name')
            .populate('products.contactLens.cylRight', 'name')
            .populate('products.contactLens.axisRight', 'name')
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Carts.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).populate('products.product', 'sku').exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Carts.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.addProduct = async (cartid, data) => {
    try {
        let cart = await db.Carts.updateOne({ refid: cartid }, { $push: { products: data } })
        return cart
    } catch (error) {
        throw error
    }
}