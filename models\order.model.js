const mongoose = require('mongoose')

const orderSchema = new mongoose.Schema({
    customer: { type: mongoose.Schema.Types.ObjectId, ref: 'customers' },
    devicetoken: { type: String },
    invoice: { type: String },
    cart: { type: mongoose.Schema.Types.ObjectId, ref: 'carts' },
    address: { type: mongoose.Schema.Types.ObjectId, ref: 'address', required: true },
    addressDetails: {
        name: { type: String },
        refid: { type: String },
        country: { type: String },
        emirates: { type: String },
        street: { type: String },
        suiteUnit: { type: String },
        email: { type: String },
        city: { type: String, },
        postalCode: { type: String },
        countryCode: { type: String, required: true },
        mobile: { type: String, required: true },
        type: { type: String, enum: ['Home', 'Work', 'Other'], required: true, default: "Home" },
    },
    expectedDate: { type: Date },
    returnDate: { type: Date },
    deliveryCharge: { type: Number, default: 0 },
    paymentMethod: { type: String, enum: ['COD', 'ONLINE'] },
    gateway: { type: String, enum: ['TAMARA', 'NOON', 'TABBY'] },
    tamaraOrderId: { type: String },
    tabbyOrderId: { type: String },
    tabbySessionId: { type: String },
    orderNo: { type: String, required: true },
    invoiceId: { type: String, required: true },
    orderDate: { type: Date, default: new Date().toISOString() },
    orderTime: { type: String, default: new Date().getHours() + ":" + new Date().getMinutes() + ":" + new Date().getSeconds() },
    orderStatus: {
        type: String, default: 'PENDING',
        enum: ['PENDING', 'PLACED', 'CONFIRMED', 'READY TO SHIP', 'SHIPPED VIA ECO', 'SHIPPED VIA INHOUSE', 'OUT FOR DELIVERY', 'DELIVERED', 'CANCELLED', 'FAILED',
            'RETURNED', 'REFUNDED']
    },
    paymentStatus: { type: String, default: 'PENDING', enum: ['PENDING', 'PAID'] },
    products: [{
        product: { type: mongoose.Schema.Types.ObjectId, ref: 'products' },
        quantity: { type: Number },
        couponCode: { type: String },
        couponAmount: { type: Number },
        price: { type: Number },
        OGPrice: { type: Number },
        total: { type: Number },
        tax: { type: Number },
        currency: { type: String },
        type: { type: String },
        size: { type: mongoose.Schema.Types.ObjectId, ref: 'sizes' },
        contactSize: { type: mongoose.Schema.Types.ObjectId, ref: 'contact.sizes' },
        notInterested: { type: Boolean },
        isReviewed: { type: Boolean, default: false },
        lensDetails: {
            vision: { type: String },
            prescription: { type: mongoose.Schema.Types.Mixed },
            lensType: { type: String },
            brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand" },
            photocromic: { type: String },
            index: { type: mongoose.Schema.Types.ObjectId, ref: "lens.index" },
            coating: { type: mongoose.Schema.Types.ObjectId, ref: "coating" },
            lensEnquiry: { type: mongoose.Schema.Types.ObjectId, ref: "lensEnquiries" },
        },
        contactLens: {
            multiple: { type: Boolean },
            sphLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            sphRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            cylLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            cylRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            axisLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            axisRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            multiFocal: { type: String },
        },
    }],
    history: [{
        status: { type: String, default: 'PENDING' },
        date: { type: Date, default: new Date().toISOString() },
    }],
    baseTotal: { type: Number },
    savings: { type: Number },
    loyaltyDiscount: { type: Number },
    couponCode: { type: String },
    shippingCharge: { type: Number, default: 0 },
    shippingMethod: { type: String },
    store: { type: mongoose.Schema.Types.ObjectId, ref: 'stores' },
    awb: { type: String },
    trackingId: { type: String },
    paymentCharge: { type: Number, default: 0 },
    tryCartDeduction: { type: Number, default: 0 },
    vat: { type: Number, default: 0 },
    vatAmount: { type: Number, default: 0 },
    total: { type: Number },
    subscription: { type: mongoose.Schema.Types.ObjectId, ref: 'subscriptions' },
    nextOrderDate: { type: Date },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    isTryCart: { type: Boolean },
    storeId: { type: String, required: true, default: 'ae' },
    isGiftWrapping: { type: Boolean, default: false },
    giftWrappingFee: { type: Number, default: 0 },
    giftWrapMessage: { type: String }
}, { timestamps: true })



module.exports = mongoose.model('Order', orderSchema)
