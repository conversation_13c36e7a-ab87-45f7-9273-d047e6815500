const express = require("express");
const router = express.Router();
const controller = require("../../controllers/front.material.controller");

module.exports = () => {
    router.post("/front-materials/", controller.create);
    router.get("/front-materials/", controller.find);
    router.get("/front-materials/:refid", controller.findOne);
    router.put("/front-materials/:refid", controller.update);
    router.put("/delete-front-material/:refid", controller.delete);
    return router;
};
