const express = require('express');
const router = express.Router();
const controller = require('../../controllers/lens.type.controller');

module.exports = () => {
    router.post('/create-lens-type', controller.create);
    router.get('/get-lens-types', controller.list);
    router.get('/lens-type-details/:refid', controller.detail);
    router.put('/update-lens-type/:refid', controller.update);
    router.put('/delete-lens-type/:refid', controller.delete);

    return router;
}