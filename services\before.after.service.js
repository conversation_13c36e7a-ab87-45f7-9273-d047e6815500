const db = require('../models/index')

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.BeforeAfter.find(query, projection).sort({ createdAt: -1 })
        return response
    } catch (error) {
        throw error;
    }
}

exports.create = async (data) => {
    try {
        let response = new db.BeforeAfter(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.BeforeAfter.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.BeforeAfter.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}