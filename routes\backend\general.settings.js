const express = require('express');
const router = express.Router();
const controller = require('../../controllers/general.settings.controller');
const authorize = require('../../middlewares/authorize');
const upload = require('../../util/upload');
const mediaController = require('../../controllers/image.controller');

module.exports = () => {
    router.post('/create-settings', upload.fields([{ name: 'logo', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), authorize.verifyToken, controller.create);
    router.get('/general-settings', controller.list);
    router.post('/update-settings', upload.fields([{ name: 'logo', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), authorize.verifyToken, controller.update);

    router.delete('/media-delete', mediaController.deleteFile);
    router.post("/multiple-upload", upload.array("files"), mediaController.multipleUpload);

    return router;
}