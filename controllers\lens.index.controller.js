const messages = require('../config/constants/messages');
const helper = require('../util/responseHelper');
const service = require('../services/lens.index.service');
const enquiryService = require('../services/lens.enquiries.service');
const generateUniqueNumber = require('../util/getRefid');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_INDEX.CREATED.error_code,
                error_message: messages.LENS_INDEX.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.detail = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_INDEX.UPDATED.error_code,
                error_message: messages.LENS_INDEX.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.lensIndex = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const { brand } = req.params
        const index = await service.find({ isDelete: false, isActive: true, brand: brand, storeId: storeid })
        const response = { index: index?.map((item=>({...item?._doc, name: item?.name[language]})))}
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const {refid} = req.params
        const detail = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        const enquiries = await enquiryService.find({ index: detail._id, isDelete: false, storeId: storeid });
        if (enquiries.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Cannot delete lens index as it is used in Produts"
            })
            return;
        }
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.LENS_INDEX.DELETED.error_code,
            error_message: messages.LENS_INDEX.DELETED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}