const mongoose = require('mongoose');

const insuranceProviderSchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: {  type: String }
    },
    logo: { type: String, required: true },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('insurance.provider', insuranceProviderSchema);