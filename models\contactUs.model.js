const mongoose = require("mongoose");

const contactUsSchema = new mongoose.Schema({
    fullName: { type: String, required: true },
    email: { type: String, required: true },
    countryCode: { type: String, required: true },
    mobile: { type: String, required: true },
    store: { type: mongoose.Schema.Types.ObjectId, ref: "stores", required: true },
    message: { type: String, required: true },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'ae' }
}, { timestamps: true })

module.exports = mongoose.model("contactUs", contactUsSchema)