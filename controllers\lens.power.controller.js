const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/lens.power.service");
const enquiryService = require("../services/lens.enquiries.service");
const generateUniqueNumber = require("../util/getRefid");

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber();
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message
                }
            );
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_POWER.CREATED.error_code,
                error_message: messages.LENS_POWER.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { type } = req?.params;
        const response = await service.find({ isDelete: false, type: type, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.brandPowers = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const { brand } = req?.params;
        const powers = await service.find({ isDelete: false, brand: brand, storeId: storeid });
        const sph = powers.filter((power) => power.type === "Sph");
        const cyl = powers.filter((power) => power.type === "Cyl");
        const axis = powers.filter((power) => power.type === "Axis");
        const add = powers.filter((power) => power.type === "Add");
        const response = { sph, cyl, axis, add };
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.detail = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, body);
        if (response instanceof Error) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message
                }
            );
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.LENS_POWER.UPDATED.error_code,
                error_message: messages.LENS_POWER.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params;
        const detail = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });

        const lensEnquiries = await enquiryService.find({
            $or: [
                { "prescription.leftSph": detail?._id },
                { "prescription.rightSph": detail?._id },
                { "prescription.leftCyl": detail?._id },
                { "prescription.rightCyl": detail?._id },
                { "prescription.leftAxis": detail?._id },
                { "prescription.rightAxis": detail?._id }
            ]
        });

        if (lensEnquiries.length > 0) {
            helper.deliverResponse(res,422, {}, {
                error_code: 1,
                error_message: "Cannot delete lens power as it is used in Produts"
            })
            return;
        }

        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });

        helper.deliverResponse(res, 200, {}, {
            error_code: messages.LENS_POWER.DELETED.error_code,
            error_message: messages.LENS_POWER.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            }
        );
    }
};
