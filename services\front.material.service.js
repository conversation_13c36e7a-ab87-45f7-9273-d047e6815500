const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.FrontMaterial(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}, sort = { position: 1}) => {
    try {
        let response = await db.FrontMaterial.find(query, projection).sort(sort)
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.FrontMaterial.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.FrontMaterial.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.FrontMaterial.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}