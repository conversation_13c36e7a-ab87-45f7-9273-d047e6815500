const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/brands.service");
const db = require("../models");
const slug = require('../util/slug')
const axios = require('axios');
const productService = require('../services/products.service');
const { uploadWebp } = require('../util/uploadWebp');
const generateUniqueNumber = require('../util/getRefid');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('name', 'Name is required').exists(),
            ]
        }
    }
}

async function uploadImage(req, body, brandId) {
    if (req?.files?.image) {
        const name = `brands/${brandId}/image/${Date.now()}-${req?.files["image"][0]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["image"][0]?.path, name)
        body.image = key
    }
}

async function uploadBanner(req, body, brandId) {
    if (req?.files?.banner) {
        const name = `brands/${brandId}/banner/${Date.now()}-${req?.files["banner"]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["banner"][0]?.path, name)

        if (body.banner) body.banner.en = key
        else body.banner = { en: key };
    }
}
async function uploadBannerAr(req, body, brandId) {
    if (req?.files?.bannerAr) {
        const name = `brands/${brandId}/bannerAr/${Date.now()}-${req?.files["bannerAr"]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["bannerAr"][0]?.path, name)

        if (body.banner) body.banner.ar = key
        else body.banner = { ar: key };
    }
}
async function uploadPoster(req, body, brandId) {
    if (req?.files?.poster) {
        const name = `brands/${brandId}/poster/${Date.now()}-${req?.files["poster"]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["poster"][0]?.path, name)

        if (body.poster) body.poster.en = key
        else body.poster = { en: key };

    }
}
async function uploadPosterAr(req, body, brandId) {
    if (req?.files?.posterAr) {
        const name = `brands/${brandId}/posterAr/${Date.now()}-${req?.files["posterAr"]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["posterAr"][0]?.path, name)

        if (body.poster) body.poster.ar = key
        else body.poster = { ar: key };
    }
}

async function uploadSeoImage(req, body, brandId) {
    if (req?.files?.ogImage) {
        const name = `brands/${brandId}/seoImage/${Date.now()}-${req?.files["ogImage"]?.originalname}.webp`
        const { key } = await uploadWebp(req?.files["ogImage"][0]?.path, name)

        if (body.seoDetails) body.seoDetails.ogImage = key
    }
}

exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        body.slug = await slug.createSlug(db.Brands, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        body.refid = generateUniqueNumber()
        body.brandId = `BRAND-${body.refid}`;
        body.storeId = storeid

        await Promise.all([
            uploadImage(req, body, body.brandId),
            uploadBanner(req, body, body.brandId),
            uploadBannerAr(req, body, body.brandId),
            uploadPoster(req, body, body.brandId),
            uploadPosterAr(req, body, body.brandId),
            uploadSeoImage(req, body, body.brandId),
        ])

        const response = await service.create(body);
        if (body?.isMain == false) {
            const main = await service.findOne({ _id: body?.parent, isDelete: false, storeId: storeid });
            if (main) {
                const subs = main?.subBrands?.map(item => item?._id?.toString())
                subs.push(response?._id?.toString())
                const uniq = [...new Set(subs)]
                const res = await service.update({ _id: body?.parent, isDelete: false, storeId: storeid }, { subBrands: uniq });
                console.log("res",res)
            }
        }
        if (response) await axios.post(process.env.REVALIDATE, { tag: "brands" });
        return helper.deliverResponse(res, 200, response, messages.BRANDS.CREATED);
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.getBrands = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let query = { isDelete: false, storeId: storeid };
        if(!req.query?.isMain){
            query.isMain = { $ne: false }
        };
        if(req.query?.isMain == "false"){
            query.isMain = { $ne: true }
        };
        if(req.query?.parent){
            query.parent = req.query?.parent
        };
        const response = await service.find(query);
        if (response) {
            return helper.deliverResponse(res, 200, response, {
                "error_code": messages.SUCCESS_RESPONSE.error_code,
                "error_message": messages.SUCCESS_RESPONSE.error_message
            });
        } else {
            return helper.deliverResponse(res, 422, {}, {
                "error_code": messages.NO_DATA.error_code,
                "error_message": messages.NO_DATA.error_message
            });
        }
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.brandDetails = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if (!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const brandDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.name?.en !== brandDetails?.name?.en) body.slug = await slug.createSlug(db.Brands, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid })

        if (body?.image) delete body.image
        if (body?.banner) delete body.banner
        if (body?.bannerAr) delete body.bannerAr
        if (body?.poster) delete body.poster
        if (body?.posterAr) delete body.posterAr
        if (body?.ogImage) delete body.ogImage

        await Promise.all([
            uploadImage(req, body, body.brandId),
            uploadBanner(req, body, body.brandId),
            uploadBannerAr(req, body, body.brandId),
            uploadPoster(req, body, body.brandId),
            uploadPosterAr(req, body, body.brandId),
            uploadSeoImage(req, body, body.brandId),
        ])

        if (!body.store) {
            body.store = null
        }

        const pages = [];
        body?.page?.forEach(page => {
            let {id, ...item} = JSON.parse(page);
            pages.push(item);
        })

        body.page = pages;

        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        if (response) await axios.post(process.env.REVALIDATE, { tag: "brands" });
        return helper.deliverResponse(res, 200, response, messages.BRANDS.UPDATED);
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.brands = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        const { type } = req.params;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        let query = { isDelete: false, isActive: true, storeId: storeid };
        if (type == "home") query.inHome = true
        const response = await service.find(query);
        if (response instanceof Error) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        })
        else {
            const data = response.map(item => {
                return {
                    ...item?._doc,
                    name: item.name[language],
                    overview: item.overview[language],
                    banner: item?.banner
                        ? item?.banner[language]
                            ? process.env.DOMAIN + item.banner[language]
                            : item?.banner.en
                                ? process.env.DOMAIN + item.banner.en
                                : null
                        : null,
                    image: item?.image ? process.env.DOMAIN + item.image : null,
                    poster: item?.poster
                        ? item?.poster[language]
                            ? process.env.DOMAIN + item.poster[language]
                            : item?.poster.en
                                ? process.env.DOMAIN + item.poster.en
                                : null
                        : null,
                }
            })
            helper.deliverResponse(res, 200, data, {
                "error_code": messages.SUCCESS_RESPONSE.error_code,
                "error_message": messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.updateInHome = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req
        const { refid } = req.params
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, body)
        await axios.post(process.env.REVALIDATE, { tag: "home" })
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        })
    }
}

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params
        const brand = await service.findOne({ refid: refid, isDelete: false, storeId: storeid })
        const products = await productService.find({ brand: brand?._id, isDelete: false, storeId: storeid })
        if (products?.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Cannot delete brand as it is in used in Products'
            });
            return;
        }
        const resp = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true })
        if(resp?.isMain == false){
            const main = await service.findOne({ _id: resp?.parent, isDelete: false, storeId: storeid });
            if (main) {
                const subs = main?.subBrands?.map(item => item?._id?.toString())
                const uniq = [...new Set(subs?.filter(item => item !== resp?._id?.toString()))]
                console.log("uniq",uniq)
                await service.update({ _id: resp?.parent, isDelete: false, storeId: storeid }, { subBrands: uniq });
            }
        }
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.BRANDS.DELETED.error_code,
            error_message: messages.BRANDS.DELETED.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        })
        return;
    }
}