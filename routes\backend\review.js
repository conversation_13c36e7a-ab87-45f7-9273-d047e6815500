const express = require('express');
const router = express.Router();
const controller = require('../../controllers/reviews.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/create-review', authorize.verifyToken, controller.create);
    router.get('/get-reviews', controller.getReviews);
    router.get('/review-details/:refid', controller.reviewDetails);
    router.put('/update-review/:refid', controller.update);
    router.put('/delete-review/:refid', controller.delete);

    return router;
}
