const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/non.list.service");
const enquiryService = require("../services/product.enquiry.service");

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = (await service.count({})) + 1;
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, response, messages.NON_LIST.CREATED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.findOne = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
        helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        let { refid } = req.params;
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, body);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, response, messages.NON_LIST.UPDATED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.getAvailableStores = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { name } = req.params;
        const result = await service.findOne({ "name.en": { $regex: new RegExp(`^${name}$`, "i") }, isDelete: false, storeId: storeid });
        const stores = [];
        if (!result) {
            return res.status(404).json({ stores: [] });
        }
        for (const store of result?.stores) {
            stores.push({
                name: store?.name[language],
                address: store?.address[language],
                coordinates: store?.coordinates,
                _id: store?._id,
                refid: store?.refid,
                slug: store?.slug,
                email: store?.email,
                countryCode: store?.countryCode,
                mobile: store?.mobile,
            });
        }
        helper.deliverResponse(res, 200, stores, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.productEnquiry = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const nonList = await service.findOne({
            storeId: storeid,
            $or: [
                { "name.en": { $regex: "^" + body?.brand + "$", $options: "i" } },
                { "name.ar": { $regex: "^" + body?.brand + "$", $options: "i" } },
            ], isDelete: false
        });
        body.brand = nonList._id;
        body.refid = (await enquiryService.count({})) + 1;
        body.storeId = storeid;
        const response = await enquiryService.create(body);
        helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.getEnquiries = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await enquiryService.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, result, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { refid } = req.params;
        const brand = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        const enquiries = await enquiryService.find({ brand: brand?._id, isDelete: false, storeId: storeid });
        if (enquiries.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: 'Cannot delete as it has enquiries'
            });
            return;
        }
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, messages.NON_LIST.DELETED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}