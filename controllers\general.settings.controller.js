const messages = require('../config/constants/messages');
const helper = require('../util/responseHelper');
const adminService = require('../services/admin.users.service');
const service = require('../services/general.settings.service');
const axios = require('axios'); 
const { uploadWebp } = require('../util/uploadWebp');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { data } = req?.body;
        data = JSON.parse(data)
        const { email } = res?.locals?.user
        const admin = await adminService.findOne({ email: email, isDelete: false })

        data.createdBy = admin?._id
        data.refid = await service.count({}) + 1
        data.storeId = storeid;

        if (req?.files?.logo){
            const name = `general/logo/${Date.now()}-${req?.files?.logo?.originalname}.webp`
            const {key} = await uploadWebp(req?.files?.logo?.path, name)
            data.logo = key
        }

        if (req?.files?.ogImage){
            const name = `general/ogImage/${Date.now()}-${req?.files?.ogImage?.originalname}.webp`
            const {key} = await uploadWebp(req?.files?.ogImage?.path, name)
            data.ogImage = key
        }

        const result = await service.create(data);
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        await axios.post(process.env.REVALIDATE, { tag: "header" })
        helper.deliverResponse(res, 200, result, {
            error_code: messages.GENERAL_SETTINGS.CREATED.error_code,
            error_message: messages.GENERAL_SETTINGS.CREATED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}


exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.find({ isDelete: false, storeId: storeid });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { data } = req?.body;
        data = JSON.parse(data)
        
        if (req?.files?.logo){
            const name = `general/logo/${Date.now()}-${req?.files?.logo?.originalname}.webp`
            const {key} = await uploadWebp(req?.files?.logo?.path, name)
            data.logo = key
        }

        if (req?.files?.ogImage){
            const name = `general/ogImage/${Date.now()}-${req?.files?.ogImage?.originalname}.webp`
            const {key} = await uploadWebp(req?.files?.ogImage?.path, name)
            data.ogImage = key
        }

        const result = await service.update({ storeId: storeid }, data)
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        await axios.post(process.env.REVALIDATE, { tag: "header" })
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}