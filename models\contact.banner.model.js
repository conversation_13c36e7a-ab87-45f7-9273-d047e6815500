const mongoose = require('mongoose');

const contactBannerSchema = new mongoose.Schema({
    image: { type: String, required: true },
    pageTitle: {
        en: { type: String, required: true },
        ar:  { type: String },
    },
    formTitle: {
        en: { type: String, required: true },
        ar: {  type: String }
    },
    title: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    description: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    buttonText: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    link: { type: String, required: true },
    officeHoursTitle: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    officeHours: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    storeOneTitle : {
        en: { type: String, required: true },
        ar: { type: String }
    },
    storeOneAddress: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    storeTwoTitle: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    storeTwoAddress: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String },
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('contactBanner', contactBannerSchema)