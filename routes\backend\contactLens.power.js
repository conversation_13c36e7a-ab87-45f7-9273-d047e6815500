const express = require('express');
const router = express.Router();
const controller = require('../../controllers/contactLens.power.controller')

module.exports = () => {
    router.post('/contactLens-power', controller.create);
    router.get('/contactLens-power/:type', controller.list);
    router.get('/contactLens-powers/:refid', controller.details);
    router.put('/contactLens-power/:refid', controller.update);
    router.put('/delete-contactLens-power/:refid', controller.delete);

    return router;
}