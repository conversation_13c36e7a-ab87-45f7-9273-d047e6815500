const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require("../services/lens.brand.service");
const lensPowerService = require("../services/lens.power.service");
const lensIndexService = require("../services/lens.index.service");
const coatingService = require("../services/coating.service");
const lensTypeService = require("../services/lens.type.service");
const generateUniqueNumber = require('../util/getRefid');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.LENS_BRAND.CREATED.error_code,
            error_message: messages.LENS_BRAND.CREATED.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        let query = { isDelete: false, storeId: storeid };
        if (body?.isActive) query.isActive = body?.isActive
        const response = await service.find(query);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.details = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.LENS_BRAND.UPDATED.error_code,
            "error_message": messages.LENS_BRAND.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.lensBrands = async (req, res) => {
    try {
        let { language, storeid } = req?.headers
        language = language ? language : 'en'
        storeid = storeid ? storeid : 'sa'
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        if (response.length > 0) {
            const data = response.map((item=>({
                ...item._doc,
                name: item.name[language],
                cover: item.cover? process.env.DOMAIN + item.cover: null,
                logo: item.logo? process.env.DOMAIN + item.logo: null
            })))
            // for (let item of response) {
            //     item.name = item.name[language];
            //     console.log(item.name[language])
            //     if (item?.logo) item.logo = process.env.DOMAIN + item.logo
            //     if (item?.cover) item.cover = process.env.DOMAIN + item.cover
            // }
            helper.deliverResponse(res, 200, data, {
                "error_code": messages.SUCCESS_RESPONSE.error_code,
                "error_message": messages.SUCCESS_RESPONSE.error_message
            });
        } else {
            helper.deliverResponse(res, 200, [], {
                "error_code": messages.NO_DATA.error_code,
                "error_message": messages.NO_DATA.error_message,
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;

        const details = await service.aggregate([
            { $match: { refid, isDelete: false, storeId: storeid } },
            {
                $lookup: {
                    from: "lens.power",
                    localField: "_id",
                    foreignField: "brand",
                    as: "powers"
                }
            },
            {
                $lookup: {
                    from: "lens.index",
                    localField: "_id",
                    foreignField: "brand",
                    as: "indexes"
                }
            },
            {
                $lookup: {
                    from: "coating",
                    localField: "_id",
                    foreignField: "brand",
                    as: "coatings"
                }
            },
            {
                $lookup: {
                    from: "lens.type",
                    localField: "_id",
                    foreignField: "brand",
                    as: "types"
                }
            }
        ]);

        if (!details.length || details[0].powers.length > 0 || details[0].indexes.length > 0 || details[0].coatings.length > 0 || details[0].types.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": 1,
                "error_message": "Lens brand is used in products",
            });
            return;
        }

        const response = await service.update({ refid, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.LENS_BRAND.DELETED.error_code,
            "error_message": messages.LENS_BRAND.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}