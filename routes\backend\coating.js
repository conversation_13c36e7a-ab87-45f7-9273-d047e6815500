const express = require('express');
const router = express.Router();
const controller = require('../../controllers/coating.controller');

module.exports = () => {
    router.post('/create-coating', controller.create);
    router.get('/get-coatings', controller.list);
    router.get('/coating-details/:refid', controller.detail);
    router.put('/update-coating/:refid', controller.update);
    router.put('/delete-coating/:refid', controller.delete);

    return router;
}