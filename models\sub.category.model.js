const mongoose = require('mongoose');

const subCategorySchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    parentRefId: { type: String },
    childs: [{ type: mongoose.Schema.Types.ObjectId, ref: 'sub.categories' }],
    depth: { type: Number, default: 0 },
    isChild: { type: Boolean, default: false },
    root: { type: mongoose.Schema.Types.ObjectId, ref: 'sub.categories' },
    slug: { type: String, required: true },
    refid: { type: String, required: true, unique: true },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

function autoPopulateSubs(next) {
    this.populate('childs');
    this.populate('name');
    next();
}

subCategorySchema
    .pre('findOne', autoPopulateSubs)
    .pre('find', autoPopulateSubs)

module.exports = mongoose.model('sub.categories', subCategorySchema);