const express = require('express')
const router = express.Router();
const controller = require('../../controllers/coupons.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/create-coupon', authorize.verifyToken, controller.create);
    router.get('/get-coupons', controller.list);
    router.get('/coupon-details/:refid', controller.detail);
    router.put('/update-coupon/:refid', authorize.verifyToken, controller.update);

    return router;
}