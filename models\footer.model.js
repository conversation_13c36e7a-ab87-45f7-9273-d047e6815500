const mongoose = require("mongoose");

const footerSchema = new mongoose.Schema(
    {
        firstCol: {
            title: {
                en: { type: String, required: true },
                ar: { type: String}
            },
            links: [
                {
                    title: {
                        en: { type: String, required: true },
                        ar: { type: String }
                    },
                    label: { type: String },
                    link: { type: String, required: true }
                }
            ]
        },
        secondCol: {
            title: {
                en: { type: String, required: true },
                ar: { type: String }
            },
            links: [
                {
                    title: {
                        en: { type: String, required: true },
                        ar: { type: String }
                    },
                    label: { type: String },
                    link: { type: String, required: true }
                }
            ]
        },
        thirdCol: {
            title: {
                en: { type: String, required: true },
                ar: { type: String }
            },
            links: [
                {
                    title: {
                        en: { type: String, required: true },
                        ar: { type: String }
                    },
                    label: { type: String },
                    link: { type: String, required: true }
                }
            ]
        },
        fourthCol: {
            title: {
                en: { type: String, required: true },
                ar: { type: String }
            },
            links: [
                {
                    title: {
                        en: { type: String, required: true },
                        ar: { type: String }
                    },
                    label: { type: String },
                    link: { type: String, required: true }
                }
            ]
        },
        refid: { type: String, required: true },
        isActive: { type: Boolean, default: true },
        isDelete: { type: Boolean, default: false },
        storeId: { type: String, required: true, default: 'sa' }
    },
    { timestamps: true }
);

module.exports = mongoose.model("footer", footerSchema);
