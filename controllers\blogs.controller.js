const messages = require('../config/constants/messages');
const service = require('../services/blogs.service');
const helper = require('../util/responseHelper');
const db = require('../models/index');
const adminService = require('../services/admin.users.service');
const slug = require('../util/slug');
const { validationResult, body } = require('express-validator');
const { uploadWebp } = require('../util/uploadWebp');

exports.validate = (method) => {
    switch (method) {
        case 'blogs': {
            return [
                body('page').exists().withMessage("Page is required"),
                body('limit').exists().withMessage("Limit is required"),
            ]
        }
    }
}

exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const { email } = res?.locals?.user
        const adminDetails = await adminService.findOne({ email, isDelete: false })
        body.slug = await slug.createSlug(db.Blogs, body?.title?.en, { slug: await slug.generateSlug(body?.title?.en), storeId: storeid });
        body.refid = await service.count({}) + 1
        body.createdBy = adminDetails?._id
        body.storeId = storeid
        console.log(body)
        if (req?.file) {
            const name = `blogs/${body.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.image = key;
        }
        const response = await service.create(body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.BLOGS.CREATED.error_code,
                error_message: messages.BLOGS.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.getBlogs = async (req, res) => {
    try {
        let query = { isDelete: false };
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ ...query, storeId: storeid });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.getBlogDetails = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }else{
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.BLOGS.NOT_FOUND.error_code,
                error_message: messages.BLOGS.NOT_FOUND.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;

        if (body?.image) delete body.image

        const blogDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.title?.en !== blogDetails?.title?.en) body.slug = await slug.createSlug(db.Blogs, body?.title?.en, { slug: await slug.generateSlug(body?.title?.en), storeId: storeid })

        if (req?.file) {
            const name = `blogs/${body.slug ?? blogDetails?.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.image = key;
        }
        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.BLOGS.UPDATED.error_code,
                error_message: messages.BLOGS.UPDATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.blogsList = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                "error_code": messages.VALIDATION_ERROR.error_code,
                "error_message": messages.VALIDATION_ERROR.error_message
            })
            return
        }
        const { language } = req?.headers
        let { body } = req
        let query = { isDelete: false, isActive: true, storeId: storeid }

        const response = await service.pagination(query, body?.page, body?.limit);
        const result = []
        if (response) {
            let count = await service.count(query);
            let pages = Math.ceil(count / body?.limit);
            let isLastPage = body?.page * body?.limit >= count;
            let nextPage = isLastPage ? null : body?.page + 1;
            for (let item of response) {
                result.push({
                    refid: item?.refid,
                    _id: item?._id,
                    title: item?.title[language] ? item?.title[language] : item?.title['en'],
                    author: item?.author[language] ? item?.author[language] : item?.author['en'],
                    slug: item?.slug,
                    image: process.env.DOMAIN + item?.image,
                    summary: item?.summary[language] ? item?.summary[language] : item?.summary['en'],
                    date: item?.createdAt,
                })
            }
            const data = {
                blogs: result,
                count: count,
                totalPages: pages,
                page: body?.page,
                limit: body?.limit,
                nextPage: nextPage,
                isLastPage: isLastPage
            }
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.blogDetail = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";

        let { body } = req
        let { language } = req?.headers
        language = language ? language : 'en'
        const response = await service.findOne({ slug: body?.slug, isDelete: false, storeId: storeid });
        if (response) {
            let data;
            data = {
                ...response?._doc,
                title: response?.title[language] ? response?.title[language] : response?.title['en'],
                summary: response?.summary[language] ? response?.summary[language] : response?.summary['en'],
                content: response?.content[language],
                image: process.env.DOMAIN + response?.image,
                author: response?.author[language] ? response?.author[language] : response?.author['en'],
            }
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            });
        } else {
            helper.deliverResponse(res, 200, {}, {
                "error_code": messages.BLOGS.NOT_FOUND.error_code,
                "error_message": messages.BLOGS.NOT_FOUND.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, { isDelete: true });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.BLOGS.DELETED.error_code,
                error_message: messages.BLOGS.DELETED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}
