const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.PaymentMethodFee(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.PaymentMethodFee.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}, limit) => {
    try {
        let response = await db.PaymentMethodFee.find(query, projection).sort({ createdAt: -1 }).limit(limit)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.PaymentMethodFee.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: true,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}