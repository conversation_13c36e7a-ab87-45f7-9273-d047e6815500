const express = require('express');
const router = express.Router();
const controller = require('../../controllers/size.controller');

module.exports = () => {
    router.post('/create-size', controller.create);
    router.post('/get-sizes', controller.list);
    router.get('/size-details/:refid', controller.detail);
    router.put('/update-size/:refid', controller.update);
    router.put('/delete-size/:refid', controller.delete);

    return router;
}