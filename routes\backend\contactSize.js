const express = require('express');
const router = express.Router();
const controller = require('../../controllers/contact.size.controller');

module.exports = () => {
    router.post('/create-contact-size', controller.create);
    router.post('/get-contact-sizes', controller.list);
    router.get('/contact-size-details/:refid', controller.detail);
    router.put('/update-contact-size/:refid', controller.update);
    router.put('/delete-contact-size/:refid', controller.delete);

    return router;
}