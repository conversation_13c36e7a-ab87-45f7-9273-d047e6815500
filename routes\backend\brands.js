const express = require('express');
const router = express.Router();
const controller = require('../../controllers/brands.controller')
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-brand', upload.fields([{ name: 'image', maxCount: 1 }, { name: 'banner', maxCount: 1 }, { name: 'bannerAr', maxCount: 1 }, { name: 'poster', maxCount: 1 }, { name: 'posterAr', maxCount: 1 }, { name: 'ogImage', maxCount: 1 }]), controller.create);
    router.get('/get-brands', controller.getBrands);
    router.get('/brand-details/:refid', controller.brandDetails);
    router.post('/update-brand', 
        upload.fields([
            { name: 'image', maxCount: 1 },
            { name: 'banner', maxCount: 1 },
            { name: 'bannerAr', maxCount: 1 },
            { name: 'poster', maxCount: 1 },
            { name: 'posterAr', maxCount: 1 },
            { name: 'ogImage', maxCount: 1 }
        ]),
        controller.update);
    router.put('/delete-brand/:refid', controller.delete);
    router.put('/brand-inHome/:refid', controller.updateInHome);

    return router;
}
