const express = require('express')
const router = express.Router()
const controller = require('../../controllers/products.controller')
const authorize = require('../../middlewares/authorize')
const coatingController = require('../../controllers/coating.controller')
const reviewController = require('../../controllers/reviews.controller')
const nonListController = require('../../controllers/non.list.controller')

module.exports = () => {
    router.post('/products', controller.validate('listing'), controller.productsList)
    router.post('/next-filter-count', controller.validate('listing'), controller.countNextFilter)
    router.post('/filters', controller.filters)
    router.get('/reviews/:id', controller.getReviews)
    router.post('/product-banner', controller.productBanner)
    router.get('/product-detail/:slug', controller.productDetail)
    router.get('/variants/:id', controller.variants)
    router.post('/manage-wishlist', authorize.verifyToken, controller.manageWishlist)

    router.post('/search', controller.validate('search'), controller.search)
    router.get('/suggestions', controller.suggestions)
    router.get('/autocomplete', controller.autocomplete)

    router.get('/available-stores/:name', nonListController.getAvailableStores)
    router.post('/product-enquiry', nonListController.productEnquiry)

    router.post('/add-to-compare', controller.validate('addToCompare'), controller.addToCompare)
    router.get('/compare', controller.compare)
    router.post('/remove-from-compare', controller.validate('removeCompare'), controller.removeCompare)

    router.get('/coating', coatingController.listCoatings)

    router.post('/add-review', authorize.verifyToken, reviewController.addReview)

    router.get('/slugs-for-sitemap', controller.slugsForSitemap)

    return router;
}