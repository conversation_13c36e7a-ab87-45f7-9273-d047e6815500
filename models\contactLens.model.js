const mongoose = require("mongoose");

const contactLensSchema = new mongoose.Schema({
    title : {
        en: { type: String, required: true },
        ar: { type: String} 
    },
    description: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    sectionOne: {
        title: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        image: { type: String, required: true }
    },
    sectionTwo: {
        title: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        steps: [{
            step: { type: String },
            title: {
                en: { type: String, required: true },
                ar: { type: String }
            },
            description: {
                en: { type: String, required: true },
                ar: { type: String }
            },
        }]
    },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String }
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String }
        },
        ogImage: { type: String }
    },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    refid: { type: String, required: true },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model("contactLens", contactLensSchema)