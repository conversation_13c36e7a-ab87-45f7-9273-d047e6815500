const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: { type: String}
    },
    banner: { type: String },
    hoverImage: { type: String },
    image: { type: String,},
    isRoot: { type: Boolean, default: true },
    root: { type: mongoose.Schema.Types.ObjectId, ref: 'categories' },
    parent: { type: mongoose.Schema.Types.ObjectId, ref: 'categories' },
    slug: { type: String, required: true },
    refid: { type: String, required: true, unique: true },
    categoryId: { type: String, unique: true },
    isCashbackEnabled: { type: Boolean, default: false },
    cashbackPercentage: { type: Number, default: 0 },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isFilterable: { type: Boolean },
    topCategory: { type: Boolean },
    isDelete: { type: Boolean, default: false },
    inHome: { type: Boolean, default: false },
    order : { type: Number },
    subCategory: [{ type: mongoose.Schema.Types.ObjectId, ref: 'sub.categories' }],
    storeId: { type: String, required: true, default: 'ae' },
    seoDetails: {
        title: {
            en: { type: String },
            ar: { type: String }
        },
        description: {
            en: { type: String },
            ar: { type: String }
        },
        keywords: {
            en: { type: String },
            ar: { type: String },
        },
        canonical: {
            en: { type: String },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
}, { timestamps: true });

categorySchema.index({ refid: 1  }, {unique: true});
categorySchema.index({ isActive: 1, isDelete: 1 });

module.exports = mongoose.model('categories', categorySchema);