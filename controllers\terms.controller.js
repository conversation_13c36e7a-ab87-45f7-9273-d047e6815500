const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require('../services/terms.service')
const axios = require('axios');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('content', 'Content is required').exists(),
            ]
        }
    }
}

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 400, errors, messages.VALIDATION_ERROR);
        }
        let { body } = req;
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" });
        return helper.deliverResponse(res, 200, response, messages.TERMS.CREATED);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.getTerms = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const response = await service.find({ isDelete: false, storeId: storeid });
        let data;
        if (language) {
            data = response.map(item => {
                return {
                    ...item?._doc,
                    title: item.title[language],
                    content: item.content[language]
                };
            });
        } else {
            data = response;
        }
        return helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.getDashboardTerms = async (req, res) => {
    try {
        let {storeid } = req.headers;
        storeid = storeid ? storeid : 'sa';
        const response = await service.find({ isDelete: false, storeId: storeid });
        let data = response;
        return helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const response = await service.update({ storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" });
        return helper.deliverResponse(res, 200, response, messages.TERMS.UPDATED);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}