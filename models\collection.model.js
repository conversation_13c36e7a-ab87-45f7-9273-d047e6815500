const mongoose = require('mongoose');

const collectionSchema = new mongoose.Schema({
    title: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    banner: { type: String },
    image: { type: String },
    products: [{ type: mongoose.Schema.Types.ObjectId, ref: 'products' }],
    type: { type: String },
    refid: { type: String, required: true },
    slug: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isFeatured: { type: Boolean },
    isDelete: { type: Boolean, default: false },
    inHome: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' },
    seoDetails: {
        title: {
            en: { type: String },
            ar: { type: String }
        },
        description: {
            en: { type: String },
            ar: { type: String }
        },
        keywords: {
            en: { type: String },
            ar: { type: String },
        },
        canonical: {
            en: { type: String },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
}, { timestamps: true });

module.exports = mongoose.model('collections', collectionSchema)