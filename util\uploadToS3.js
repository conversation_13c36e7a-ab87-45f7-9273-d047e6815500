const { s3, bucketName } = require('../config/s3');
const { PutObjectCommand } = require("@aws-sdk/client-s3");
const fs = require("fs");


const uploadToS3 = async (fileContent, key, type) => {
    // const fileContent = fs.readFileSync(filePath);
    const params = {
        Bucket: bucketName,
        Key: key,
        Body: fileContent,
        ContentType: type,
        ACL: "public-read",
    };
    const command = new PutObjectCommand(params);
    await s3.send(command);
    const fileURL = `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
    // return fileURL;
    return {
        key,
        fileURL
    };
};

module.exports = {
    uploadToS3
}