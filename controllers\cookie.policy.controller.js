const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require('../services/cookie.policy.service')
const axios = require('axios');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('content', 'Content is required').exists(),
            ]
        }
    }
}

exports.create = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, messages.VALIDATION_ERROR);
        }
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" })
        return helper.deliverResponse(res, 200, response, messages.COOKIE_POLICY.CREATED);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.getCookiePolicy = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : 'sa';
        
        let data;
        if (language) {
            data = await service.find({ isDelete: false, storeId: storeid });
            data = data.map(item => {
                return {
                    ...item?._doc,
                    title: item.title[language],
                    content: item.content[language]
                }
            })
        } else {
            data = await service.find({ isDelete: false, storeId: storeid });
        }
        return helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.update = async (req, res) => {
    try {
        const { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.update({ storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" })
        return helper.deliverResponse(res, 200, response, messages.COOKIE_POLICY.UPDATED);
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}
