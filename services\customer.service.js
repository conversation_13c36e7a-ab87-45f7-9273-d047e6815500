const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Customers(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}, page = 0, limit = 0, sort = { createdAt: -1 }) => {
    try {
        let response = await db.Customers.find(query, projection)
            .sort(sort)
            .skip((page - 1) * limit)
            .limit(limit * 1);
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Customers.findOne(query, projection)
        .populate('wishlist', 'storeId')
        .populate('tryCart.product', 'storeId isActive isDelete')
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Customers.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Customers.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.aggregate = async (query) => {
    try {
        let response = await db.Customers.aggregate(query)
        return response
    } catch (error) {
        throw error;
    }
}