const messages = require('../config/constants/messages')
const helper = require('../util/responseHelper')
const service = require('../services/subscribe.plan.service')
const productService = require('../services/products.service');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const result = await service.create(body)
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SUBSCRIBE_PLAN.CREATED.error_code,
                error_message: messages.SUBSCRIBE_PLAN.CREATED.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUBSCRIBE_PLAN.CREATED.error_code,
                error_message: messages.SUBSCRIBE_PLAN.CREATED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.find({ isDelete: false, storeId: storeid })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.findOne = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params
        const result = await service.findOne({ refid: refid, isDelete: false, storeId: storeid })
        if(!result) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req
        const { refid } = req.params
        const result = await service.update({ refid: refid, storeId: storeid }, body)
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUBSCRIBE_PLAN.UPDATED.error_code,
                error_message: messages.SUBSCRIBE_PLAN.UPDATED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.activePlans = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.find({ isActive: true, isDelete: false, storeId: storeid })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params
        const plan = await service.findOne({ refid: refid, storeId: storeid })
        const products = await productService.find({ plans: plan._id, isDelete: false, storeId: storeid })
        if (products.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Plan is used in products"
            })
            return;
        }
        const result = await service.update({ refid: refid, storeId: storeid }, { isDelete: true })
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        } else {
            helper.deliverResponse(res, 200, result, {
                error_code: messages.SUBSCRIBE_PLAN.DELETED.error_code,
                error_message: messages.SUBSCRIBE_PLAN.DELETED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
} 