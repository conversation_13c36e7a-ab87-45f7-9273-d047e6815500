const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.Products(data);
        await response.save();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.miniFind = async (query, projection = {}) => {
    try {
        let response = await db.Products.find(query, projection).populate("brand", "name").lean();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}, page = 0, limit = 0, sort = { createdAt: -1 }) => {
    try {
        let response = await db.Products.find(query, projection)
            .sort(sort)
            .skip((page - 1) * limit)
            .limit(limit * 1)
            .populate("category", "name isRoot")
            .populate("subCategory", "name parent")
            .populate("ageGroup", "name")
            .populate("size", "name")

            .populate("brand", "name")
            .populate("color", "name color")
            .populate("label", "name")
            .populate("frameType", "name")
            .populate("frameShape", "name")
            .populate("frontMaterial", "name")
            .populate("lensMaterial", "name")
            .populate("type", "name")

            .populate("recommendedProducts", "sku")
            .populate("boughtTogether", "sku")
            .populate("sphValues", "name")
            .populate("cylValues", "name")
            .populate("axisValues", "name")
            .populate("addValues", "name")
            .populate("contactSph", "name")
            .populate("contactCyl", "name")
            .populate("contactAxis", "name")
            .populate("parent", "name refid")
        // .populate({
        //     path: "variants",
        //     select: "name slug color brand thumbnail isDelete isActive",
        //     populate: [{ path: "color", select: "name color" }, { path: "brand", select: "name" }]
        // });
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findLean = async (query, projection = {}, page = 0, limit = 0, sort = { createdAt: -1 }) => {
    try {
        let response = await db.Products.find(query, projection)
            .sort(sort)
            .skip((page - 1) * limit)
            .limit(limit * 1)
            // .lean()
            .populate("category", "name isRoot")
            .populate("subCategory", "name parent")
            .populate("ageGroup", "name")

            .populate("brand", "name")
            .populate("color", "name color")
            .populate("label", "name")
            .populate("frameType", "name")
            .populate("frameShape", "name")
            .populate("frontMaterial", "name")
            .populate("lensMaterial", "name")
            .populate("type", "name")
            .populate("size", "name")
            .populate("contactSize", "name")
            // .populate("mainProduct", "sku")

            .populate("recommendedProducts", "sku")
            .populate("boughtTogether", "sku")
            .populate("sphValues", "name")
            .populate("cylValues", "name")
            .populate("axisValues", "name")
            .populate("addValues", "name")
            .populate("contactSph", "name")
            .populate("contactCyl", "name")
            .populate("contactAxis", "name")
            .populate("parent", "sku")
        // .populate({
        //     path: "variants",
        //     select: "name slug color brand thumbnail isDelete isActive",
        //     populate: [{ path: "color", select: "name color" }, { path: "brand", select: "name" }]
        // });
        return response;
    } catch (error) {
        throw error;
    }
};

exports.simpleCount = async (query) => {
    try {
        let response = await db.Products.find(query).countDocuments();
        return response
    } catch {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Products.findOne(query, projection)
            .populate("category", "name slug isCashbackEnabled cashbackPercentage",)
            .populate("subCategory", "name parent")
            .populate("brand", "name slug isCashbackEnabled cashbackPercentage")
            .populate("subBrand", "name refid")
            .populate("recommendedProducts", "name")
            .populate("boughtTogether", "name")
            .populate("ageGroup", "name")
            .populate("frameType", "name")
            .populate("frameShape", "name")
            .populate("plans")
            .populate("color", "name color")
            .populate("size", "name")
            .populate("contactSize", "name")
            // .populate({
            //     path: "variants",
            //     select: "name slug color brand thumbnail isDelete isActive",
            //     populate: [{ path: "color", select: "name color" }, { path: "brand", select: "name" }]
            // })
            .populate("label", "name")
            .populate("frontMaterial", "name")
            .populate("type", "name")
            .populate("lensMaterial", "name")
            .populate("sphValues", "name")
            .populate("cylValues", "name")
            .populate("axisValues", "name")
            .populate("addValues", "name")
            .populate("contactSph", "name")
            .populate("contactCyl", "name")
            .populate("contactAxis", "name")
            .populate("parent", "name refid");
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.Products.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.Products.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.aggregateCount = async (query) => {
    try {
        // let response = await db.Products.find(query).countDocuments();
        response = await db.Products.aggregate([
            {
                $match: {
                    ...query,
                }
            },
            // {
            //     $addFields: {
            //         parent: {
            //             $cond: [
            //                 "$parent",
            //                 "$parent",
            //                 "$_id"
            //             ]
            //         }
            //     }
            // },
            {
                $group: {
                    _id: "$parent",
                    // products: { $push: "$refid" },
                }
            },
            {
                $match: {
                    _id: { $ne: null }
                }
            },
            {
                $lookup: {
                    from: "product.heads",
                    localField: "_id",
                    foreignField: "_id",
                    as: "head"
                }
            },
            {
                $match: {
                    "head.0": {
                        "$exists": true
                    }
                }
            }
        ])

        return response.length;
    } catch (error) {
        throw error;
    }
};

exports.pagination = async (query, page, limit, projection = {}, sort = {}) => {
    try {
        let response;
        // const sortValue = (sort?.type === "price" || sort?.type === "order") ? sort.value : { isSale: -1, position: -1 }
        let sortTwo = {}
        if (sort?.type == "price") {
            if (sort?.value?.lastPrice == 1) {
                sortTwo = { "variants.lastPrice": 1 }
            } else {
                sortTwo = { "variants.lastPrice": -1 }
            }
        } else if (sort?.type === "order") {
            sortTwo = { "variants.position": -1 }
        } else {
            sortTwo = { "variants.isSale": -1, "variants.position": -1 }
        }
        let addFields = {};
        if (sort.type === "price") {
            addFields = {
                $addFields: {
                    lastPrice: {
                        $cond: [
                            { $and: [{ $ne: ["$offerPrice.aed", null] }, { $gt: ["$offerPrice.aed", 0] }] },
                            "$offerPrice.aed",
                            "$price.aed",
                        ]
                    }
                }
            }
        } else {
            addFields = {
                $addFields: {
                    isSale: {
                        $cond: [
                            {
                                $eq: ["$label", sort.value], // Check if name matches "sale"
                            },
                            1,
                            0,
                        ],
                    },
                },
            }
        }

        // const price = sort.type === "price" ? { $match: { lastPrice: { $ne: null } } } : { $match: { isDelete: false } }
        response = await db.Products.aggregate([
            {
                $match: {
                    ...query,
                }
            },
            // {
            //     $addFields: {
            //         isProduct: {
            //             $cond: [
            //                 "$parent",
            //                 false,
            //                 true
            //             ]
            //         }
            //     }
            // },
            // {
            //     $addFields: {
            //         parent: {
            //             $cond: [
            //                 "$parent",
            //                 "$parent",
            //                 "$_id"
            //             ]
            //         }
            //     }
            // },
            {
                $group: {
                    _id: "$parent",
                    // isProduct: { $first: "$isProduct" },
                }
            },
            {
                $match: {
                    _id: { $ne: null }
                }
            },
            {
                $lookup: {
                    from: "product.heads",
                    localField: "_id",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $lookup: {
                                from: "products",
                                localField: "products",
                                foreignField: "_id",
                                pipeline: [
                                    addFields,
                                    {
                                        $project: {
                                            _id: 1,
                                            labelDetails: 1,
                                            isSale: 1,
                                            lastPrice: 1,
                                            position: 1
                                        }
                                    }
                                ],
                                as: "products"
                            }
                        },
                        {
                            $project: {
                                products: 1
                            }
                        }
                    ],
                    as: "variants"
                }
            },
            // {
            //     $lookup: {
            //         from: "products",
            //         localField: "_id",
            //         foreignField: "_id",
            //         pipeline: [
            //             addFields,
            //             {
            //                 $project: {
            //                     _id: 1,
            //                     labelDetails: 1,
            //                     isSale: 1,
            //                     lastPrice: 1,
            //                     position: 1
            //                 }
            //             }
            //         ],
            //         as: "other"
            //     }
            // },
            {
                $project: {
                    variants: { $arrayElemAt: ["$variants.products", 0] },
                    // isProduct: 1,
                    // other: 1
                }
            },
            // {
            //     $addFields: {
            //         variants: {
            //             $cond: [
            //                 "$isProduct",
            //                 "$other",
            //                 "$variants"
            //             ]
            //         }
            //     }
            // },
            {
                $sort: { ...sortTwo, "variants.createdAt": -1, "variants._id": 1 }, // Prioritize `label: sale`, then sort by `_id`
            },
            {
                $skip: (page - 1) * limit
            },
            {
                $limit: limit * 1
            },
            {
                $lookup: {
                    from: "products",
                    localField: "variants._id",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $lookup: {
                                from: "labels", // Replace with the actual collection name for the referenced labels
                                localField: "label", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "labelDetails", // The output array with matched label documents,
                            },
                        },
                        {
                            $lookup: {
                                from: "brands", // Replace with the actual collection name for the referenced labels
                                localField: "brand", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "brand", // The output array with matched label documents,
                            },
                        },
                        {
                            $lookup: {
                                from: "colors", // Replace with the actual collection name for the referenced labels
                                localField: "color", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "color", // The output array with matched label documents,
                            },
                        },
                        {
                            $lookup: {
                                from: "categories", // Replace with the actual collection name for the referenced labels
                                localField: "category", // Field in Product that references Label
                                foreignField: "_id", // Field in Label that is matched with Product's `label`
                                as: "category", // The output array with matched label documents,
                            },
                        },
                        {
                            $project: {
                                images: 1,
                                price: 1,
                                offerPrice: 1,
                                offerPercentage: 1,
                                color: {
                                    _id: 1,
                                    name: 1,
                                    color: 1,
                                    _id: 1
                                },
                                refid: 1,
                                productType: 1,
                                name: 1,
                                brand: {
                                    name: 1,
                                    slug: 1
                                },
                                category: {
                                    name: 1,
                                    isRoot: 1,
                                    isCashbackEnabled: 1,
                                    cashbackPercentage: 1,
                                },
                                isCashbackEnabled: 1,
                                cashbackPercentage: 1,
                                slug: 1,
                                upc: 1,
                                showDiscountPercentage: 1,
                                thumbnail: 1,
                                isVirtualTry: 1,
                                isDefaultVariant: 1,
                                labelDetails: {
                                    name: 1
                                },
                                storeId: 1
                            },
                        }
                    ],
                    as: "variants"
                }
            },
            {
                $project: {
                    variants: 1
                }
            }
        ])
        // }
        // console.log(response)
        // for(let item of response){
        //     console.log(item.isProduct)
        //     console.log(item.other)
        //     console.log(item.variants)
        // }
        // console.log(response.find(item=> item.isProduct))
        return response;
    } catch (error) {
        throw error;
    }
};

exports.aggreagte = async (query) => {
    try {
        let response = await db.Products.aggregate(query);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.updateMany = async (query, data) => {
    try {
        let response = await db.Products.updateMany(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};
