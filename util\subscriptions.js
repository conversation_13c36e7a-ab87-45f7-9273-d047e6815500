const subscriptionService = require("../services/subscription.service");
const orderService = require("../services/order.service");
const productService = require("../services/products.service");
const customerService = require("../services/customer.service");
const axios = require("axios");
const template = require("../util/templates");
const emailHelper = require("../util/emailHelper");
const { NOONAPI } = require("../config/constants/noon");

exports.subscriptionPayment = async () => {
    try {
        const subscriptions = await subscriptionService.find({
            isDelete: false,
            unsubscribed: false
        });
        for (let subscription of subscriptions) {
            try {
                const today = new Date();
                const nextOrderDate = new Date(subscription.nextOrderDate);
                if (nextOrderDate.toDateString() === today.toDateString()) {
                    const order = await orderService.findOne({
                        _id: subscription?.order,
                        isDelete: false
                    });
                    const count = await orderService.count({});
                    const paymentPayload = {
                        apiOperation: "INITIATE",
                        order: {
                            name: `Payment for Order YATE-0000${count + 1}`,
                            reference: `YATE-0000${count + 1}`
                        },
                        configuration: {
                            paymentAction: "AUTHORIZE,SALE"
                        },
                        paymentData: {
                            type: "Subscription",
                            data: {
                                subscriptionIdentifier: subscription?.subscriptionIdentifier
                            }
                        }
                    };
                    try {
                        const paymentResponse = await axios.post(
                            NOONAPI,
                            paymentPayload,
                            {
                                headers: { Authorization: process.env.PAYMENT_KEY }
                            }
                        );
                        if (paymentResponse?.data?.resultCode == 0) {
                            const capturePayload = {
                                apiOperation: "CAPTURE",
                                order: {
                                    Id: paymentResponse?.data?.result?.order?.id
                                },
                                transaction: {
                                    amount: paymentResponse?.data?.result?.order?.amount,
                                    currency: "AED",
                                    TransactionReference:
                                        paymentResponse?.data?.result?.order?.reference,
                                    description: `Payment for Order YATE-0000${count + 1}`
                                }
                            };
                            const captureResponse = await axios.post(
                                NOONAPI,
                                capturePayload,
                                {
                                    headers: { Authorization: process.env.PAYMENT_KEY }
                                }
                            );
                            if (captureResponse?.data?.resultCode != 0) {
                                await subscriptionService.update(
                                    { _id: subscription._id },
                                    { status: "Payment Failed" }
                                );
                            }
            
                            const expectedDate = new Date();
                            expectedDate.setDate(expectedDate.getDate() + 7);

                            const duration = subscription?.plan?.duration?.duration;
                            const durationType = subscription?.plan?.duration?.type;
                            let nextOrderDate = null;
                            let currentDate = new Date();
                            if (durationType === "Weekly") {
                                nextOrderDate = new Date(currentDate.getTime() + duration * 604800000); // 604800000 = 7 * 24 * 60 * 60 * 1000 (milliseconds in a week)
                            } else if (durationType === "Monthly") {
                                nextOrderDate = new Date(currentDate.getTime() + duration * 2629800000); // 2629800000 = 30.44 * 24 * 60 * 60 * 1000 (approximate milliseconds in a month)
                            } else if (durationType === "Yearly") {
                                nextOrderDate = new Date(currentDate.getTime() + duration * 31557600000); // 31557600000 = 365 * 24 * 60 * 60 * 1000 (approximate milliseconds in a year)
                            }

                            let orderPayload = {
                                refid: count + 1,
                                customer: order?.customer,
                                address: order?.address,
                                expectedDate: expectedDate,
                                deliveryCharge: order?.deliveryCharge,
                                paymentMethod: order?.paymentMethod,
                                orderNo: `YATE-0000${count + 1}`,
                                orderStatus: "PLACED",
                                paymentStatus: "PAID",
                                products: order?.products,
                                history: [
                                    {
                                        status: "PLACED",
                                        date: new Date()
                                    }
                                ],
                                baseTotal: order?.baseTotal,
                                savings: order?.savings,
                                total: order?.total,
                                nextOrderDate: nextOrderDate,
                                isTryCart: false
                            };
                            const newOrder = await orderService.create(orderPayload);
                            const orderDateUpdate = await subscriptionService.update(
                                { _id: subscription._id },
                                { nextOrderDate: nextOrderDate }
                            )
                            if (newOrder instanceof Error) {
                            }
                            for (let product of order?.products) {
                                await productService.update(
                                    { _id: product?.product },
                                    { $inc: { stock: -product?.quantity } }
                                );
                            }
                            const customer = await customerService.findOne({
                                _id: order?.customer,
                                isDelete: false
                            });
                            if (customer && customer?.email) {
                                const addressDetails = await orderService.findOne({
                                    _id: order?.address,
                                    isDelete: false
                                });
                                const orderdata = {
                                    status: "Placed",
                                    order: newOrder,
                                    address: addressDetails,
                                    products: order?.products
                                };
                                const mailOptions = {
                                    from: process.env.EMAIL_USER,
                                    to: customer?.email,
                                    subject: "Order Placed",
                                    html: await template.orderPlaced(orderdata),
                                    attachments: [
                                        {
                                            filename: "logo-light-full.png",
                                            path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                                            cid: "logo" // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                                        }
                                    ]
                                };

                                emailHelper.sendMail(mailOptions, (error, info) => {
                                    if (error) {
                                    } else {
                                    }
                                });
                            }
                        } else {
                            try {
                                await subscriptionService.update(
                                    { _id: subscription?._id },
                                    { status: "Payment Failed" }
                                );
                            } catch (error) {
                                console.log(
                                    `Error updating subscription status ${subscription._id}: ${error}`
                                );
                            }
                            console.log(
                                "error caught in subscription payment initiation :: ",
                                paymentResponse
                            );
                        }
                    } catch (error) {
                        console.log(
                            `Error processing payment for subscription ${subscription._id}: ${error}`
                        );
                    }
                }
            } catch (error) {
                console.log(`Error processing subscription ${subscription._id}: ${error}`);
            }
        }
    } catch (error) {
        console.log("Error retrieving subscriptions: ", error);
    }
};
