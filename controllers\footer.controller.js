const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const generalSettingService = require("../services/general.settings.service");
const service = require("../services/footer.service");

exports.footer = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language: "en";
        storeid = storeid ? storeid: "ae";

        const general = await generalSettingService.findOne({ isDelete: false, storeId: storeid });
        let projection = {_id : 0, __v : 0, createdAt : 0, updatedAt : 0, isDelete : 0, isActive : 0, refid : 0};
        const footer1 = await service.findOne({ isDelete: false, storeId: storeid }, projection);
        const footer= JSON.parse(JSON.stringify(footer1))
        footer.firstCol.title = footer.firstCol.title[language];
        footer.firstCol.links.map((item) => {
            item.title = item.title[language];
        })
        footer.secondCol.title = footer.secondCol.title[language];
        footer.secondCol.links.map((item) => {
            item.title = item.title[language];
        })
        footer.thirdCol.title = footer.thirdCol.title[language];
        footer.thirdCol.links.map((item) => {
            item.title = item.title[language];
        })
        footer.fourthCol.title = footer.fourthCol.title[language];
        footer.fourthCol.links.map((item) => {
            item.title = item.title[language];
        })

        const response = {
            logo: general?.logo ? process.env.DOMAIN + general?.logo : null,
            socialMedia: general?.socialMedia.map((item) => {
                return {
                    name: item?.name,
                    link: item?.link,
                    icon: item?.icon ? process.env.DOMAIN + item?.icon : null
                };
            }),
            contact: {
                address: general?.contact?.address[language],
                email: general?.contact?.email,
                mobileOne: general?.contact?.mobileOne,
                mobileTwo: general?.contact?.mobileTwo
            },
            copyRight: general?.copyRight[language],
            footer: footer,
        };
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = (await service.count({})) + 1;
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.FOOTER.CREATED.error_code,
            error_message: messages.FOOTER.CREATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        let { body } = req;
        const response = await service.update({ refid, storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.FOOTER.UPDATED.error_code,
            error_message: messages.FOOTER.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};
