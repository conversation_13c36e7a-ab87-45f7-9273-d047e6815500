{"name": "yateem-api", "version": "1.0.0", "description": "To make it easy for you to get started with GitLab, here's a list of recommended next steps.", "main": "app.js", "scripts": {"dev": "nodemon ./app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://gitlab.com/shifas/yateem-api.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://gitlab.com/shifas/yateem-api/issues"}, "homepage": "https://gitlab.com/shifas/yateem-api#readme", "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "adm-zip": "^0.5.10", "async-mutex": "^0.5.0", "axios": "^1.6.3", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "firebase": "^10.11.0", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "minisearch": "^6.3.0", "moment": "^2.30.1", "mongoose": "^7.6.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "node-stream-zip": "^1.15.0", "nodemailer": "^6.9.12", "nodemon": "^3.0.1", "object-hash": "^3.0.0", "sharp": "^0.33.5", "slugify": "^1.6.6", "xmlbuilder": "^15.1.1"}}