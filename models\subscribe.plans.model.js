const mongoose = require('mongoose')

const subscribePlansSchema = new mongoose.Schema(
  {
    name: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    description: {
      en: { type: String, required: true },
      ar: { type: String },
    },
    discountPercent: { type: Number, required: true },
    duration: {
      duration: { type: Number, required: true },
      type: {
        type: String,
        required: true,
        enum: ["Weekly", "Monthly", "Yearly"],
      },
    },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
  },
  { timestamps: true }
);

module.exports = mongoose.model('subscribe.plans', subscribePlansSchema)