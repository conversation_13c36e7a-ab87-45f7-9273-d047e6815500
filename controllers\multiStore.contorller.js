const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/multiStore.service");
const dashboardSettingsService = require("../services/dashboard.settings.service");
const imageMapService = require("../services/imageMap.service")
const generalSettingsService = require("../services/general.settings.service");
const transaltionService = require("../services/text.service");
const vmPolicyService = require("../services/vm.policy.service");
const db = require('../models/index');
const metaTagsService = require("../services/meta.tag.service");
const trycartService = require("../services/trycart.service");
const footerService = require("../services/footer.service");
const contactUsService = require("../services/contact.banner.service")
const insuranceContentService = require("../services/insurance.content.service")
const contactLensPageService = require("../services/contactLens.service")
const faqService = require("../services/faq.service")
const termsService = require("../services/terms.service")
const privacyPolicyService = require("../services/privacy.policy.service")
const cookiePolicyService = require("../services/cookie.policy.service")
const refundPolicyService = require("../services/refund.policy.service")
const shippingPolicyService = require("../services/shipping.service")
const returnPolicyService = require("../services/return.policy.service")
const aboutService = require("../services/about.service");
const { default: axios } = require("axios");


exports.create = async (req, res) => {
    try {
        let { body } = req;
        body.refid = await service.count({}) + 1;
        const isStoreExist = await service.findOne({ storeId: body?.storeId, isDelete: false });
        if (isStoreExist) {
            return helper.deliverResponse(res, 422, {}, {
                "error_code": 1,
                "error_message": "Store already exists"
            });
        }
        const response = await service.create(body);

        let [
            dashboardSettings, dashboardSettingsCount,
            imageMap, imageMapCount,
            settings, settingsCount,
            translation, translationCount,
            vmPolicy,
            metaTags,
            tryCartCount,
            footer, footerCount,
            contactUs, contactUsCount,
            insuranceContent, insuranceContentCount,
            contactLensPage, contactLensPageCount,
            faq, faqCount,
            terms, termsCount,
            privacyPolicy, privacyPolicyCount,
            cookiePolicy, cookiePolicyCount,
            refundPolicy, refundPolicyCount,
            shippingPolicy, shippingPolicyCount,
            returnPolicy, returnPolicyCount,
            about, aboutCount,
            // old
            oldDashboardSettings,
            oldImageMap,
            oldSettings,
            oldTranslation,
            oldTryCart,
            oldFooter,
            oldContactUs,
            oldInsuranceContent,
            oldContactLensPage,
            oldContactFaq,
            oldInsuranceFaq,
            oldTerms,
            oldPrivacyPolicy,
            oldCookiePolicy,
            oldRefundPolicy,
            oldShippingPolicy,
            oldReturnPolicy,
            oldAbout
        ] = await Promise.all([
            dashboardSettingsService.findOne({ isDelete: false }), dashboardSettingsService.count({}),
            imageMapService.findOne({ isDelete: false }), imageMapService.count({}),
            generalSettingsService.findOne({ isDelete: false }), generalSettingsService.count({}),
            transaltionService.findOne({ isDelete: false }), transaltionService.count({}),
            vmPolicyService.findOne({ isDelete: false }),
            metaTagsService.findOne({ isDelete: false }),
            trycartService.count({}),
            footerService.findOne({ isDelete: false }), footerService.count({}),
            contactUsService.findOne({ isDelete: false }), contactUsService.count({}),
            insuranceContentService.findOne({ isDelete: false }), insuranceContentService.count({}),
            contactLensPageService.findOne({ isDelete: false }), contactLensPageService.count({}),
            faqService.findOne({ isDelete: false }), faqService.count({}),
            termsService.findOne({ isDelete: false }), termsService.count({}),
            privacyPolicyService.findOne({ isDelete: false }), privacyPolicyService.count({}),
            cookiePolicyService.findOne({ isDelete: false }), cookiePolicyService.count({}),
            refundPolicyService.findOne({ isDelete: false }), refundPolicyService.count({}),
            shippingPolicyService.findOne({ isDelete: false }), shippingPolicyService.count({}),
            returnPolicyService.findOne({ isDelete: false }), returnPolicyService.count({}),
            aboutService.findOne({ isDelete: false }), aboutService.count({}),
            //old values
            dashboardSettingsService.findOne({ isDelete: false, storeId: body?.storeId }),
            imageMapService.findOne({ isDelete: false, storeId: body?.storeId }),
            generalSettingsService.findOne({ isDelete: false, storeId: body?.storeId }),
            transaltionService.findOne({ isDelete: false, storeId: body?.storeId }),
            trycartService.findOne({ isDelete: false, storeId: body?.storeId }),
            footerService.findOne({ isDelete: false, storeId: body?.storeId }),
            contactUsService.findOne({ isDelete: false, storeId: body?.storeId }),
            insuranceContentService.findOne({ isDelete: false, storeId: body?.storeId }),
            contactLensPageService.findOne({ isDelete: false, storeId: body?.storeId }),
            faqService.findOne({ type: "contactLens", isDelete: false, storeId: body?.storeId }),
            faqService.findOne({ type: "insurance", isDelete: false, storeId: body?.storeId }),
            termsService.findOne({ isDelete: false, storeId: body?.storeId }),
            privacyPolicyService.findOne({ isDelete: false, storeId: body?.storeId }),
            cookiePolicyService.findOne({ isDelete: false, storeId: body?.storeId }),
            refundPolicyService.findOne({ isDelete: false, storeId: body?.storeId }),
            shippingPolicyService.findOne({ isDelete: false, storeId: body?.storeId }),
            returnPolicyService.findOne({ isDelete: false, storeId: body?.storeId }),
            aboutService.findOne({ isDelete: false, storeId: body?.storeId })


        ]);

        const updateDashboardSettings = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyDashboardSettings } = dashboardSettings?._doc;
            copyDashboardSettings.storeId = body?.storeId;
            if (oldDashboardSettings) {
                copyDashboardSettings.refid = oldDashboardSettings?.refid;
            } else {
                copyDashboardSettings.refid = dashboardSettingsCount + 1;
            }
            copyDashboardSettings.items = items.map(item => ({ type: item?.type, isActive: item?.isActive }))

            return [{ storeId: body?.storeId, refid: copyDashboardSettings?.refid }, copyDashboardSettings]
        }

        const updateImageMap = () => {
            let { _id, createdAt, updatedAt, __v, refid, ...copyImageMap } = imageMap?._doc
            copyImageMap.storeId = body?.storeId;
            if (oldImageMap) {
                copyImageMap.refid = oldImageMap?.refid;
            } else {
                copyImageMap.refid = imageMapCount + 1;
            }
            return [{ storeId: body?.storeId, refid: copyImageMap?.refid }, copyImageMap]
        }

        const updateSettings = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copySettings } = settings?._doc
            copySettings.storeId = body?.storeId;
            if (oldSettings) {
                copySettings.refid = oldSettings?.refid;
            } else {
                copySettings.refid = settingsCount + 1;
            }
            copySettings.clickAndCollect = false;
            copySettings.tamara = false;
            copySettings.isStoreLocatorIframe = true;
            return [{ storeId: body?.storeId, refid: copySettings?.refid }, copySettings]
        }

        const updateTranslation = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyTranslation } = translation?._doc
            copyTranslation.storeId = body?.storeId;
            if (oldTranslation) {
                copyTranslation.refid = oldTranslation?.refid;
            } else {
                copyTranslation.refid = translationCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyTranslation?.refid }, copyTranslation]
        }

        const updateVmPolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyVmPolicy } = vmPolicy?._doc
            copyVmPolicy.storeId = body?.storeId;

            return [{ storeId: body?.storeId }, copyVmPolicy]
        }
        const updateMetaTags = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyMetaTags } = metaTags?._doc
            copyMetaTags.storeId = body?.storeId;

            return [{ storeId: body?.storeId }, copyMetaTags]
        }

        const updateFooter = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyFooter } = footer?._doc
            copyFooter.storeId = body?.storeId;
            if (oldFooter) {
                copyFooter.refid = oldFooter?.refid;
            } else {
                copyFooter.refid = footerCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyFooter?.refid }, copyFooter]
        }

        const updateContactUs = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyContactUs } = contactUs?._doc
            copyContactUs.storeId = body?.storeId;
            if (oldContactUs) {
                copyContactUs.refid = oldContactUs?.refid;
            } else {
                copyContactUs.refid = contactUsCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyContactUs?.refid }, copyContactUs]
        }

        const updateInsuranceContent = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyInsuranceContent } = insuranceContent?._doc
            copyInsuranceContent.storeId = body?.storeId;
            if (oldInsuranceContent) {
                copyInsuranceContent.refid = oldInsuranceContent?.refid;
            } else {
                copyInsuranceContent.refid = insuranceContentCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyInsuranceContent?.refid }, copyInsuranceContent]
        }

        const updateContactLensePage = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyContactLensPage } = contactLensPage?._doc
            copyContactLensPage.storeId = body?.storeId;
            if (oldContactLensPage) {
                copyContactLensPage.refid = oldContactLensPage?.refid;
            } else {
                copyContactLensPage.refid = contactLensPageCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyContactLensPage?.refid }, copyContactLensPage]
        }

        const updateFaq = (type, count, oldFaq) => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyFaq } = faq?._doc
            copyFaq.storeId = body?.storeId;
            if (oldFaq) {
                copyFaq.refid = oldFaq?.refid;
            } else {
                copyFaq.refid = count;
            }
            copyFaq.type = type;

            return [{ storeId: body?.storeId, refid: copyFaq?.refid, type }, copyFaq]
        }

        const updateTerms = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyTerms } = terms?._doc
            copyTerms.storeId = body?.storeId;
            if (oldTerms) {
                copyTerms.refid = oldTerms?.refid;
            } else {
                copyTerms.refid = termsCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyTerms?.refid }, copyTerms]
        }

        const updatePrivacyPolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyPrivacyPolicy } = privacyPolicy?._doc
            copyPrivacyPolicy.storeId = body?.storeId;
            if (oldPrivacyPolicy) {
                copyPrivacyPolicy.refid = oldPrivacyPolicy?.refid;
            } else {
                copyPrivacyPolicy.refid = privacyPolicyCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyPrivacyPolicy?.refid }, copyPrivacyPolicy]
        }

        const updateCookiePolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyCookiePolicy } = cookiePolicy?._doc
            copyCookiePolicy.storeId = body?.storeId;
            if (oldCookiePolicy) {
                copyCookiePolicy.refid = oldCookiePolicy?.refid;
            } else {
                copyCookiePolicy.refid = cookiePolicyCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyCookiePolicy?.refid }, copyCookiePolicy]
        }

        const updateRefundPolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyRefundPolicy } = refundPolicy?._doc
            copyRefundPolicy.storeId = body?.storeId;
            if (oldRefundPolicy) {
                copyRefundPolicy.refid = oldRefundPolicy?.refid;
            } else {
                copyRefundPolicy.refid = refundPolicyCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyRefundPolicy?.refid }, copyRefundPolicy]
        }

        const updateShippingPolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyReturnPolicy } = shippingPolicy?._doc
            copyReturnPolicy.storeId = body?.storeId;
            if (oldShippingPolicy) {
                copyReturnPolicy.refid = oldShippingPolicy?.refid;
            } else {
                copyReturnPolicy.refid = shippingPolicyCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyReturnPolicy?.refid }, copyReturnPolicy]
        }

        const updateReturnPolicy = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyShippingPolicy } = returnPolicy?._doc
            copyShippingPolicy.storeId = body?.storeId;
            if (oldReturnPolicy) {
                copyShippingPolicy.refid = oldReturnPolicy?.refid;
            } else {
                copyShippingPolicy.refid = returnPolicyCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyShippingPolicy?.refid }, copyShippingPolicy]
        }

        const updateAbout = () => {
            let { _id, createdAt, updatedAt, __v, refid, items, ...copyAbout } = about?._doc
            copyAbout.storeId = body?.storeId;
            if (oldAbout) {
                copyAbout.refid = oldAbout?.refid;
            } else {
                copyAbout.refid = aboutCount + 1;
            }

            return [{ storeId: body?.storeId, refid: copyAbout?.refid }, copyAbout]
        }

        await Promise.all([
            generalSettingsService.upsert(...updateSettings()),
            dashboardSettingsService.upsert(...updateDashboardSettings()),
            imageMapService.upsert(...updateImageMap()),
            transaltionService.upsert(...updateTranslation()),
            vmPolicyService.upsert(...updateVmPolicy()),
            db.PaymentMethodFee.findOneAndUpdate({ storeId: body?.storeId, type: "COD" }, { fee: 0 }, { upsert: true }),
            db.PaymentMethodFee.findOneAndUpdate({ storeId: body?.storeId, type: "creditCard" }, { fee: 0 }, { upsert: true }),
            db.PaymentMethodFee.findOneAndUpdate({ storeId: body?.storeId, type: "vat" }, { fee: 0 }, { upsert: true }),
            metaTagsService.upsert(...updateMetaTags()),
            trycartService.upsert({ storeId: body?.storeId, refid: oldTryCart ? oldTryCart?.refid : tryCartCount + 1 }, { products: [], duration: "0", amount: 0 }),
            footerService.upsert(...updateFooter()),
            contactUsService.upsert(...updateContactUs()),
            insuranceContentService.upsert(...updateInsuranceContent()),
            contactLensPageService.upsert(...updateContactLensePage()),
            faqService.upsert(...updateFaq("insurance", faqCount + 1, oldInsuranceFaq)),
            faqService.upsert(...updateFaq("contactLens", faqCount + (oldInsuranceFaq ? 1 : 2), oldContactFaq)),
            termsService.upsert(...updateTerms()),
            privacyPolicyService.upsert(...updatePrivacyPolicy()),
            cookiePolicyService.upsert(...updateCookiePolicy()),
            refundPolicyService.upsert(...updateRefundPolicy()),
            shippingPolicyService.upsert(...updateShippingPolicy()),
            returnPolicyService.upsert(...updateReturnPolicy()),
            aboutService.upsert(...updateAbout())
        ])

        await axios.post(process.env.REVALIDATE, { tag: "country" });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.list = async (req, res) => {
    try {
        const result = await service.find({ isDelete: false, isActive: true });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.countries = async (req, res) => {
    try {
        const result = await service.find({ isDelete: false });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.findOne = async (req, res) => {
    try {
        const { refid } = req.params;
        const response = await service.findOne({ refid: refid, isDelete: false });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.FAQ.UPDATED.error_code,
            "error_message": messages.FAQ.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        const { body } = req;
        const response = await service.update({ refid: body?.refid, isDelete: false }, body);
        await axios.post(process.env.REVALIDATE, { tag: "country" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.FAQ.UPDATED.error_code,
            "error_message": body?.isDelete ? "Store deleted successfully" : messages.FAQ.UPDATED.error_message
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.listWeb = async (req, res) => {
    try {
        let { language } = req.headers;
        language = language ? language : "en";
        const result = await service.find({ isDelete: false, isActive: true });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        const data = result.map(item => {
            return {
                name: item.name[language],
                storeId: item.storeId,
                countryCode: item.countryCode,
                currencyCode: item.currencyCode,
                currencySymbol: item.currencySymbol
            }
        })
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getLocales = async (req, res) => {
    try {
        const result = await service.find({ isDelete: false, isActive: true });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        const data = [];

        result.forEach(item => {
            data.push(`${item.storeId}-en`)
            data.push(`${item.storeId}-ar`)
        })

        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}