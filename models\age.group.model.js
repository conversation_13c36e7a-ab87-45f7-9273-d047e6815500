const mongoose = require('mongoose')

const ageGroupSchema = new mongoose.Schema({
    name: { type: String, required: true },
    refid: { type: String, required: true },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('age.groups', ageGroupSchema)