const express = require('express');
const router = express.Router();
const controller = require('../../controllers/label.controller')

module.exports = () => {
    router.post('/create-label', controller.create);
    router.post('/label', controller.list);
    router.get('/label/:refid', controller.details);
    router.put('/label/:refid', controller.update);
    router.put('/delete-label/:refid', controller.delete);

    return router;
}
