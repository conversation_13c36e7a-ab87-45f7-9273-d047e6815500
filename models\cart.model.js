const mongoose = require('mongoose');

const cartSchema = new mongoose.Schema({
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers" },
    products: [{
        product: { type: mongoose.Schema.Types.ObjectId, ref: "products" },
        couponCode: { type: String },
        couponAmount: { type: Number },
        quantity: { type: Number, required: true },
        priceTotal: { type: Number, required: true },
        cart: { type: Number },
        totalExcContact: { type: Number },
        currency: { type: String, default: 'AED' },
        size: { type: mongoose.Schema.Types.ObjectId, ref: "sizes" },
        contactSize: { type: mongoose.Schema.Types.ObjectId, ref: "contact.sizes" },
        contactLens: {
            multiple: { type: Boolean },
            sphLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            sphRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            cylLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            cylRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            axisLeft: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            axisRight: { type: mongoose.Schema.Types.ObjectId, ref: "contact.lens.power" },
            multiFocal: { type: String },
        },
        contactLensHash: { type: String },
        lensId: { type: String },
        lensDetails: {
            vision: { type: String },
            prescription: { type: mongoose.Schema.Types.Mixed },
            lensType: { type: String },
            brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand" },
            photocromic: { type: String },
            index: { type: mongoose.Schema.Types.ObjectId, ref: "lens.index" },
            coating: { type: mongoose.Schema.Types.ObjectId, ref: "coating" },
            lensEnquiry: { type: mongoose.Schema.Types.ObjectId, ref: "lensEnquiries" },
        },
        fromTryCart: { type: Boolean, default: false },
        orderNo: { type: String },
        lensSum : { type: Number },
        contactLensSum: { type: Number },
    }],
    devicetoken: { type: String },
    coupons: { type: mongoose.Schema.Types.ObjectId, ref: "coupons" },
    baseTotal: { type: Number },
    couponApplied: { type: Boolean, default: false },
    couponCode: { type: String },
    savings: { type: Number },
    tryCartDeduct: { type: Number },
    loyaltyAmount : { type: Number },
    loyaltyApplied : { type: Boolean, default: false },
    loyaltyDate: { type: Date },
    couponDate: { type: Date },
    total: { type: Number },
    date: {
        added: { type: Date, default: (new Date().toISOString()) },
        purchased: { type: Date },
    },
    isPurchased: { type: Boolean, default: false },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'ae' },
    cardNumber: { type: String },
}, { timestamps: true });

module.exports = mongoose.model('carts', cartSchema)