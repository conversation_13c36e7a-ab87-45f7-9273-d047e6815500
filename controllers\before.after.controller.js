const messages = require('../config/constants/messages');
const service = require('../services/before.after.service');
const helper = require('../util/responseHelper');
const axios = require('axios');

exports.addBeforeAfter = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.create({...req.body, storeId: storeid});
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, 200, result, messages.SUCCESS);
    }
    catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}

exports.getBeforeAfter = async (req, res) => {
    
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        const appendedResult = result.map(item => {
            return {
                ...item?._doc,
                beforeImage: process.env.DOMAIN + item.beforeImage,
                afterImage: process.env.DOMAIN + item.afterImage,
            }
        })
        helper.deliverResponse(res, 200, appendedResult, messages.SUCCESS_RESPONSE.message);
    }
    catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}

exports.deleteBeforeAfter = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.update({ _id: req.params.id, storeId: storeid }, { isDelete: true });
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, 200, result, messages.SUCCESS_RESPONSE);
    }
    catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}

exports.updateBeforeAfter = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.update({ _id: req.params.id, storeId: storeid }, req.body);
        await axios.post(process.env.REVALIDATE, { tag: "home" });
        helper.deliverResponse(res, result, messages.SUCCESS);
    }
    catch (error) {
        helper.deliverResponse(res, error, messages.SERVER_ERROR);
    }
}