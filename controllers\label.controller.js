const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require("../services/label.service");
const productService = require('../services/products.service');
const generateUniqueNumber = require('../util/getRefid');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.LABELS.CREATED.error_code,
            error_message: messages.LABELS.CREATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        let query = { isDelete: false, storeId: storeid };
        if (body?.isActive) query.isActive = body?.isActive
        const response = await service.find(query);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.details = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const response = await service.update({ refid: req?.params?.refid, isDelete: false, storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.LABELS.UPDATED.error_code,
            "error_message": messages.LABELS.UPDATED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const label = await service.findOne({ refid, isDelete: false, storeId: storeid });
        const products = await productService.find({ label: label?._id, isDelete: false, storeId: storeid });
        if (products.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Label is used in products"
            });
            return;
        }
        await service.update({ refid, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.LABELS.DELETED.error_code,
            error_message: messages.LABELS.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}