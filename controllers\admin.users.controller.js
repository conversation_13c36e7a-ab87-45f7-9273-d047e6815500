const service = require("../services/admin.users.service");
const helper = require("../util/responseHelper");
const messages = require("../config/constants/messages");
const slug = require("../util/slug");
const db = require("../models/index");
const bcrypt = require("bcrypt");
const { body, validationResult } = require("express-validator");
const jwt = require("jsonwebtoken");
const { KEYS } = require("../config/constants/key");
const emailHelper = require("../util/emailHelper");
const crypto = require("crypto");
const template = require("../util/templates");

exports.validate = (method) => {
    switch (method) {
        case "create": {
            return [
                body("name", "Name is required").exists(),
                body("email", "Email is required").exists(),
                body("password", "Password is required").exists(),
                body("mobile", "Mobile is required").exists(),
            ];
        }
        case "login": {
            return [body("email", "Email is required").exists(), body("password", "Password is required").exists()];
        }
    }
};

exports.create = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }

        let { body } = req;
        const emailExist = await service.findOne({ email: body.email, isDelete: false });
        if (emailExist) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Email already exist",
            });
            return;
        }
        body.refid = generateUniqueNumber();
        if (req?.file) body.image = req?.file?.path;
        bcrypt.hash(body.password, 10, async (err, hash) => {
            body.password = hash;
            const response = await service.create(body);
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.CREATED.error_code,
                error_message: messages.ADMIN.CREATED.error_message,
            });
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getAdminUsers = async (req, res) => {
    try {
        const response = await service.find({ isDelete: false }, { password: 0 });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.adminUserdetails = async (req, res) => {
    try {
        let { refid } = req?.params;
        const response = await service.findOne({ refid: refid, isDelete: false }, { password: 0 });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.NOT_FOUND.error_code,
                error_message: messages.ADMIN.NOT_FOUND.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.findAdmin = async (req, res) => {
    try {
        const { headers } = req;
        const { authorization } = headers;
        if (!authorization) {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.ADMIN.NOT_FOUND.error_code,
                    error_message: messages.ADMIN.NOT_FOUND.error_message,
                }
            );
        }
        let token = authorization.split("Bearer ")[1];
        let decoded = jwt.verify(token, KEYS.DEV.JWTSECRET);
        const response = await service.findOne({ refid: decoded?.refid, isDelete: false }, { password: 0 });

        if (response) {
            const avatar = response?.image && process.env.DOMAIN + response?.image;
            helper.deliverResponse(
                res,
                200,
                { ...response?._doc, avatar },
                {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: messages.SUCCESS_RESPONSE.error_message,
                }
            );
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.NOT_FOUND.error_code,
                error_message: messages.ADMIN.NOT_FOUND.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.update = async (req, res) => {
    try {
        const { body } = req;
        const emailExist = await service.findOne({ email: body?.email, isDelete: false, refid: { $ne: body?.refid } });
        if (emailExist) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Email already exist",
            });
            return;
        }
        if (body?.image) delete body.image;

        if (req?.file) body.image = req?.file?.path;
        const response = await service.update({ refid: body?.refid, isDelete: false }, body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.UPDATED.error_code,
                error_message: messages.ADMIN.UPDATED.error_message,
            });
        } else {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.NOT_FOUND.error_code,
                error_message: messages.ADMIN.NOT_FOUND.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.adminLogin = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }

        let { body } = req;
        const admin = await service.findOne({
            email: body?.email,
            isDelete: false,
            isActive: true,
        });
        if (admin) {
            bcrypt.compare(body.password, admin.password, function (err, result) {
                if (result) {
                    const token = jwt.sign({ refid: admin.refid, email: admin.email }, KEYS.DEV.JWTSECRET, { expiresIn: KEYS.DEV.JWT_EXPIRE });
                    helper.deliverResponse(
                        res,
                        200,
                        {
                            accessToken: token,
                            refid: admin.refid,
                            email: admin.email,
                        },
                        {
                            error_code: messages.LOGIN_SUCCESS.error_code,
                            error_message: messages.LOGIN_SUCCESS.error_message,
                        }
                    );
                } else {
                    helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.PASSWORD_MISMATCH.error_code,
                            error_message: messages.PASSWORD_MISMATCH.error_message,
                        }
                    );
                }
            });
        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.ADMIN.NOT_FOUND.error_code,
                    error_message: messages.ADMIN.NOT_FOUND.error_message,
                }
            );
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.checkPermission = async (req, res) => {
    try {
        const { body } = req;
        const { refid } = res.locals.user;
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";

        const admin = await service.findOne({ refid: refid, isDelete: false, isActive: true });
        if (admin) {
            if (admin.role && admin.role?.isActive && !admin.role?.isDelete && admin.role?.permissions?.includes(body?.permission)) {
                if (admin.role?.stores?.includes(storeid)) {
                    return helper.deliverResponse(
                        res,
                        200,
                        {},
                        {
                            error_code: messages.SUCCESS_RESPONSE.error_code,
                            error_message: messages.SUCCESS_RESPONSE.error_message,
                        }
                    );
                } else {
                    return helper.deliverResponse(
                        res,
                        403,
                        {},
                        {
                            error_code: messages.ROLES.PERMISSION_DENIED.error_code,
                            error_message: messages.ROLES.PERMISSION_DENIED.error_message,
                        }
                    );
                }
            } else {
                return helper.deliverResponse(
                    res,
                    403,
                    {},
                    {
                        error_code: messages.ROLES.PERMISSION_DENIED.error_code,
                        error_message: messages.ROLES.PERMISSION_DENIED.error_message,
                    }
                );
            }

        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.ADMIN.NOT_FOUND.error_code,
                    error_message: messages.ADMIN.NOT_FOUND.error_message,
                }
            );
        }
    } catch (errorr) {
        console.log(errorr)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.forgotPassword = async (req, res) => {
    try {
        const { body } = req;
        const admin = await service.findOne({
            email: body?.email,
            isDelete: false,
            isActive: true,
        });
        if (!admin) {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.ADMIN.NOT_FOUND.error_code,
                    error_message: messages.ADMIN.NOT_FOUND.error_message,
                }
            );
        }

        function generateRandomAlphaString(length) {
            const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
            let result = '';
            const bytes = crypto.randomBytes(length);

            for (let i = 0; i < length; i++) {
                // Use random byte to pick a character from alphabet
                result += alphabet[bytes[i] % alphabet.length];
            }

            return result;
        }

        const resetToken = generateRandomAlphaString(20);
        const resetTokenExpire = Date.now() + 3600000; // 1 hour

        admin.resetToken = resetToken;
        admin.resetExpiry = resetTokenExpire;
        await service.update({ refid: admin.refid, isDelete: false }, admin);

        const resetUrl = `${req.headers.origin}/reset-password/${resetToken}`;

        const mailOptions = {
            from: process.env.EMAIL_USER,
            to: admin.email,
            subject: "Password Reset",
            html: await template.forgotPassword(resetUrl),
            attachments: [
                {
                    filename: "logo-light-full.png",
                    path: __dirname + "/../public/logo-black.png", // path contains the filename, do not just give path of folder where images are reciding.
                    cid: "logo", // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object. // give any unique name to the image and make sure, you do not repeat the same string in given attachment array of object.
                },
            ],
        };

        emailHelper.sendMail(mailOptions, (error, info) => {
            if (error) {
                return helper.deliverResponse(res, 422, error, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                });
            }
        });

        helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: "Password reset link sent to " + admin.email,
            }
        );
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.resetPassword = async (req, res) => {
    try {
        const { body } = req;
        const admin = await service.findOne({
            resetToken: body?.resetToken,
            resetExpiry: { $gt: Date.now() },
        });
        if (!admin) {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.ADMIN.NOT_FOUND.error_code,
                    error_message: "Password reset token is invalid or has expired",
                }
            );
        }

        admin.password = await bcrypt.hash(body?.newPassword, 10);
        admin.resetToken = undefined;
        admin.resetExpiry = undefined;
        await service.update({ refid: admin.refid, isDelete: false }, admin);
        helper.deliverResponse(
            res,
            200,
            {},
            {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: "Password reset successfully",
            }
        );
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.delete = async (req, res) => {
    try {
        const { refid } = req.params;
        const response = await service.update({ refid: refid, isDelete: false }, { isDelete: true });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.ADMIN.DELETED.error_code,
                error_message: messages.ADMIN.DELETED.error_message
            });
        } else {
            helper.deliverResponse(res, 422, response, {
                error_code: 1,
                error_message: "Something went wrong"
            });
            return
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        })
    }
}