const messages = require("../config/constants/messages");
const service = require("../services/products.service");
const productHeadService = require("../services/product.head.service");
const helper = require("../util/responseHelper");
const db = require("../models/index");
const adminService = require("../services/admin.users.service");
const slug = require("../util/slug");
const { validationResult, body } = require("express-validator");
const customerService = require("../services/customer.service");
const reviewService = require("../services/reviews.service");
const jwt = require("jsonwebtoken");
const { KEYS } = require("../config/constants/key");
const brandService = require("../services/brands.service");
const ageGroupService = require("../services/age.group.service");
const frameTypeService = require("../services/frame.types.service");
const frameShapeService = require("../services/frame.shapes.service");
const popularSearchService = require("../services/popularSearch.service");
const categoryService = require("../services/category.service");
const compareService = require("../services/compare.service");
const MiniSearch = require("minisearch");
const colorService = require("../services/color.service");
const sizeService = require("../services/size.service");
const contactSizeService = require("../services/contact.size.service");
const csv = require("csv-parser");
const fs = require("fs");
const fsAsync = require("fs").promises;
const AdmZip = require("adm-zip");
const path = require("path");
const labelService = require("../services/label.service");
const nonListService = require("../services/non.list.service");
const frontMaterialService = require("../services/front.material.service");
const typeService = require("../services/type.service");
const lensMaterialService = require("../services/lens.material.service");
const Mongoose = require("mongoose");
const StreamZip = require('node-stream-zip');
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const xmlbuilder = require("xmlbuilder");
const { uploadWebp } = require("../util/uploadWebp");
const generateUniqueNumber = require("../util/getRefid");
const Mutex = require('async-mutex').Mutex;
const mutex = new Mutex();

exports.validate = (method) => {
    switch (method) {
        case "listing": {
            return [body("page").exists().withMessage("Page is required"), body("limit").exists().withMessage("Limit is required")];
        }
        case "search": {
            return [
                body("keyword").exists().withMessage("Keyword is required"),
                body("page").exists().withMessage("Page is required"),
                body("limit").exists().withMessage("Limit is required"),
            ];
        }
        case "addToCompare": {
            return [body("product").exists().withMessage("Product is required")];
        }
        case "removeCompare": {
            return [body("product").exists().withMessage("Product is required"), body("type").exists().withMessage("Type is required")];
        }
    }
};

exports.generateSlug = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;

        const newSlug = await slug.createSlug(db.Products, body?.name, {
            slug: await slug.generateSlug(body?.name),
            storeId: storeid,
            isDelete: false
        });

        helper.deliverResponse(res, 200, { slug: newSlug }, {
            error_code: messages.PRODUCTS.CREATED.error_code,
            error_message: messages.PRODUCTS.CREATED.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};
exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const sku = await service.findOne({ sku: body?.sku, isDelete: false, storeId: storeid });
        if (sku) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.SKU_EXISTS.error_code,
                    error_message: messages.PRODUCTS.SKU_EXISTS.error_message,
                }
            );
        }

        if (body?.slug) {
            // body.slug = await slug.createSlug(db.Products, body?.name?.en, {
            //     slug: await slug.generateSlug(body?.name?.en),
            // });
            const isSlugExist = await slug.isSlugExist(db.Products, { slug: body?.slug, storeId: storeid });
            if (isSlugExist) {
                helper.deliverResponse(res, 422, {}, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Slug already exists",
                });
                return;
            }
        }

        let stock = 0;
        if (body?.stock) stock = body?.stock;
        const { email } = res?.locals?.user;

        const adminDetails = await adminService.findOne({ email, isDelete: false });
        if (!body.slug) {
            body.slug = await slug.createSlug(db.Products, body?.name?.en, {
                slug: await slug.generateSlug(body?.name?.en),
                storeId: storeid,
                isDelete: false
            });
        }

        body.refid = generateUniqueNumber();
        body.createdBy = adminDetails?._id;
        body.storeId = storeid;

        // if (body?.mainProduct) {
        //     if (body?.thumbnail?.includes(process.env.DOMAIN)) body.thumbnail = body.thumbnail.replace(process.env.DOMAIN, "");
        //     if (body?.banner?.includes(process.env.DOMAIN)) body.banner = body.banner.replace(process.env.DOMAIN, "");
        //     if (body?.images) {
        //         for (let i = 0; i < body?.images?.length; i++) {
        //             if (body?.images[i].includes(process.env.DOMAIN)) body.images[i] = body.images[i].replace(process.env.DOMAIN, "");
        //         }
        //     }
        //     if (body?.descriptionImages) {
        //         for (let i = 0; i < body?.descriptionImages?.length; i++) {
        //             if (body?.descriptionImages[i].includes(process.env.DOMAIN)) body.descriptionImages[i] = body.descriptionImages[i].replace(process.env.DOMAIN, "");
        //         }
        //     }
        // } else {
        //     if (body.banner) delete body.banner;
        //     if (body.thumbnail) delete body.thumbnail;
        //     if (body.images) delete body.images;
        //     if (body.descriptionImages) delete body.descriptionImages;
        // }


        async function uploadBanner(req, body) {
            if (req?.files?.banner) {
                const name = `products/${body?.sku}/banner/${Date.now()}-${req?.files["banner"][0]?.originalname}.webp`
                const { key } = await uploadWebp(req?.files["banner"][0]?.path, name)
                body.banner = key
            }
        }

        async function uploadThumbnail(req, body) {
            if (req?.files?.thumbnail) {
                let name = `products/${body?.sku}/thumbnail/${Date.now()}-${req?.files["thumbnail"][0]?.originalname}.webp`
                let type = 'image/webp';
                if (req?.files["thumbnail"][0]?.mimetype == 'image/gif') {
                    name = `products/${body?.sku}/thumbnail/${Date.now()}-${req?.files["thumbnail"][0]?.originalname}`;
                    type = req?.files["thumbnail"][0]?.mimetype;
                }

                const { key } = await uploadWebp(req?.files["thumbnail"][0]?.path, name, type)
                body.thumbnail = key
            }
        }

        async function uploadImages(req, body) {
            if (req?.files?.images) {
                body.images = [];
                for (let i = 0; i < req?.files?.images?.length; i++) {
                    if (req.files["images"][i]) {
                        let name = `products/${body?.sku}/images/${Date.now()}-${req?.files["images"][i]?.originalname}.webp`
                        let type = 'image/webp';
                        if (req?.files["images"][0]?.mimetype == 'image/gif') {
                            name = `products/${body?.sku}/images/${Date.now()}-${req?.files["images"][i]?.originalname}`;
                            type = req?.files["images"][i]?.mimetype;
                        }
                        const { key } = await uploadWebp(req?.files["images"][i]?.path, name, type)
                        body.images.push(key);
                    }
                }
            }
        }

        async function uploadDescriptionImages(req, body) {
            if (req?.files?.descriptionFiles) {
                body.descriptionImages = [];
                for (let i = 0; i < req?.files?.descriptionFiles?.length; i++) {
                    if (req.files["descriptionFiles"][i]) {
                        const name = `products/${body?.sku}/descriptionImages/${Date.now()}-${req?.files["descriptionFiles"][i]?.originalname}.webp`
                        const { key } = await uploadWebp(req?.files["descriptionFiles"][i]?.path, name)
                        body.descriptionImages.push(key);
                    }
                }
            }
        }

        async function uploadSeoImage(req, body) {
            if (req?.files?.ogImage) {
                const name = `products/${body?.sku}/ogImage/${Date.now()}-${req?.files["ogImage"][0]?.originalname}.webp`
                const { key } = await uploadWebp(req?.files["ogImage"][0]?.path, name)
                body.seoDetails.ogImage = key;
            }
        }
        await Promise.all([
            uploadBanner(req, body),
            uploadThumbnail(req, body),
            uploadImages(req, body),
            uploadDescriptionImages(req, body),
            uploadSeoImage(req, body)
        ])

        if (body?.plans) body.plans = body?.plans;

        body.offerPercentage =
            body?.offerPrice?.aed
                ? (body?.offerPrice?.aed < body?.price?.aed && body?.offerPrice?.aed > 0)
                    ? ((body?.price?.aed - body?.offerPrice?.aed) / body?.price?.aed) * 100
                    : 0
                : 0;

        body.stock = stock;

        if (body?.label === "null") body.label = null;
        const parent = body?.parent;
        delete body.parent;
        if (body.size == "null") body.size = null;
        body.isDefaultVariant = false;
        const response = await service.create(body);

        if (parent) {
            productHead = await db.ProductHead.findOne({ refid: parent, isDelete: false })
            if (productHead) {
                await Promise.all([
                    db.ProductHead.updateOne({ refid: parent, isDelete: false }, { $push: { products: response._id } }),
                    db.Products.updateOne({ _id: response._id }, { parent: productHead._id })
                ])
            }
        }

        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.PRODUCTS.CREATED.error_code,
                error_message: messages.PRODUCTS.CREATED.error_message,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.createDuplicate = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let { body } = req;
        const { email } = res?.locals?.user;
        const adminDetails = await adminService.findOne({ email, isDelete: false });

        body.slug = await slug.createSlug(db.Products, body?.name?.en, {
            slug: await slug.generateSlug(body?.name?.en),
        });



        body.refid = generateUniqueNumber();
        body.storeId = storeid

        const sku = await service.findOne({
            sku: body?.sku,
            isDelete: false,
            refid: { $ne: body?.refid },
            storeId: storeid
        });

        if (sku) {
            body.sku = body?.sku + "-" + body?.refid
        }


        const response = await service.create(body);

        helper.deliverResponse(res, 200, response, {
            error_code: messages.PRODUCTS.CREATED.error_code,
            error_message: messages.PRODUCTS.CREATED.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
}

exports.getProducts = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        console.log(storeid)
        storeid = storeid ? storeid : "ae"
        let { page, limit, sort } = req.query;
        sort = sort ?? { createdAt: -1 }

        let { body } = req;
        let query = { isDelete: false, storeId: storeid };
        if (body?.isDefaultVariant) query.isDefaultVariant = body?.isDefaultVariant;
        if (body?.refid) query.refid = { $ne: body?.refid };
        if (body?.isActive) query.isActive = body?.isActive;
        if (body?.productType) query.productType = body?.productType;
        if (body?.category && body?.category.length > 0) query.category = { $in: body?.category };
        if (body?.brand) query.brand = body?.brand;
        if (body?.frameType && body?.frameType.length > 0) query.frameType = { $in: body?.frameType };
        if (body?.frameShape && body?.frameShape.length > 0) query.frameShape = { $in: body?.frameShape };
        if (body?.color && body?.color.length > 0) query.color = { $in: body?.color };
        if (body?.size && body?.size.length > 0) query["sizes.size"] = { $in: body?.size };
        if (body?.frontMaterial && body?.frontMaterial.length > 0) query["frontMaterial"] = { $in: body?.frontMaterial };
        if (body?.type && body?.type.length > 0) query.type = { $in: body?.type };
        if (body?.lensMaterial && body?.lensMaterial.length > 0) query.lensMaterial = { $in: body?.lensMaterial };

        if (body?.productHead === true) query.parent = { $ne: null }
        if (body?.productHead === false) query.parent = { $eq: null }

        if (body?.keyword) {
            query["$or"] = [
                { "name.en": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "name.ar": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "sku": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { brand: { $in: await getBrandIdsByName(body?.keyword, storeid) } },
                { category: { $in: await getCategoryIdsByName(body?.keyword, storeid) } },
            ]
        }
        console.log(query["$or"])
        // console.log(query["$or"][4])
        const [response, total] = await Promise.all([
            service.find(query, {}, page, limit, sort),
            service.simpleCount(query)
        ])

        if (response) {
            helper.deliverResponse(res, 200, {
                products: response,
                total
            }, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getSunglasses = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let { page, limit, sort } = req.query;
        sort = sort ?? { createdAt: -1 }

        let { body } = req;
        let query = { isDelete: false, storeId: storeid, productType: "frame" };
        if (body?.isDefaultVariant) query.isDefaultVariant = body?.isDefaultVariant;
        if (body?.refid) query.refid = { $ne: body?.refid };
        if (body?.isActive) query.isActive = body?.isActive;
        if (body?.category && body?.category.length > 0) query.category = { $in: body?.category };
        if (body?.frameType && body?.frameType.length > 0) query.frameType = { $in: body?.frameType };
        if (body?.frameShape && body?.frameShape.length > 0) query.frameShape = { $in: body?.frameShape };
        if (body?.color && body?.color.length > 0) query.color = { $in: body?.color };
        if (body?.size && body?.size.length > 0) query["sizes.size"] = { $in: body?.size };
        if (body?.frontMaterial && body?.frontMaterial.length > 0) query["frontMaterial"] = { $in: body?.frontMaterial };
        if (body?.type && body?.type.length > 0) query.type = { $in: body?.type };
        if (body?.lensMaterial && body?.lensMaterial.length > 0) query.lensMaterial = { $in: body?.lensMaterial };

        if (body?.productHead === true) query.parent = { $ne: null }
        if (body?.productHead === false) query.parent = { $eq: null }

        if (body?.keyword) {
            query["$or"] = [
                { "name.en": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "name.ar": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "sku": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { brand: { $in: await getBrandIdsByName(body?.keyword, storeid) } },
                { category: { $in: await getCategoryIdsByName(body?.keyword, storeid) } },
            ]
        }

        const [response, total] = await Promise.all([
            service.find(query, {

            }, page, limit, sort),
            service.simpleCount(query)
        ])

        if (response) {
            helper.deliverResponse(res, 200, {
                products: response,
                total
            }, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getProductDetails = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let projection = { createdAt: 0, updatedAt: 0, __v: 0, createdBy: 0, isDelete: 0 };
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.getReviews = async (req, res) => {
    try {
        const response = await service.findOne({
            _id: req?.params?.id,
            isDelete: false,
        });
        const reviews = await reviewService.find({
            product: response?._id,
            isDelete: false,
            isApproved: true,
        });
        if (response) {
            const data = {
                product: response?._id,
                rating: response?.rating || 0,
                reviewsCount: reviews?.length || 0,
                reviews: reviews.map((review) => {
                    return {
                        message: review?.message,
                        customer: review?.customer?.name,
                        image: review?.customer?.image ? process.env.BASE_URL + review?.customer?.image : "",
                        date: review?.date,
                    };
                }),
            };
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.NO_DATA.error_code,
                    error_message: messages.NO_DATA.error_message,
                }
            );
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

// exports.getVariants = async (req, res) => {
//     try {
//         const { refid } = req.params;
//         const response = await service.findOne({
//             refid: refid,
//             isDelete: false,
//             // isDefaultVariant: true,
//         });
//         if (response) {
//             if (response?.isDefaultVariant) {
//                 if (response?.variants?.length > 0) {
//                     const variants = [];
//                     for (let variant of response?.variants) {
//                         const variantDetails = await service.findOne({
//                             _id: variant?._id,
//                             isDelete: false,
//                         });
//                         variants.push(variantDetails);
//                     }
//                     return helper.deliverResponse(res, 200, variants, {
//                         error_code: messages.SUCCESS_RESPONSE.error_code,
//                         error_message: messages.SUCCESS_RESPONSE.error_message,
//                     });
//                 } else {
//                     return helper.deliverResponse(
//                         res,
//                         422,
//                         {},
//                         {
//                             error_code: 1,
//                             error_message: "Variants not found",
//                         }
//                     );
//                 }
//             } else {
//                 const main = await service.findOne({ _id: response?.mainProduct });
//                 if (main) {
//                     if (main?.variants?.length > 0) {
//                         const variants = [];
//                         variants.push(main);
//                         for (let variant of main?.variants) {
//                             const variantDetails = await service.findOne({
//                                 _id: variant?._id,
//                                 isDelete: false,
//                             });
//                             if (variantDetails?.refid == refid) continue;
//                             variants.push(variantDetails);
//                         }
//                         return helper.deliverResponse(res, 200, variants, {
//                             error_code: messages.SUCCESS_RESPONSE.error_code,
//                             error_message: messages.SUCCESS_RESPONSE.error_message,
//                         });
//                     } else {
//                         return helper.deliverResponse(
//                             res,
//                             422,
//                             {},
//                             {
//                                 error_code: 1,
//                                 error_message: "Variants not found",
//                             }
//                         );
//                     }
//                 } else {
//                     return helper.deliverResponse(
//                         res,
//                         422,
//                         {},
//                         {
//                             error_code: messages.PRODUCTS.NOT_FOUND.error_code,
//                             error_message: messages.PRODUCTS.NOT_FOUND.error_message,
//                         }
//                     );
//                 }
//             }
//         } else {
//             helper.deliverResponse(
//                 res,
//                 422,
//                 {},
//                 {
//                     error_code: messages.PRODUCTS.NOT_FOUND.error_code,
//                     error_message: messages.PRODUCTS.NOT_FOUND.error_message,
//                 }
//             );
//         }
//     } catch (error) {
//         helper.deliverResponse(
//             res,
//             422,
//             {},
//             {
//                 error_code: messages.SERVER_ERROR.error_code,
//                 error_message: messages.SERVER_ERROR.error_message,
//             }
//         );
//     }
// };

exports.getVariants = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        const { refid } = req.params;
        const response = await service.findOne({
            refid: refid,
            isDelete: false,
            isDefaultVariant: true,
            storeId: storeid
        });
        if (response) {
            const variants = await service.find({ mainProduct: response?._id, isDefaultVariant: false, isDelete: false, storeId: storeid })

            return helper.deliverResponse(res, 200, { main: response, variants }, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });

        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addExistingVariant = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        const { refid, variantRefid } = req.body

        if (refid == variantRefid) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Varinat must be a different product from the main product",
                }
            );
        }

        const [variant, main] = await Promise.all([
            service.findOne({ refid: variantRefid, isDelete: false, storeId: storeid }),
            service.findOne({ refid: refid, isDelete: false, isDefaultVariant: true, storeId: storeid })
        ]);
        if (!variant || !main) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }

        if (variant._id?.toString() == main?._id?.toString()) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Varinat must be a different product from the main product",
                }
            );
        }

        const variants = main.variants?.map((item) => item?._id)
        if (!variants?.map(item => item?.toString())?.includes(variant?._id?.toString())) {
            variants.push(variant?._id)
        }
        console.log(variants)
        await Promise.all([
            service.update({ _id: main?._id, storeId: storeid }, { variants: variants }),
            service.update({ _id: variant?._id, storeId: storeid }, { isDefaultVariant: false, mainProduct: main?._id })
        ])

        return helper.deliverResponse(res, 200, { success: true }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};


exports.removeVariant = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        const { refid } = req.params;
        const response = await service.findOne({
            refid: refid,
            isDelete: false,
            isDefaultVariant: false,
            storeId: storeid
        });

        if (response) {
            const main = await service.findOne({ _id: response?.mainProduct, storeId: storeid });
            if (main) {
                if (main?.variants?.length > 0) {
                    await Promise.all([
                        service.update({ _id: main._id, storeId: storeid }, {
                            variants: main?.variants?.filter((item) => item?._id?.toString() != response?._id?.toString()).map(item => item?._id)
                        }),
                        service.update({ _id: response?._id, storeId: storeid }, { isDefaultVariant: true, mainProduct: response?._id })
                    ]);
                    // console.log(resp)
                    return helper.deliverResponse(res, 200, { message: "Variant removed" }, {
                        error_code: messages.SUCCESS_RESPONSE.error_code,
                        error_message: messages.SUCCESS_RESPONSE.error_message,
                    });
                } else {
                    return helper.deliverResponse(
                        res,
                        422,
                        {},
                        {
                            error_code: 1,
                            error_message: "Variants not found",
                        }
                    );
                }
            } else {
                return helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                        error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                    }
                );
            }
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let { body } = req;
        let stock = 0;
        if (body?.stock) stock = body?.stock;
        const sku = await service.findOne({
            sku: body?.sku,
            isDelete: false,
            refid: { $ne: body?.refid },
            storeId: storeid
        });
        if (sku) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.SKU_EXISTS.error_code,
                    error_message: messages.PRODUCTS.SKU_EXISTS.error_message,
                }
            );
        }

        const productDetails = await service.findOne({
            refid: body?.refid,
            isDelete: false,
            storeId: storeid
        });
        // if (body?.name?.en !== productDetails?.name?.en){
        //     body.slug = await slug.createSlug(db.Products, body?.name?.en, {
        //         slug: await slug.generateSlug(body?.name?.en),
        //     });
        // }
        if (body?.slug !== productDetails?.slug) {
            // body.slug = await slug.createSlug(db.Products, body?.name?.en, {
            //     slug: await slug.generateSlug(body?.name?.en),
            // });
            const isSlugExist = await slug.isSlugExist(db.Products, { slug: body?.slug, storeId: storeid });
            if (isSlugExist) {
                helper.deliverResponse(res, 422, {}, {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: "Slug already exists",
                });
                return;
            }
        }

        if (body?.banner) delete body.banner;
        if (body?.thumbnail) delete body.thumbnail;
        // if (body?.images) delete body.images;
        // if (body?.descriptionImages) delete body.descriptionImages;

        async function uploadImage() {
            let images = []
            if (body?.image) {
                if (typeof body?.image == "string") {
                    if (body?.image?.includes(process.env.DOMAIN)) {
                        images.push(body?.image?.replace(process.env.DOMAIN, ""))
                    } else {
                        const file = req.files["images"]?.find(file => file?.originalname == body.image)
                        let name = `products/${body?.sku}/images/${Date.now()}-${file?.originalname}.webp`
                        let type = 'image/webp';
                        if (file?.mimetype == 'image/gif') {
                            name = `products/${body?.sku}/images/${Date.now()}-${file?.originalname}`;
                            type = file?.mimetype;
                        }
                        const { key } = await uploadWebp(file?.path, name, type)
                        images.push(key);
                    }
                } else {
                    for (let image of body?.image) {
                        if (image?.includes(process.env.DOMAIN)) {
                            images.push(image?.replace(process.env.DOMAIN, ""))
                        } else {
                            const file = req.files["images"]?.find(file => file?.originalname == image)
                            let name = `products/${body?.sku}/images/${Date.now()}-${file?.originalname}.webp`
                            let type = 'image/webp';
                            if (file?.mimetype == 'image/gif') {
                                name = `products/${body?.sku}/images/${Date.now()}-${file?.originalname}`;
                                type = file?.mimetype;
                            }
                            const { key } = await uploadWebp(file?.path, name, type)
                            images.push(key);
                        }
                    }
                }
            }
            body.images = images
        }

        async function uploadBanner() {
            if (req?.files?.banner) {
                const name = `products/${body?.sku}/banner/${Date.now()}-${req?.files["banner"][0]?.originalname}.webp`
                const { key } = await uploadWebp(req?.files["banner"][0]?.path, name)
                body.banner = key;
            }
        }

        async function uploadThumbnail() {
            if (req?.files?.thumbnail) {
                let name = `products/${body?.sku}/thumbnail/${Date.now()}-${req?.files["thumbnail"][0]?.originalname}.webp`;
                let type = 'image/webp';

                if (req?.files["thumbnail"][0]?.mimetype == 'image/gif') {
                    name = `products/${body?.sku}/thumbnail/${Date.now()}-${req?.files["thumbnail"][0]?.originalname}`;
                    type = req?.files["thumbnail"][0]?.mimetype;
                }

                const { key } = await uploadWebp(req?.files["thumbnail"][0]?.path, name, type)
                body.thumbnail = key
            }
        }

        async function uploadVariantImage() {
            if (req?.files?.variantImage) {
                const name = `products/${body?.sku}/variantImage/${Date.now()}-${req?.files["variantImage"][0]?.originalname}.webp`
                const { key } = await uploadWebp(req?.files["variantImage"][0]?.path, name)
                body.variantImage = key
            }
        }


        async function uploadDescriptionImages() {
            let descriptionImages = [];
            if (body?.descriptionImages) {
                if (typeof body?.descriptionImages == "string") {
                    if (body?.descriptionImages.includes(process.env.DOMAIN)) {
                        descriptionImages.push(body?.descriptionImages?.replace(process.env.DOMAIN, ""))
                    } else {
                        const val = req.files["descriptionFiles"]?.find(file => file?.originalname == body?.descriptionImages)

                        const name = `products/${body?.sku}/descriptionImages/${Date.now()}-${val?.originalname}.webp`
                        const { key } = await uploadWebp(val?.path, name)
                        descriptionImages.push(key)
                    }
                } else {
                    for (let file of body?.descriptionImages) {
                        if (file.includes(process.env.DOMAIN)) {
                            descriptionImages.push(file?.replace(process.env.DOMAIN, ""))
                        } else {
                            console.log(file)
                            console.log(req.files["descriptionFiles"])
                            const val = req.files["descriptionFiles"]?.find(item => item?.originalname == file)
                            console.log(val)
                            const name = `products/${body?.sku}/descriptionImages/${Date.now()}-${val?.originalname}.webp`
                            const { key } = await uploadWebp(val?.path, name)
                            descriptionImages.push(key)
                        }
                    }
                }
            }
            body.descriptionImages = descriptionImages;
        }

        async function uploadSeoImage() {
            if (req?.files?.ogImage) {
                const name = `products/${body?.sku}/ogImage/${Date.now()}-${req?.files["ogImage"][0]?.originalname}.webp`
                const { key } = await uploadWebp(req?.files["ogImage"][0]?.path, name)
                body.seoDetails.ogImage = key;
            }
        }

        console.log(body)

        await Promise.all([
            uploadImage(),
            uploadBanner(),
            uploadThumbnail(),
            uploadDescriptionImages(),
            uploadVariantImage(),
            uploadSeoImage()
        ])


        if (body?.plans) body.plans = body?.plans;

        if (!body?.offerPrice?.aed || isNaN(body?.offerPrice?.aed)) body.offerPrice.aed = null;
        console.log(body.offerPrice.aed)
        body.offerPercentage =
            body?.offerPrice?.aed
                ? (body?.offerPrice?.aed < body?.price?.aed && body?.offerPrice?.aed > 0)
                    ? ((body?.price?.aed - body?.offerPrice?.aed) / body?.price?.aed) * 100
                    : 0
                : 0;

        body.stock = stock;
        if (!body.ageGroup) body.ageGroup = []
        if (!body.frontMaterial) body.frontMaterial = []
        if (!body.lensMaterial) body.lensMaterial = []
        if (!body.type) body.type = []
        if (!body.recommended) body.recommended = []
        if (!body.boughtTogether) body.boughtTogether = []
        if (!body.subCategory) body.subCategory = []
        if (body?.label == "null") body.label = null
        if (body.size == "null") body.size = null;

        const parent = body?.parent;
        delete body.parent;

        if (parent) {
            productHead = await db.ProductHead.findOne({ refid: parent, isDelete: false })
            if (productDetails?.parent?.toString() !== productHead?._id.toString()) {
                const products = productHead.products ?? [];
                let flag = false;
                for (let product of products) if (product.toString() == productDetails._id.toString()) flag = true;
                if (!flag) products.push(productDetails._id)
                await db.ProductHead.updateOne({ refid: parent, isDelete: false }, { products: products })
                body.parent = productHead._id
            }
        }

        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.PRODUCTS.UPDATED.error_code,
                error_message: messages.PRODUCTS.UPDATED.error_message,
            });
        }
    } catch (error) {
        console.log(error);
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.delete = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        const { refid } = req.params;
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true });
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.PRODUCTS.DELETED.error_code,
                error_message: messages.PRODUCTS.DELETED.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.productsList = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { authorization } = req.headers;
        let customerId = null;
        let banner = null;
        let brandOverview = {
            overview: null,
            stores: [],
        };

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }
        let { body } = req;
        let query = { isDelete: false, isActive: true, storeId: storeid };
        if (body.isVirtualTry == "1") query.isVirtualTry = true
        let sort = { createdAt: -1 };

        // if (body?.sort) {
        //     switch (body?.sort[0]) {
        //         case "0": //low to high
        //             sort = { sellingPrice: 1 };
        //             break;
        //         case "1": //high to low
        //             sort = { sellingPrice: -1 };
        //             break;
        //     }
        // } else {
        //     sort = { position: -1 };
        // }
        let label;
        if (body?.sort) {
            switch (body?.sort[0]) {
                case "0":
                    label = await db.Labels.findOne({ "name.en": "New", storeId: storeid });
                    sort = { type: "label", value: label?._id };
                    break;
                case "1":
                    label = await db.Labels.findOne({ "name.en": "Sale", storeId: storeid });
                    sort = { type: "label", value: label?._id };
                    break;
                case "2":
                    label = await db.Labels.findOne({ "name.en": "Exclusive", storeId: storeid });
                    sort = { type: "label", value: label?._id };
                    break;
                case "3": //low to high
                    sort = { type: "price", value: { lastPrice: 1 } };
                    break;
                case "4": //high to low
                    sort = { type: "price", value: { lastPrice: -1 } };
                    break;
            }
        } else {
            sort = { type: "order", value: { position: -1 } };
        }

        if (body?.category) query.category = body?.category;
        if (body?.tryHome) query.isTryCart = true;
        if (body?.brand && body?.brand.length > 0) query.brand = { $in: body?.brand?.map((item) => new Mongoose.Types.ObjectId(item)) }; // { $in: body?.brand?.map((item) => item) };
        if (body?.subBrand && body?.subBrand.length > 0) query.subBrand = { $in: body?.subBrand?.map((item) => new Mongoose.Types.ObjectId(item)) }; // { $in: body?.brand?.map((item) => item) };
        if (body?.ageGroup && body?.ageGroup.length > 0) query.ageGroup = { $in: body?.ageGroup.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.gender && body?.gender.length > 0) query.gender = { $in: body?.gender };
        if (body?.frameType && body?.frameType.length > 0) query.frameType = { $in: body?.frameType.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.frameShape && body?.frameShape.length > 0) query.frameShape = { $in: body?.frameShape.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.color && body?.color.length > 0) {
            query['color'] = { $in: body?.color.map((item) => new Mongoose.Types.ObjectId(item)) }
        }
        if (body?.size && body?.size.length > 0) query["size"] = { $in: body?.size.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.contactSize && body?.contactSize.length > 0) query["contactSize"] = { $in: body?.contactSize.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.frontMaterial && body?.frontMaterial.length > 0) query["frontMaterial"] = { $in: body?.frontMaterial.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.type && body?.type.length > 0) query.type = { $in: body?.type.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.lensType && body?.lensType.length > 0) query.lensMaterial = { $in: body?.lensType.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.subCategory && body?.subCategory.length > 0) query.subCategory = { $in: body?.subCategory.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.subChildCategory && body?.subChildCategory.length > 0) query.subChildCategory = { $in: body?.subChildCategory.map((item) => new Mongoose.Types.ObjectId(item)) };

        if (body?.priceFrom && !body?.priceTo) {
            // query["sellingPrice"] = { $gte: Number(body?.priceFrom) };
            query["$or"] = [
                {
                    $and: [
                        { "offerPrice.aed": { $exists: true } },
                        { "offerPrice.aed": { $ne: null } },
                        { "offerPrice.aed": { $gte: Number(body?.priceFrom) } },
                    ]
                },
                {
                    $and: [
                        { "price.aed": { $exists: true } },
                        { "price.aed": { $ne: null } },
                        { "price.aed": { $gte: Number(body?.priceFrom) } },
                    ]
                }
            ];
        }

        if (body?.priceTo && !body?.priceFrom) {
            // query["sellingPrice"] = { $lte: Number(body?.priceTo) };
            query["$or"] = [
                {
                    $and: [
                        { "offerPrice.aed": { $exists: true } },
                        { "offerPrice.aed": { $ne: null } },
                        { "offerPrice.aed": { $lte: Number(body?.priceTo) } },
                    ]
                },
                {
                    $and: [
                        { "price.aed": { $exists: true } },
                        { "price.aed": { $ne: null } },
                        { "price.aed": { $lte: Number(body?.priceTo) } },
                    ]
                }
            ];
        }

        if (body?.priceFrom && body?.priceTo) {
            // query["$and"] = [{ sellingPrice: { $gte: Number(body?.priceFrom) } }, { sellingPrice: { $lte: Number(body?.priceTo) } }];
            query["$and"] = [
                {
                    $or: [
                        {
                            $and: [
                                { "offerPrice.aed": { $exists: true } },
                                { "offerPrice.aed": { $ne: null } },
                                { "offerPrice.aed": { $gte: Number(body?.priceFrom) } },
                            ]
                        },
                        {
                            $and: [
                                { "price.aed": { $exists: true } },
                                { "price.aed": { $ne: null } },
                                { "price.aed": { $gte: Number(body?.priceFrom) } },
                            ]
                        },
                    ]
                },
                {
                    $or: [
                        {
                            $and: [
                                { "offerPrice.aed": { $exists: true } },
                                { "offerPrice.aed": { $ne: null } },
                                { "offerPrice.aed": { $lte: Number(body?.priceTo) } },
                            ]
                        },
                        {
                            $and: [
                                { "price.aed": { $exists: true } },
                                { "price.aed": { $ne: null } },
                                { "price.aed": { $lte: Number(body?.priceTo) } },
                            ]
                        }
                    ]
                }

            ];
        }
        let categories = null
        let brands = null
        let seoDetails = null
        let brandPage = null

        if (body?.keyword && body?.keyword != "allProducts") {
            if (!body?.brandPage) {
                categories = await categoryService.findOne({
                    slug: body?.keyword,
                    isDelete: false,
                    isActive: true,
                    storeId: storeid,
                })
                if (!categories) {
                    helper.deliverResponse(
                        res,
                        404,
                        {},
                        {
                            error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                            error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                        }
                    );
                    return;
                }
            } else {
                brands = await brandService.findOne({
                    slug: body?.keyword,
                    isDelete: false,
                    isActive: true,
                    storeId: storeid,
                });
                if (!brands) {
                    helper.deliverResponse(
                        res,
                        404,
                        {},
                        {
                            error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                            error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                        }
                    );
                    return;
                }
            }
            if (categories && !body?.brandPage) {
                query.category = categories?._id;
                banner = categories?.banner;
                seoDetails = categories?.seoDetails;
            } else if (brands && body?.brandPage) {
                query.brand = brands?._id;
                banner = brands?.banner[language];
                brandOverview = {
                    overview: brands?.overview[language],
                    stores: brands?.store?.map((store) => store?.name[language]),
                };
                seoDetails = brands?.seoDetails;
            }
        }

        // console.log(query)

        const project = {
            refid: 1,
            _id: 1,
            name: 1,
            brand: 1,
            category: 1,
            color: 1,
            slug: 1,
            upc: 1,
            showDiscountPercentage: 1,
            sizes: 1,
            offerPrice: 1,
            offerPercentage: 1,
            isWishlisted: 1,
            thumbnail: 1,
            label: 1,
            isVirtualTry: 1,
            customizable: 1,
            productType: 1,
            contactSizes: 1,
            price: 1,
            variants: 1,
        }

        async function getCustomer() {
            if (customerId) {
                return await db.Customers.findOne({ refid: customerId, isDelete: false })
            }
        }
        const [response, count, customer] = await Promise.all([
            service.pagination(query, body?.page, body?.limit, project, sort),
            service.aggregateCount(query),
            getCustomer()
        ])
        console
        if (brands && body?.brandPage) {
            const pages = []
            for (let item of brands?.page) {
                if (!item?.isActive) continue;
                if (item?.type == "banner") {
                    if (item?.bannerType == "image") {
                        pages.push({
                            ...item?._doc,
                            banner: process.env.DOMAIN + item?.banner[language]
                        })
                    } else {
                        pages.push({
                            ...item?._doc,
                            src: process.env.DOMAIN + item?.src,
                            mobileSrc: process.env.DOMAIN + item?.mobileSrc,
                        })
                    }
                }
                if (item?.type == "content") {
                    pages.push({
                        ...item?._doc,
                        content: item?.content[language]
                    })
                }
                if (item?.type == "carousel") {
                    const products = [];
                    for (let product of item?.products) {
                        if (product?.storeId != storeid) continue;
                        let isWishlisted = false;
                        if (customer) {
                            isWishlisted = customer.wishlist.includes(product?._id);
                        }
                        let price = product?.price?.aed;
                        let offerPrice = product?.offerPrice?.aed;

                        if (offerPrice < 1 || offerPrice >= price) offerPrice = null
                        // let price = product?.sizes && product?.sizes.length > 0 ? product?.sizes[0]?.price : product?.price?.aed;
                        // let offerPrice = product?.sizes && product?.sizes.length > 0 ? product?.sizes[0]?.offerPrice : product?.offerPrice?.aed || null;
                        let offerPercentage = product?.offerPercentage?.aed
                            ? Math.round(product?.offerPercentage?.aed)
                            : product?.offerPercentage
                                ? Math.round(product?.offerPercentage)
                                : null;

                        if (!offerPercentage) {
                            if (product?.price?.aed && product?.offerPrice?.aed) {
                                offerPercentage = Math.round(((product?.price?.aed - product?.offerPrice?.aed) / product?.price?.aed) * 100);
                            }
                        }
                        const finalColor = product?.color?._doc ?? product?.color
                        const brand = product?.brand
                        products.push([{
                            refid: product?.refid,
                            _id: product?._id,
                            productType: product?.productType,
                            name: product?.name[language],
                            brand: brand?.name[language],
                            brandSlug: brand?.slug,
                            category: product?.category?.map(cat => ({
                                ...(cat?._doc ?? cat),
                                name: cat?.name[language]
                            })),
                            color: {
                                ...finalColor,
                                name: finalColor?.name?.[language]
                            },
                            cashback: {
                                brand: {
                                    isEnabled: brand?.isCashbackEnabled ?? false,
                                    percentage: brand?.cashbackPercentage ?? 0,
                                },
                                category: (product?.category?.[0] && product?.category?.[0]?.isCashbackEnabled) ?
                                    { isEnabled: product?.category?.[0]?.isCashbackEnabled, percentage: product?.category?.[0]?.cashbackPercentage }
                                    : (product?.category?.[1] && product?.category?.[1]?.isCashbackEnabled) ?
                                        { isEnabled: product?.category?.[1]?.isCashbackEnabled, percentage: product?.category?.[1]?.cashbackPercentage }
                                        : (product?.category?.[2] || product?.category?.[2]?.isCashbackEnabled) ?
                                            { isEnabled: product?.category?.[2]?.isCashbackEnabled, percentage: product?.category?.[2]?.cashbackPercentage }
                                            : { isEnabled: false, percentage: 0 },
                                product: {
                                    isEnabled: product?.isCashbackEnabled,
                                    percentage: product?.cashbackPercentage
                                }
                            },
                            slug: product?.slug,
                            upc: product?.upc,
                            showDiscountPercentage: product?.showDiscountPercentage,
                            price: price,
                            offerPrice: offerPrice,
                            offerPercentage: offerPercentage && offerPercentage != 0 ? offerPercentage : null,
                            isWishlisted: isWishlisted,
                            thumbnail: process.env.DOMAIN + product?.thumbnail,
                            // images: [process.env.DOMAIN + product?.thumbnail, ...(product?.images?.map((image) => process.env.DOMAIN + image) ?? [])],
                            currency: "AED",
                            label: product?.label?.name?.[language],
                            isVirtualTry: product?.isVirtualTry,
                            isDefaultVariant: product?.isDefaultVariant
                        }])
                    }

                    pages.push({
                        ...item?._doc,
                        name: item?.name[language],
                        products: products
                    })
                }
                if (item?.type == "offerBanner") {
                    pages.push({
                        ...item?._doc,
                        banner: process.env.DOMAIN + item?.banner[language]
                    })
                }
                // return pages.push(item)
            }
            brandPage = pages
        }

        // console.log(query)

        if (response) {
            // let count = response.length;
            let pages = Math.ceil(count / body?.limit);
            let isLastPage = body?.page * body?.limit >= count;
            let nextPage = isLastPage ? null : body?.page + 1;

            let minRange = 99999;
            let maxRange = 0;

            let productObjs = {}
            // console.log(response)
            for (let [key, productHead] of response.entries()) {
                if (productHead) {
                    for (let item of productHead?.variants) {
                        if (item?.storeId != storeid) continue;
                        let isWishlisted = false;
                        if (customer) {
                            isWishlisted = customer.wishlist.includes(item?._id);
                        }

                        let price = item?.price?.aed;
                        let offerPrice = item?.offerPrice?.aed;

                        if (offerPrice < 1 || offerPrice >= price) offerPrice = null
                        // let price = item?.sizes && item?.sizes.length > 0 ? item?.sizes[0]?.price : item?.price?.aed;
                        // let offerPrice = item?.sizes && item?.sizes.length > 0 ? item?.sizes[0]?.offerPrice : item?.offerPrice?.aed || null;
                        let offerPercentage = item?.offerPercentage?.aed
                            ? Math.round(item?.offerPercentage?.aed)
                            : item?.offerPercentage
                                ? Math.round(item?.offerPercentage)
                                : null;

                        if (!offerPercentage) {
                            if (item?.price?.aed && item?.offerPrice?.aed) {
                                offerPercentage = Math.round(((item?.price?.aed - item?.offerPrice?.aed) / item?.price?.aed) * 100);
                            }
                        }

                        if (offerPrice) {
                            if (offerPrice < minRange) minRange = offerPrice;
                            if (offerPrice > maxRange) maxRange = offerPrice
                        } else {
                            if (price < minRange) minRange = price;
                            if (price > maxRange) maxRange = price
                        }
                        const finalColor = item?.color[0]
                        const brand = item?.brand[0]

                        const prod = {
                            refid: item?.refid,
                            _id: item?._id,
                            productType: item?.productType,
                            name: item?.name[language],
                            brand: brand?.name[language],
                            brandSlug: brand?.slug,
                            category: item?.category?.map(cat => ({
                                ...(cat?._doc ?? cat),
                                name: cat?.name[language]
                            })),
                            color: {
                                ...finalColor,
                                name: finalColor?.name?.[language]
                            },
                            cashback: {
                                brand: {
                                    isEnabled: brand?.isCashbackEnabled ?? false,
                                    percentage: brand?.cashbackPercentage ?? 0,
                                },
                                category: (item?.category?.[0] && item?.category?.[0]?.isCashbackEnabled) ?
                                    { isEnabled: item?.category?.[0]?.isCashbackEnabled, percentage: item?.category?.[0]?.cashbackPercentage }
                                    : (item?.category?.[1] && item?.category?.[1]?.isCashbackEnabled) ?
                                        { isEnabled: item?.category?.[1]?.isCashbackEnabled, percentage: item?.category?.[1]?.cashbackPercentage }
                                        : (item?.category?.[2] || item?.category?.[2]?.isCashbackEnabled) ?
                                            { isEnabled: item?.category?.[2]?.isCashbackEnabled, percentage: item?.category?.[2]?.cashbackPercentage }
                                            : { isEnabled: false, percentage: 0 },
                                product: {
                                    isEnabled: item?.isCashbackEnabled,
                                    percentage: item?.cashbackPercentage
                                }
                            },
                            slug: item?.slug,
                            upc: item?.upc,
                            showDiscountPercentage: item?.showDiscountPercentage,
                            price: price,
                            offerPrice: offerPrice,
                            offerPercentage: offerPercentage && offerPercentage != 0 ? offerPercentage : null,
                            isWishlisted: isWishlisted,
                            thumbnail: process.env.DOMAIN + item?.thumbnail,
                            images: [process.env.DOMAIN + item?.thumbnail, ...(item?.images?.map((image) => process.env.DOMAIN + image) ?? [])],
                            currency: "AED",
                            label: item?.labelDetails?.[0]?.name?.[language],
                            isVirtualTry: item?.isVirtualTry,
                            isDefaultVariant: item?.isDefaultVariant
                        }

                        if (productObjs[key]) {
                            productObjs[key] = {
                                ...productObjs[key],
                                [finalColor?._id?.toString()]: {
                                    ...prod
                                }
                            }
                        } else {
                            productObjs[key] = {
                                [finalColor?._id?.toString()]:
                                {
                                    ...prod
                                }
                            }
                        }
                    }
                }
            }

            // console.log(productObjs["0"])
            const data = {
                products: Object.values(productObjs),
                brand: body?.brandPage ? query.brand : null,
                minRange,
                maxRange,
                banner: banner ? process.env.DOMAIN + banner : banner,
                brandOverview: brandOverview,
                count: count,
                totalPages: pages,
                page: body?.page,
                limit: body?.limit,
                nextPage: nextPage,
                isLastPage: isLastPage,
                breadCrump: {
                    category: categories ? categories?.name?.[language] : null,
                    brand: brands ? brands?.name?.[language] : null
                },
                seoDetails: seoDetails,
                brandPage
            };
            // console.log(data)
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }

    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.productBanner = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { prevPage } = req.body;
        const slug = prevPage.url?.split("/")
        if (!slug[slug.length - 1]) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.SERVER_ERROR.error_code,
                    error_message: messages.SERVER_ERROR.error_message,
                }
            );
            return;
        }
        const categories = await categoryService.findOne({
            slug: slug[slug.length - 1],
            isDelete: false,
            isActive: true,
            storeId: storeid
        })
        helper.deliverResponse(res, 200, { ...categories?._doc, banner: process.env.DOMAIN + categories?.banner }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.countNextFilter = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "en";
        const { authorization } = req.headers;
        let customerId = null;
        let banner = null;
        let brandOverview = {
            overview: null,
            stores: [],
        };

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }
        let { body } = req;
        let query = { isDelete: false, isActive: true, storeId: storeid };
        let sort = { createdAt: -1 };

        if (body?.category) query.category = body?.category;
        if (body?.tryHome) query.isTryCart = true;
        if (body?.brand && body?.brand.length > 0) query.brand = { $in: body?.brand.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.ageGroup && body?.ageGroup.length > 0) query.ageGroup = { $in: body?.ageGroup.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.gender && body?.gender.length > 0) query.gender = { $in: body?.gender };
        if (body?.frameType && body?.frameType.length > 0) query.frameType = { $in: body?.frameType.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.frameShape && body?.frameShape.length > 0) query.frameShape = { $in: body?.frameShape.map((item) => new Mongoose.Types.ObjectId(item)) };
        // if (body?.color && body?.color.length > 0) query.color = { $in: body?.color };
        if (body?.color && body?.color.length > 0) {
            query['$or'] = [
                {
                    color: { $in: body?.color.map((item) => new Mongoose.Types.ObjectId(item)) }
                },
            ]
        }
        if (body?.size && body?.size.length > 0) query["sizes.size"] = { $in: body?.size.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.frontMaterial && body?.frontMaterial.length > 0) query["frontMaterial"] = { $in: body?.frontMaterial.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.type && body?.type.length > 0) query.type = { $in: body?.type.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.lensType && body?.lensType.length > 0) query.lensMaterial = { $in: body?.lensType.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.subCategory && body?.subCategory.length > 0) query.subCategory = { $in: body?.subCategory.map((item) => new Mongoose.Types.ObjectId(item)) };
        if (body?.subChildCategory && body?.subChildCategory.length > 0) query.subChildCategory = { $in: body?.subChildCategory.map((item) => new Mongoose.Types.ObjectId(item)) };

        if (body?.priceFrom && !body?.priceTo) query["sellingPrice"] = { $gte: Number(body?.priceFrom) };

        if (body?.priceTo && !body?.priceFrom) query["sellingPrice"] = { $lte: Number(body?.priceTo) };

        if (body?.priceFrom && body?.priceTo) {
            query["$and"] = [{ sellingPrice: { $gte: Number(body?.priceFrom) } }, { sellingPrice: { $lte: Number(body?.priceTo) } }];
        }
        if (body?.keyword) {
            let categories = null
            let brands = null
            if (!body?.brandPage) {
                categories = await categoryService.findOne({
                    slug: body?.keyword,
                    isDelete: false,
                    isActive: true,
                    storeId: storeid
                })
            } else {
                brands = await brandService.findOne({
                    slug: body?.keyword,
                    isDelete: false,
                    isActive: true,
                    storeId: storeid
                });
            }
            if (categories && !body?.brandPage) {
                query.category = categories?._id;
                banner = categories?.banner;
            } else if (brands && body?.brandPage) {
                query.brand = brands?._id;
                banner = brands?.banner[language];
                brandOverview = {
                    overview: brands?.overview[language],
                    stores: brands?.store?.map((store) => store?.name[language]),
                };
            }
        }
        // console.log()
        let count = await service.aggregateCount(query);

        helper.deliverResponse(res, 200, { count }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
}

exports.filters = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { category, brand } = req?.query;
        let categoryDetails = null;
        if (category) {
            categoryDetails = await categoryService.findOne({
                slug: category,
                isDelete: false,
                isActive: true,
                storeId: storeid
            });
        }
        let brandDetails = null
        if (brand) {
            brandDetails = await brandService.findOne({
                slug: brand,
                isDelete: false,
                isActive: true,
                storeId: storeid
            })
        }
        const gen = [
            { _id: "Men", name: "Men" },
            { _id: "Women", name: "Women" },
            { _id: "Unisex", name: "Unisex" },
            { _id: "Kids", name: "Kids" },
        ]
        let filters = {
            brand: [],
            subBrand: [],
            color: [],
            size: [],
            contactSize: [],
            category: [],
            subCategory: [],
            subChildCategory: [],
            frameType: [],
            frameShape: [],
            frontMaterial: [],
            type: [],
            lensType: [],
            ageGroup: [],
            gender: category ? category === 'contact-lenses' || category === 'contact-lens-2' || category === 'contact-lens' ? null : gen : gen,
        };

        let categoryQuery = { isDelete: false, isActive: true, isRoot: true, storeId: storeid };

        let productsQuery = { isDelete: false, isActive: true, storeId: storeid };
        if (categoryDetails) productsQuery.category = categoryDetails?._id;
        if (brandDetails) productsQuery.brand = brandDetails?._id;

        async function getCategoryFilters() {
            const project = { name: 1, parent: 1, isRoot: 1, slug: 1, isDelete: 1, isActive: 1 }
            let category;
            if (categoryDetails) {
                category = await db.Category.find({ _id: categoryDetails?._id, isActive: true, isDelete: false, isRoot: true, storeId: storeid }, project);
            } else {
                category = await db.Products.aggregate([
                    {
                        $match: productsQuery
                    },
                    {
                        $unwind: "$category"
                    },
                    {
                        $group: {
                            _id: "$category"
                        }
                    },
                    {
                        $lookup: {
                            from: "categories",
                            foreignField: "_id",
                            localField: "_id",
                            pipeline: [
                                {
                                    $project: project
                                }
                            ],
                            as: "category"
                        }
                    },
                    {
                        $unwind: "$category"
                    },
                    {
                        $replaceRoot: {
                            newRoot: "$category"
                        }
                    },
                    {
                        $match: categoryQuery
                    }
                ])
            }

            for (let item of category) filters.category.push({ name: item?.name[language] ?? item?.name?.en, _id: item?._id, isRoot: item?.isRoot, slug: item?.slug });
        }

        async function getBrandsFilters() {
            if (!brandDetails) {
                const brands = await db.Products.aggregate([
                    {
                        $match: productsQuery
                    },
                    {
                        $group: {
                            _id: "$brand"
                        }
                    },
                    {
                        $lookup: {
                            from: "brands",
                            foreignField: "_id",
                            localField: "_id",
                            pipeline: [
                                {
                                    $project: { name: 1, image: 1, position: 1 }
                                }
                            ],
                            as: "brand"
                        }
                    },
                    {
                        $unwind: "$brand"
                    },
                    {
                        $replaceRoot: {
                            newRoot: "$brand"
                        }
                    },
                    {
                        $sort: { position: -1 }
                    }
                ])
                for (let item of brands)
                    filters.brand.push({
                        name: language ? item?.name[language] : item?.name.en,
                        _id: item?._id,
                        logo: process.env.DOMAIN + item?.image,
                    });
            }
        }

        async function getSubBrandsFilters() {

            const subBrands = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$subBrand"
                    }
                },
                {
                    $lookup: {
                        from: "brands",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, image: 1, position: 1 }
                            }
                        ],
                        as: "subBrand"
                    }
                },
                {
                    $unwind: "$subBrand"
                },
                {
                    $replaceRoot: {
                        newRoot: "$subBrand"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ])
            for (let item of subBrands)
                filters.subBrand.push({
                    name: language ? item?.name[language] : item?.name.en,
                    _id: item?._id,
                    // logo: process.env.DOMAIN + item?.image,
                });

        }

        async function getFrameTypeFilters() {
            const frameTypes = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$frameType"
                    }
                },
                {
                    $lookup: {
                        from: "frame.types",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, image: 1, position: 1 }
                            }
                        ],
                        as: "frameType"
                    }
                },
                {
                    $unwind: "$frameType"
                },
                {
                    $replaceRoot: {
                        newRoot: "$frameType"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ])
            for (let item of frameTypes) {
                filters.frameType.push({
                    name: language ? item?.name[language] : item?.name.en,
                    _id: item?._id,
                    logo: process.env.DOMAIN + item?.image,
                });
            }
        }

        async function getFrameShapeFilters() {
            const frameShapes = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$frameShape"
                    }
                },
                {
                    $lookup: {
                        from: "frame.shapes",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, image: 1, position: 1 }
                            }
                        ],
                        as: "frameShape"
                    }
                },
                {
                    $unwind: "$frameShape"
                },
                {
                    $replaceRoot: {
                        newRoot: "$frameShape"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ]);
            for (let item of frameShapes) {
                filters.frameShape.push({
                    name: language ? item?.name[language] : item?.name.en,
                    _id: item?._id,
                    logo: process.env.DOMAIN + item?.image,
                });
            }
        }

        async function getColorFilter() {
            const colors = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$color"
                    }
                },
                {
                    $lookup: {
                        from: "colors",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "color"
                    }
                },
                {
                    $unwind: "$color"
                },
                {
                    $replaceRoot: {
                        newRoot: "$color"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ]);
            for (let item of colors) filters.color.push({ name: language ? item?.name[language] : item?.name.en, _id: item?._id });
        }

        async function getSizeFilter() {
            const sizes = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$size"
                    }
                },
                {
                    $lookup: {
                        from: "sizes",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "size"
                    }
                },
                {
                    $unwind: "$size"
                },
                {
                    $replaceRoot: {
                        newRoot: "$size"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ]);
            for (let item of sizes) {
                filters.size.push({ name: item.name, _id: item?._id });
            }
        }

        async function getContactSizeFilter() {
            const sizes = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $group: {
                        _id: "$contactSize"
                    }
                },
                {
                    $lookup: {
                        from: "contact.sizes",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "contactSize"
                    }
                },
                {
                    $unwind: "$contactSize"
                },
                {
                    $replaceRoot: {
                        newRoot: "$contactSize"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ]);
            for (let item of sizes) {
                filters.contactSize.push({ name: item.name, _id: item?._id });
            }
        }

        async function getSubCategoryFilters() {
            let root
            if (categoryDetails) {
                root = { $in: [categoryDetails?._id] };
            } else {
                root = { $in: filters?.category?.map(item => item?._id) };
            }
            const subCategory = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $unwind: "$subCategory"
                },
                {
                    $group: {
                        _id: "$subCategory"
                    }
                },
                {
                    $lookup: {
                        from: "categories",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, parent: 1, root: 1 }
                            }
                        ],
                        as: "subCategory"
                    }
                },
                {
                    $unwind: "$subCategory"
                },
                {
                    $replaceRoot: {
                        newRoot: "$subCategory"
                    }
                },
                {
                    $match: {
                        root: root
                    }
                }
            ]);
            for (let item of subCategory) {
                filters.subCategory.push({ name: item?.name[language] ?? item?.name?.en, _id: item?._id, parent: item?.parent })
            }
        }

        // async function getSubChildCategoryFilters() {
        //     const subChildCategory = await categoryService.find(subChildCategoryQuery, { name: 1, parent: 1 });
        //     for (let item of subChildCategory) filters.subChildCategory.push({ name: item?.name[language] ?? item?.name?.en, _id: item?._id, parent: item?.parent });
        // }

        async function getFrontMeterialFilters() {
            const frontMaterials = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $unwind: "$frontMaterial"
                },
                {
                    $group: {
                        _id: "$frontMaterial"
                    }
                },
                {
                    $lookup: {
                        from: "front.materials",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "frontMaterial"
                    }
                },
                {
                    $unwind: "$frontMaterial"
                },
                {
                    $replaceRoot: {
                        newRoot: "$frontMaterial"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ])
            for (let item of frontMaterials) filters.frontMaterial.push({ name: language ? item?.name[language] : item?.name.en, _id: item?._id });
            filters.frontMaterial = filters.frontMaterial?.sort((a) => a?.name === 'All' ? -1 : 0)
        }

        async function getTypeFilter() {
            const types = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $unwind: "$type"
                },
                {
                    $group: {
                        _id: "$type"
                    }
                },
                {
                    $lookup: {
                        from: "types",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "type"
                    }
                },
                {
                    $unwind: "$type"
                },
                {
                    $replaceRoot: {
                        newRoot: "$type"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ])
            for (let item of types) filters.type.push({ name: language ? item?.name[language] : item?.name.en, _id: item?._id });
            filters.type = filters.type?.sort((a) => a?.name === 'All' ? -1 : 0)
        }

        async function getLensTypeFilters() {
            const lensTypes = await db.Products.aggregate([
                {
                    $match: productsQuery
                },
                {
                    $unwind: "$lensMaterial"
                },
                {
                    $group: {
                        _id: "$lensMaterial"
                    }
                },
                {
                    $lookup: {
                        from: "lens.materials",
                        foreignField: "_id",
                        localField: "_id",
                        pipeline: [
                            {
                                $project: { name: 1, position: 1 }
                            }
                        ],
                        as: "lensMaterial"
                    }
                },
                {
                    $unwind: "$lensMaterial"
                },
                {
                    $replaceRoot: {
                        newRoot: "$lensMaterial"
                    }
                },
                {
                    $sort: { position: -1 }
                }
            ])
            for (let item of lensTypes) filters.lensType.push({ name: language ? item?.name[language] : item?.name.en, _id: item?._id });
            filters.lensType = filters.lensType?.sort((a) => a?.name === 'All' ? -1 : 0)
        }

        async function getAllCategories() {
            await getCategoryFilters()
            await getSubCategoryFilters()
        }

        async function getAllOtherFilters() {
            const [priceAggregation] = await Promise.all([
                service.aggreagte([
                    { $match: { isDelete: false, isActive: true, storeId: storeid } },
                    {
                        $group: {
                            _id: null,
                            minPrice: { $min: "$sellingPrice" },
                            maxPrice: { $max: "$sellingPrice" },
                        },
                    },
                ]),
                getFrameTypeFilters(),
                getFrameShapeFilters(),
                getColorFilter(),
                getSizeFilter(),
                getContactSizeFilter(),
                getSubBrandsFilters(),
                // getCategoryFilters(),
                // getSubChildCategoryFilters(),
                getFrontMeterialFilters(),
                getTypeFilter(),
                getLensTypeFilters(),
                getBrandsFilters()
            ])
            return priceAggregation
        }

        const [priceAggregation] = await Promise.all([
            getAllOtherFilters(),
            getAllCategories()
        ])

        const minPrice = priceAggregation[0]?.minPrice || 0;
        const maxPrice = priceAggregation[0]?.maxPrice || 0;

        filters.priceRange = { minPrice, maxPrice };

        if (filters) {
            if (category === "accessories") {
                const { brand, frontMaterial, ...others } = filters
                helper.deliverResponse(res, 200, others, {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: messages.SUCCESS_RESPONSE.error_message,
                });
                return;
            }
            helper.deliverResponse(res, 200, filters, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.log(error);
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.productDetail = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { authorization } = req.headers;
        let customerId = null;
        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }

        let projection = {
            isDelete: 0,
            isActive: 0,
            __v: 0,
            createdAt: 0,
            updatedAt: 0,
            ageGroup: 0,
            // gender: 0,
        };
        const response = await service.findOne({ slug: req?.params?.slug, isDelete: false, storeId: storeid }, projection);
        const sizePopulate = response?.productType == "frame" ? { path: "size", select: "name position" } : { path: "contactSize", select: "name position" }
        const productHead = await db.ProductHead.findById(response?.parent).populate(
            {
                path: "products", select: "size contactSize color slug productType", populate: [
                    sizePopulate,
                    { path: "color", select: "name color" }
                ]
            }
        )

        console.log(response?.parent)

        let combinations = {};
        let colors = [];
        let sizes = [];
        if (productHead) {
            for (let [key, product] of productHead?.products?.entries()) {
                let productSize = product?.productType == "frame" ? product?.size : product?.contactSize;
                let productColor = product?.color;
                if (!productSize) productSize = { _id: "all", name: "All" };
                if (!productColor) productColor = { _id: "all", name: "All", _doc: { _id: "all", name: "All" } };
                if (combinations[productColor?._id?.toString()]) {
                    if (!combinations[productColor?._id?.toString()][productSize?._id?.toString()]) {
                        combinations[productColor?._id?.toString()] = {
                            ...combinations[productColor?._id?.toString()],
                            [productSize?._id?.toString()]: product?.slug
                        }
                    }
                } else {
                    if (productSize?._id) {
                        combinations[productColor?._id?.toString()] = {
                            [productSize?._id?.toString()]: product?.slug
                        }
                    } else {
                        combinations[productColor?._id?.toString()] = {
                            [0]: product?.slug
                        }
                    }
                }
                let color = colors.findIndex(color => color?._id?.toString() == productColor?._id?.toString())
                if (color == -1) {
                    colors.push({
                        ...productColor?._doc,
                        name: productColor?.name?.[language]
                    })
                }

                let size = sizes.findIndex(size => size?._id?.toString() == productSize?._id?.toString())
                if (size == -1) {
                    if (productSize) {
                        sizes.push(productSize)
                    }
                }

            }
        } else {
            const productSize = response?.productType == "frame" ? response?.size : response?.contactSize;
            const color = response?.color;
            colors.push(color ? {
                ...color?._doc,
                name: color?.name?.[language]
            } : 0)

            productSize && sizes.push(productSize)
            combinations[color ? color?._id?.toString() : 0] = {
                [productSize ? productSize?._id?.toString() : 0]: response?.slug
            }
        }

        // console.log(productHead)
        if (response) {
            let isWishlisted = false;
            let boughtTogether = [];
            let recommendedProducts = [];

            if (customerId) {
                const customer = await db.Customers.findOne({
                    refid: customerId,
                    isDelete: false,
                });
                if (customer) {
                    isWishlisted = customer.wishlist.includes(response?._id);
                }
            }

            const boughtTogetherPromises = [];

            if (response?.boughtTogether.length > 0) {
                for (let product of response?.boughtTogether) {
                    boughtTogetherPromises.push((async () => {
                        const productDetail = await service.findOne({
                            _id: product,
                            isDelete: false,
                            isActive: true,
                            storeId: storeid
                        });
                        if (productDetail) {
                            boughtTogether.push({
                                _id: productDetail?._id,
                                name: productDetail?.name[language] ? productDetail?.name[language] : productDetail?.name["en"],
                                slug: productDetail?.slug,
                                price: productDetail?.offerPrice?.aed > 0 && productDetail?.offerPrice?.aed < productDetail?.price?.aed
                                    ? productDetail?.offerPrice?.aed
                                    : productDetail?.price?.aed,
                                thumbnail: productDetail?.thumbnail ? process.env.DOMAIN + productDetail?.thumbnail : null,
                                color: productDetail?.color?.name[language],
                                sph: productDetail?.sph,
                                cyl: productDetail?.cyl,
                                axis: productDetail?.axis,
                                isAddToCart: productDetail?.isAddToCart,
                                productType: productDetail?.productType
                            });
                        }
                    })())



                }
            }

            await Promise.all(boughtTogetherPromises)

            const recommendedPromises = [];

            if (response?.recommendedProducts.length > 0) {
                for (let product of response?.recommendedProducts) {
                    recommendedPromises.push((async () => {
                        const productDetail = await service.findOne({
                            _id: product,
                            isDelete: false,
                            isActive: true,
                            storeId: storeid
                        });

                        if (productDetail) {
                            let offerPercentage = null

                            if (!offerPercentage) {
                                if (productDetail?.price?.aed && productDetail?.offerPrice?.aed) {
                                    offerPercentage = Math.round(((productDetail?.price?.aed - productDetail?.offerPrice?.aed) / productDetail?.price?.aed) * 100);
                                }
                            }
                            const price = productDetail?.price?.aed;

                            let offerPrice = productDetail?.offerPrice?.aed

                            if (offerPrice < 1 || offerPrice >= price) offerPrice = null

                            recommendedProducts.push({
                                '0': {
                                    _id: productDetail?._id,
                                    showDiscountPercentage: productDetail?.showDiscountPercentage,
                                    brand: productDetail?.brand?.name[language] ? productDetail?.brand?.name[language] : productDetail?.brand?.name["en"],
                                    name: productDetail?.name[language] ? productDetail?.name[language] : productDetail?.name["en"],
                                    slug: productDetail?.slug,
                                    price: price,
                                    thumbnail: productDetail?.thumbnail ? process.env.DOMAIN + productDetail?.thumbnail : null,
                                    offerPrice: offerPrice,
                                    offerPercentage: offerPercentage,
                                    isWishlisted: false,
                                    color: {
                                        ...productDetail?.color?._doc,
                                        name: productDetail?.color?.name[language]
                                    },
                                }
                            });
                        }
                    })())
                }
            }
            await Promise.all(recommendedPromises)

            const data = {
                ...response?._doc,
                label: response?.label?.name?.[language],
                frameShape: response?.frameShape?.name?.[language],
                frameType: response?.frameType?.name?.[language],
                color: {
                    ...response?.color?._doc,
                    name: response?.color?.name?.[language]
                },
                frontMaterial: response?.frontMaterial?.filter(item => item?.name?.en !== "All").map(item => item?.name?.[language]),
                category: response?.category?.map((item) => ({
                    ...item?._doc,
                    name: item?.name[language]
                })),
                name: response?.name[language] ? response?.name[language] : response?.name["en"],
                description: response?.description[language] ? response?.description[language] : response?.description["en"],
                descriptionTwo:
                    response?.descriptionTwo[language] && response?.descriptionTwo[language] !== "<p>null</p>"
                        ? response?.descriptionTwo[language]
                        : response?.descriptionTwo["en"] && response?.descriptionTwo["en"] !== "<p>null</p>"
                            ? response?.descriptionTwo["en"]
                            : "",
                brand: response?.brand?.name[language] ? response?.brand?.name[language] : response?.brand?.name["en"],
                brandSlug: response?.brand?.slug,
                cashback: {
                    brand: {
                        isEnabled: response?.brand?.isCashbackEnabled ?? false,
                        percentage: response?.brand?.cashbackPercentage ?? 0
                    },
                    category: (response?.category?.[0] && response?.category?.[0]?.isCashbackEnabled) ?
                        { isEnabled: response?.category?.[0]?.isCashbackEnabled, percentage: response?.category?.[0]?.cashbackPercentage }
                        : (response?.category?.[1] && response?.category?.[1]?.isCashbackEnabled) ?
                            { isEnabled: response?.category?.[1]?.isCashbackEnabled, percentage: response?.category?.[1]?.cashbackPercentage }
                            : (response?.category?.[2] && response?.category?.[2]?.isCashbackEnabled) ?
                                { isEnabled: response?.category?.[2]?.isCashbackEnabled, percentage: response?.category?.[2]?.cashbackPercentage }
                                : { isEnabled: false, percentage: 0 },
                    product: {
                        isEnabled: response?.isCashbackEnabled,
                        percentage: response?.cashbackPercentage
                    }
                },
                //reviewCount: reviews?.length,
                // reviews: reviews ? reviews : [],
                isWishlisted: isWishlisted,
                isTaxIncluded: response?.isTaxIncluded,
                boughtTogether: boughtTogether,
                banner: process.env.DOMAIN + response?.banner,
                thumbnail: process.env.DOMAIN + response?.thumbnail,
                video: response?.video ? process.env.DOMAIN + response?.video : null,
                images:
                    response?.images?.length > 0
                        ? response?.images.map((item) => {
                            return process.env.DOMAIN + item;
                        })
                        : [],
                descriptionImages:
                    response?.descriptionImages?.length > 0
                        ? response?.descriptionImages.map((item) => {
                            return process.env.DOMAIN + item;
                        })
                        : [],
                technicalInfo:
                    response?.technicalInfo.length > 0
                        ? response.technicalInfo.map((item) => {
                            return {
                                title: item?.title[language] ? item?.title[language] : item?.title["en"],
                                description: item?.description[language] ? item?.description[language] : item?.description["en"],
                            };
                        })
                        : [],
                recommendedProducts: recommendedProducts,
                gender: response?.gender,
                sphValues: response?.sphValues || [],
                cylValues: response?.cylValues || [],
                axisValues: response?.axisValues || [],
                addValues: response?.addValues || [],
                combinations,
                sizes,
                colors,
                size: response?.productType == "frame" ? response?.size : response?.contactSize,
                variantDisplayType: response?.variantDisplayType,
                variantImage: process.env.DOMAIN + response?.variantImage,
            };
            console.log(data.cashback)
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                404,
                {},
                {
                    error_code: messages.PRODUCTS.NOT_FOUND.error_code,
                    error_message: messages.PRODUCTS.NOT_FOUND.error_message,
                }
            );
        }
    } catch (error) {
        console.log("error caught in product detail :: ", error);
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.manageWishlist = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const translation = await db.Texts.findOne({ storeId: storeid })

        const { customerId } = res?.locals.user;
        const { body } = req;
        const customerDetails = await customerService.findOne({
            refid: customerId,
            isDelete: false,
        });
        const productDetail = await service.findOne({
            _id: body?.product,
            isDelete: false,
            storeId: storeid
        });
        console.log({
            _id: body?.product,
            isDelete: false,
            storeId: storeid
        })
        let products = [];
        for (let product of customerDetails?.wishlist) products.push(product?._id?.toString());
        if (customerDetails?.wishlist?.map((item) => item?._id?.toString()).includes(productDetail?._id?.toString())) {
            products = products.filter((item) => item != productDetail?._id);
            const result = await customerService.update({ refid: customerId }, { wishlist: products });
            if (result) await service.update({ _id: body?.product, storeId: storeid }, { likeCount: productDetail?.likeCount - 1 });
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.WISHLIST.REMOVE.error_code,
                    // error_message: messages.WISHLIST.REMOVE.error_message,
                    error_message: translation?.popup?.wishlistRemove[language],
                }
            );
        } else {
            products.push(productDetail?._id.toString());
            console.log("productDetail", productDetail)
            const result = await customerService.update({ refid: customerId }, { wishlist: products });
            if (result) await service.update({ _id: body?.product, storeId: storeid }, { likeCount: productDetail?.likeCount + 1 });
            helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.WISHLIST.ADD.error_code,
                    // error_message: messages.WISHLIST.ADD.error_message,
                    error_message: translation?.popup?.wishlistAdd[language],
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.search = async (req, res) => {
    try {
        const { body } = req;
        let { language, storeid } = req?.headers;
        const { authorization } = req.headers;

        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";

        let customerId = null;

        if (authorization) {
            let token = authorization.split("Bearer ")[1];
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) userid = null;
                customerId = decoded?.customerId;
            });
        }

        if (body?.keyword) {
            const words = body?.keyword.split(" ")
            body.keyword = body.keyword.trim();
            const nonList = await nonListService.find({
                $or: [
                    { "name.en": { $regex: "^" + body?.keyword + "$", $options: "i" } },
                    { "name.ar": { $regex: "^" + body?.keyword + "$", $options: "i" } },
                ],
                isDelete: false,
                isActive: true,
                storeId: storeid
            });
            if (nonList.length > 0) {
                helper.deliverResponse(
                    res,
                    200,
                    { notListed: true },
                    {
                        error_code: messages.SUCCESS_RESPONSE.error_code,
                        error_message: messages.SUCCESS_RESPONSE.error_message,
                    }
                );
                return;
            }

            const projection = {
                isDelete: 0,
                __v: 0,
                createdAt: 0,
                updatedAt: 0,
            };

            const or = [
                { $and: words.map(word => ({ "name.en": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "name.ar": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "description.en": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "description.ar": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "sku": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { brand: { $in: await getBrandIdsByName(body?.keyword, storeid) } },
                { category: { $in: await getCategoryIdsByName(body?.keyword, storeid) } },
                { variants: { $in: await getProductIdsByName(body?.keyword, storeid) } }
            ]
            const query = {
                isDelete: false,
                isActive: true,
                isDefaultVariant: true,
                storeId: storeid,
                $or: or,
            };

            const brandQuery = {
                isDelete: false,
                isActive: true,
                storeId: storeid,
                $or: [
                    { "name.en": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                    { "name.ar": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                ],
            }

            const [products, brands, count] = await Promise.all([
                service.pagination(query, body?.page, body?.limit, projection, { type: "order", value: {} }),
                db.Brands.find(brandQuery, { name: 1, slug: 1, image: 1 }),
                service.aggregateCount(query)
            ])

            if (products || brands) {

                let brandData = [];
                let productObjs = {}
                let customer = null
                if (customerId) {
                    customer = await db.Customers.findOne({
                        refid: customerId,
                        isDelete: false,
                    });
                }
                if (products) {
                    for (let [key, productHead] of products.entries()) {
                        if (productHead) {
                            for (let item of productHead?.variants) {
                                let isWishlisted = false;
                                if (customer) {
                                    isWishlisted = customer.wishlist.includes(item?._id);
                                }

                                let price = item?.price?.aed;
                                let offerPrice = item?.offerPrice?.aed
                                // let price = item?.sizes && item?.sizes.length > 0 ? item?.sizes[0]?.price : item?.price?.aed;
                                // let offerPrice = item?.sizes && item?.sizes.length > 0 ? item?.sizes[0]?.offerPrice : item?.offerPrice?.aed || null;
                                if (offerPrice < 1 || offerPrice >= price) offerPrice = null;

                                let offerPercentage = item?.customizable
                                    ? Math.round(item?.sizes[0]?.offerPercentage)
                                    : item?.offerPercentage?.aed
                                        ? Math.round(item?.offerPercentage?.aed)
                                        : item?.offerPercentage
                                            ? Math.round(item?.offerPercentage)
                                            : null;

                                if (!offerPercentage) {
                                    if (item?.price?.aed && item?.offerPrice?.aed) {
                                        offerPercentage = Math.round(((item?.price?.aed - item?.offerPrice?.aed) / item?.price?.aed) * 100);
                                    }
                                }
                                const finalColor = item?.color?.[0]
                                const prod = {
                                    refid: item?.refid,
                                    _id: item?._id,
                                    name: item?.name[language],
                                    brand: item?.brand?.[0]?.name?.[language],
                                    category: item?.category?.map(cat => ({
                                        ...(cat?._doc ?? cat),
                                        name: cat?.name[language]
                                    })),
                                    slug: item?.slug,
                                    color: {
                                        ...finalColor,
                                        name: item?.color?.[0]?.name[language]
                                    },
                                    price: price,
                                    showDiscountPercentage: item?.showDiscountPercentage,
                                    offerPrice: offerPrice && offerPrice == price ? null : offerPrice,
                                    offerPercentage: offerPercentage && offerPercentage != 0 ? offerPercentage : null,
                                    isWishlisted: isWishlisted,
                                    thumbnail: process.env.DOMAIN + item?.thumbnail,
                                    isBestSeller: item?.isBestSeller,
                                    isExclusive: item?.isExclusive,
                                    label: item?.labelDetails?.[0]?.name?.[language],
                                    isVirtualTry: item?.isVirtualTry,
                                    isDefaultVariant: item?.isDefaultVariant
                                }
                                if (productObjs[key]) {
                                    productObjs[key] = {
                                        ...productObjs[key],
                                        [finalColor?._id?.toString()]: {
                                            ...prod
                                        }
                                    }
                                } else {
                                    productObjs[key] = {
                                        [finalColor?._id?.toString()]:
                                        {
                                            ...prod
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (brands) {
                    for (let brand of brands) {
                        brandData.push({
                            name: brand?.name[language],
                            slug: brand?.slug,
                            image: process.env.DOMAIN + brand?.image,
                        })
                    }
                }

                pages = Math.ceil(count / body?.limit);
                isLastPage = body?.page * body?.limit >= count;
                nextPage = isLastPage ? null : body?.page + 1;

                const data = {
                    products: Object.values(productObjs),
                    brands: brandData,
                    count: count,
                    totalPages: pages,
                    page: body?.page,
                    limit: body?.limit,
                    nextPage: nextPage,
                    isLastPage: isLastPage,
                };

                helper.deliverResponse(res, 200, data, {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: messages.SUCCESS_RESPONSE.error_message,
                });
            } else {
                console.log("error")
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: messages.SERVER_ERROR.error_code,
                        error_message: messages.SERVER_ERROR.error_message,
                    }
                );
            }
        } else {
            const data = {
                products: [],
                brands: [],
                count: 0,
                totalPages: 0,
                page: body?.page,
                limit: body?.limit,
                nextPage: 0,
                isLastPage: true,
            };
            helper.deliverResponse(res, 200, data, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

async function getBrandIdsByName(keyword, storeId) {
    try {
        const brands = await db.Brands.find({
            storeId,
            $or: [{ "name.en": { $regex: ".*" + keyword + ".*", $options: "i" } }, { "name.ar": { $regex: ".*" + keyword + ".*", $options: "i" } }],
        });
        if (brands.length == 0) return [];
        return brands.map((brand) => brand._id);
    } catch (error) {
        console.error("Error while searching for brands:", error);
        return [];
    }
}

async function getCategoryIdsByName(keyword, storeId) {
    try {
        const categories = await db.Category.find({
            storeId,
            $or: [{ "name.en": { $regex: ".*" + keyword + ".*", $options: "i" } }, { "name.ar": { $regex: ".*" + keyword + ".*", $options: "i" } }],
        });
        if (categories.length == 0) return []
        return categories.map((category) => category._id);
    } catch (error) {
        console.error("Error while searching for categories:", error);
        return [];
    }
}

async function getProductIdsByName(keyword, storeId) {
    try {
        const words = keyword.split(" ")
        const Products = await db.Products.find({
            isDelete: false,
            isActive: true,
            storeId,
            $or: [
                { $and: words.map(word => ({ "name.en": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "name.ar": { $regex: ".*" + word + ".*", $options: "i" } })) },
            ],
        });
        return Products.map((category) => category._id);
    } catch (error) {
        console.error("Error while searching for Products:", error);
        return [];
    }
}

exports.suggestions = async (req, res) => {
    try {
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        let category = [];
        let searches = [];
        const categories = await categoryService.find({ storeId: storeid, isDelete: false, isActive: true, isRoot: true, topCategory: true }, {}, 3);
        // const popularSearch = await popularSearchService.find({ isDelete: false });
        const popularSearch = await db.GeneralSettings.findOne({ isDelete: false, storeId: storeid }).select("popularSearch")

        for (let item of categories) {
            category.push({
                _id: item?._id,
                name: item?.name[language],
                slug: item?.slug,
                image: process.env.DOMAIN + item?.image,
            });
        }
        // const sortedKeywords = popularSearch[0]?.keywords.sort((a, b) => b.count - a.count);
        // const limitedKeywords = sortedKeywords.slice(0, 10);
        // const resultKeywords = [];

        // for (let item of limitedKeywords) {
        //     const projection = {
        //         isDelete: 0,
        //         isActive: 0,
        //         __v: 0,
        //         createdAt: 0,
        //         updatedAt: 0,
        //     };
        //     const query = {
        //         isDelete: false,
        //         isActive: true,
        //         $or: [
        //             { "name.en": { $regex: ".*" + item?.key + ".*", $options: "i" } },
        //             { "name.ar": { $regex: ".*" + item?.key + ".*", $options: "i" } },
        //             { brand: { $in: await getBrandIdsByName(item?.key) } },
        //             { category: { $in: await getCategoryIdsByName(item?.key) } },
        //         ],
        //     };

        //     const products = await service.pagination(query, 1, 10, projection);
        //     if(products?.length > 0) resultKeywords.push(item)
        // }
        for (let item of popularSearch.popularSearch) {
            searches.push({
                key: item,
            });
        }
        const response = { categories: category, popularSearches: searches };
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

function appendKeywords(fullText, keyword, initialLength = 1) {
    const wordsArray = fullText.split(" ")
    const words = wordsArray?.length < initialLength ? initialLength : wordsArray?.length;
    let text = "";
    for (let i = initialLength; i <= words; i++) {
        text = wordsArray.slice(0, i).join(" ")
        if (text?.toLowerCase()?.includes(keyword)) break;
    }
    return text
}

exports.autocomplete = async (req, res) => {
    try {
        const { keyword } = req?.query;
        const { language, storeid } = req?.headers;
        const words = keyword.split(" ")
        const projection = {
            name: 1,
            brand: 1,
            category: 1,
        };
        const [brand, category] = await Promise.all([
            getBrandIdsByName(keyword, storeid),
            getCategoryIdsByName(keyword, storeid),
        ]);
        const query = {
            isDelete: false,
            isActive: true,
            storeId: storeid,
            $or: [
                { $and: words.map(word => ({ "name.en": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "name.ar": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "description.en": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "description.ar": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { $and: words.map(word => ({ "sku": { $regex: ".*" + word + ".*", $options: "i" } })) },
                { brand: { $in: brand } },
                { category: { $in: category } },
            ],
        };

        const products = await service.findLean(query);
        const suggestions = [];
        const length = keyword.split(" ").length + 1;
        const text = keyword.toLowerCase()
        for (let product of products) {
            // if (product?.name?.en?.toLowerCase()?.includes(text)) {
            //     const name = appendKeywords(product?.name?.en, text, length)
            //     const nameWords = name.split(" ")
            //     for (i in [...Array(name.length).keys()]) {
            //         if (i >= (length - 2)) {
            //             const str = nameWords.slice(0, i + 1).join(" ")
            //             if (!suggestions.includes(str) && str.toLowerCase().includes(text)) suggestions.push(str);
            //         }
            //     }
            // }

            suggestions.push(product?.name?.en)

            // if(product?.name?.ar?.includes(text)){
            //     suggestions.push(product.name.ar.split(" ").slice(0, length).join(" "))
            // }
            // if(product?.brand?.name?.en?.includes(text)){
            //     suggestions.push(product.brand.name.en.split(" ").slice(0, length).join(" "))
            // }
            // if(product?.brand?.name?.ar?.includes(text)){
            //     suggestions.push(product.brand.name.ar.split(" ").slice(0, length).join(" "))
            // }
            // if(product?.category?.name?.en?.includes(text)){
            //     suggestions.push(product.category.name.en.split(" ").slice(0, length).join(" "))
            // }
            // if(product?.category?.name?.ar?.includes(text)){
            //     suggestions.push(product.category.name.ar.split(" ").slice(0, length).join(" "))
            // }
        }

        helper.deliverResponse(res, 200, suggestions, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.addToCompare = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
            return;
        }
        const { devicetoken } = req?.headers;
        let { language, storeid } = req?.headers;
        language = language ?? "en"
        storeid = storeid ?? "ae"
        const { body } = req;
        if (!devicetoken) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.VALIDATION_ERROR.error_code,
                    error_message: messages.VALIDATION_ERROR.error_message,
                }
            );
            return;
        }
        const productDetail = await service.findOne({
            _id: body?.product,
            isDelete: false,
            isActive: true,
            storeId: storeid
        });
        const compareUser = await compareService.findOne({
            deviceToken: devicetoken,
            storeId: storeid
        });
        if (compareUser) {
            if (compareUser?.products?.length >= 4) {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: language == "en" ? "You can only add 4 products to compare" : "يمكنك فقط إضافة 4 منتجات للمقارنة",
                    }
                );
                return;
            }
            if (compareUser.products.includes(body?.product)) {
                helper.deliverResponse(
                    res,
                    422,
                    {},
                    {
                        error_code: 1,
                        error_message: language == "en" ? "Product already added to compare" : "تم إضافة المنتج بالفعل للمقارنة",
                    }
                );
                return;
            }
            const response = await compareService.update({ deviceToken: compareUser.deviceToken, storeId: storeid }, { $push: { products: productDetail?._id } });
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: language == "en" ? "Product added successfully" : "تمت إضافة المنتج بنجاح",
            });
        } else {
            const refid = (await compareService.count({})) + 1;
            const response = await compareService.create({
                deviceToken: devicetoken,
                products: body?.product,
                refid: refid,
                storeId: storeid
            });
            helper.deliverResponse(res, 200, response, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: language == "en" ? "Product added successfully" : "تمت إضافة المنتج بنجاح",
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.compare = async (req, res) => {
    try {
        const { devicetoken } = req?.headers;
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        if (!devicetoken) {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.VALIDATION_ERROR.error_code,
                    error_message: messages.VALIDATION_ERROR.error_message,
                }
            );
            return;
        }
        const compareUser = await compareService.findOne({
            deviceToken: devicetoken,
            storeId: storeid
        });
        if (compareUser) {
            const response = await service.find({
                _id: { $in: compareUser?.products },
                storeId: storeid
            });
            let productData = [];
            for (let item of response) {
                let sizes = [];
                const productHead = await db.ProductHead.findById(item?.parent, { products: 1 });
                if (productHead) {
                    const variants = await db.Products.find({ _id: { $in: productHead?.products } }, { size: 1, color: 1 }).populate("size");
                    if (variants?.length > 0) {
                        for (let variant of variants) {
                            if ((variant?.size?.name && !sizes.includes(variant?.size?.name) && item?.color?._id?.toString() === variant?.color?.toString())) {
                                sizes.push(variant?.size?.name);
                            }
                        }
                    }
                }

                productData.push({
                    ...item?._doc,
                    name: item?.name[language],
                    frameShape: {
                        ...item?.frameShape,
                        name: item?.frameShape?.name[language],
                    },
                    frameType: {
                        ...item?.frameType,
                        name: item?.frameType?.name[language],
                    },
                    frontMaterial: item?.frontMaterial?.map((item) => ({
                        ...item,
                        name: item?.name[language]
                    })),
                    brand: item?.brand?.name[language],
                    description: item?.description[language],
                    banner: process.env.DOMAIN + item?.banner,
                    offerPrice: item?.offerPrice?.aed < 1 || item?.offerPrice?.aed >= item?.price?.aed ? null : item?.offerPrice?.aed,
                    price: item?.price?.aed,
                    color: item?.color?.name[language],
                    sizes: sizes || [],
                    images: item?.images?.map((image) => process.env.DOMAIN + image),
                    thumbnail: process.env.DOMAIN + item?.thumbnail,
                    descriptionImages: item?.descriptionImages?.map((image) => process.env.DOMAIN + image),
                    category: item?.category?.map((cat) => cat.name[language]),
                    technicalInfo: item?.technicalInfo.map((info) => {
                        return {
                            title: info?.title[language],
                            description: info?.description[language],
                        };
                    }),
                });
            }
            helper.deliverResponse(res, 200, productData, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Invalid device token",
                }
            );
        }
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.removeCompare = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                error_code: messages.VALIDATION_ERROR.error_code,
                error_message: messages.VALIDATION_ERROR.error_message,
            });
        }

        const { devicetoken } = req?.headers;
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        if (!devicetoken) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.VALIDATION_ERROR.error_code,
                    error_message: messages.VALIDATION_ERROR.error_message,
                }
            );
        }

        const compareUser = await compareService.findOne({
            deviceToken: devicetoken,
            storeId: storeid
        });
        if (compareUser) {
            if (body?.type == "single") {
                const response = await compareService.update({ deviceToken: compareUser.deviceToken, storeId: storeid }, { $pull: { products: body?.product } });
                return helper.deliverResponse(res, 200, response, {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: language == "en" ? "Product removed successfully" : "تمت إزالة المنتج بنجاح",
                });
            }
            if (body?.type == "all") {
                const response = await compareService.update({ deviceToken: compareUser.deviceToken, storeId: storeid }, { $set: { products: [] } });
                return helper.deliverResponse(res, 200, response, {
                    error_code: messages.SUCCESS_RESPONSE.error_code,
                    error_message: language == "en" ? "Compare list cleared successfully" : "تمت مقارنة القائمة بنجاح",
                });
            }
        } else {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: language == "en" ? "Invalid device token" : "رمز الجهاز غير صالح",
                }
            );
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.variants = async (req, res) => {
    try {
        const { id } = req.params;
        let { language, storeid } = req?.headers;
        language = language ? language : "en";
        storeId = storeId ? storeId : "ae";
        const mainProduct = await service.findOne({
            _id: id,
            isDelete: false,
            isActive: true,
            storeId: storeid
        });
        let variants = [];
        if (mainProduct.variants.length > 0) {
            variants.push({
                slug: mainProduct.slug,
                color: mainProduct.color.color,
                name: mainProduct.color.name?.[language],
                variantDisplayType: mainProduct?.variantDisplayType ?? "color",
                variantImage: process.env.DOMAIN + mainProduct?.variantImage,
                _id: mainProduct._id,
            });
            for (let item of mainProduct.variants) {
                if ((!item?.isDelete && item?.isActive) && item?._id?.toString() != mainProduct?._id?.toString())
                    variants.push({
                        slug: item.slug,
                        color: item?.color?.color,
                        name: item?.color?.name?.[language],
                        variantDisplayType: item?.variantDisplayType ?? "color",
                        variantImage: process.env.DOMAIN + item?.variantImage,
                        _id: item._id,
                    });
            }
            helper.deliverResponse(res, 200, variants, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message,
            });
        } else {
            helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: 1,
                    error_message: "Variants not found",
                }
            );
        }
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

const createBrand = async (brand) => {
    try {
        const refid = generateUniqueNumber();
        let payload = {
            name: {
                en: brand.name,
                ar: brand.name,
            },
            slug: await slug.createSlug(db.Brands, brand.name, {
                slug: await slug.generateSlug(brand.name),
                storeId: brand?.storeId ?? "ae"
            }),
            refid: refid,
            brandId: `BRAND-${refid}`,
            storeId: brand?.storeId ?? "ae"
        };

        if (brand?.isMain == false) payload.isMain = false
        else payload.isMain = true;
        if (brand?.parent) payload.parent = brand?.parent
        let data = await brandService.create(payload);
        return data;
    } catch (error) {
        console.log(error)
        return error;
    }
};

exports.bulkSunglassImport = async (req, res) => {
    let csvDir, imagesDir;
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        // const zip = new AdmZip(req.file.path);
        csvDir = "tmp/csv";
        imagesDir = "tmp/images";
        fs.mkdirSync(csvDir, { recursive: true });
        fs.mkdirSync(imagesDir, { recursive: true });
        // const zip = new StreamZip({ file: req.file.path });
        const zip = new StreamZip.async({ file: req.file.path });
        const entries = await zip.entries();
        const files = []
        for (let entry of Object.values(entries)) {
            files.push(entry.name)
            const fileName = entry.name;
            if (fileName.endsWith(".csv")) {
                await zip.extract(fileName, csvDir, err => console.log(err));
            } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
                await zip.extract(fileName, imagesDir, err => console.log(err));
            }
        }

        if (!files.includes("products.csv")) throw new Error("products.csv not found")

        await zip.close();

        res.setHeader("Content-Type", "text/event-stream");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        res.flushHeaders();

        const productsFilePath = `${csvDir}/products.csv`;
        const products = [];
        fs.createReadStream(productsFilePath)
            .pipe(csv({
                mapHeaders: (({ header }) => {
                    if (header.charCodeAt(0) === 0xFEFF) {
                        header = header.substr(1);
                    }
                    return header;
                })
            }))
            .on("data", (data) => products.push(data))
            .on("end", async () => {
                try {
                    const totalProducts = products.length;
                    if (totalProducts == 0) throw new Error("Error: No products in CSV")
                    let processedProducts = 0;
                    let writer = {
                        count: 0
                    }

                    const productImagesDir = "uploads/products/";
                    fs.mkdirSync(productImagesDir, { recursive: true });

                    const createdProducts = [];
                    // const productPromises = [];
                    const productHeads = {};
                    // const categoryStrings = [], categoryPromises = [];
                    // const brands = [], brandPromises = [];
                    // const colors = [], colorCodes = [], colorPromises = [];
                    // const sphs = [], sphPromises = [];
                    // const cyls = [], cylPromises = [];
                    // const axis = [], axisPromises = [];
                    // const adds = [], addPromises = [];
                    // const frameTypes = [], frameTypePromises = [];
                    // const frameShapes = [], frameShapePromises = [];
                    // const frontMaterials = [], frontMaterialPromises = [];
                    // const lensMaterials = [], lensMaterialPromises = [];
                    // const types = [], typePromises = [];
                    // const sizes = [], sizePromises = [];
                    // const labels = [], labelPromises = [];

                    for (const [key, product] of products?.entries()) {
                        if (!product?.sku?.trim()) {
                            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
                        }
                        if (product?.brand?.trim() && product?.model_name?.trim()) {
                            if (!productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`]) {
                                productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`] = key + 1
                            }
                        }
                        //     if (product?.brand?.trim()) {
                        //         if (!brands.includes(product?.brand?.trim())) {
                        //             brands.push(product?.brand?.trim())
                        //         }
                        //     }
                        //     if (product?.categories?.trim()) {
                        //         const catStrings = product.categories?.split(",");
                        //         for (let cat of catStrings) {
                        //             if (!categoryStrings.includes(cat.trim())) {
                        //                 categoryStrings.push(cat.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.sphValues?.trim()) {
                        //         const sphValues = product?.sphValues?.split(",");
                        //         for (let sph of sphValues) {
                        //             if (!sphs.includes(sph.trim())) {
                        //                 sphs.push(sph.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.cylValues?.trim()) {
                        //         const cylValues = product?.cylValues?.split(",");
                        //         for (let cyl of cylValues) {
                        //             if (!cyls.includes(cyl.trim())) {
                        //                 cyls.push(cyl.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.axisValues?.trim()) {
                        //         const axisValues = product?.axisValues?.split(",");
                        //         for (let item of axisValues) {
                        //             if (!axis.includes(item?.trim())) {
                        //                 axis.push(item?.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.addValues?.trim()) {
                        //         const addValues = product?.addValues?.split(",");
                        //         for (let add of addValues) {
                        //             if (!adds.includes(add.trim())) {
                        //                 adds.push(add.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.frame_type?.trim()) {
                        //         if (!frameTypes.includes(product?.frame_type?.trim())) {
                        //             frameTypes.push(product?.frame_type?.trim())
                        //         }
                        //     }
                        //     if (product?.frame_shape?.trim()) {
                        //         if (!frameShapes.includes(product?.frame_shape?.trim())) {
                        //             frameShapes.push(product?.frame_shape?.trim())
                        //         }
                        //     }
                        //     if (product?.front_material?.trim()) {
                        //         const productFrontMaterials = product?.front_material?.trim()?.split(",");
                        //         for (let material of productFrontMaterials) {
                        //             if (!frontMaterials.includes(material.trim())) {
                        //                 frontMaterials.push(material.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.lens_material?.trim()) {
                        //         const productLensMaterials = product?.lens_material?.trim()?.split(",");
                        //         for (let material of productLensMaterials) {
                        //             if (!lensMaterials.includes(material?.trim())) {
                        //                 lensMaterials.push(material?.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.type?.trim()) {
                        //         const productTypes = product?.type?.trim()?.split(",");
                        //         for (let type of productTypes) {
                        //             if (!types.includes(type?.trim())) {
                        //                 types.push(type?.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.size?.trim()) {
                        //         if (!sizes.includes(product?.size?.trim())) {
                        //             sizes.push(product?.size?.trim())
                        //         }
                        //     }
                        //     if (product?.label?.trim()) {
                        //         if (!labels.includes(product?.label?.trim())) {
                        //             labels.push(product?.label?.trim())
                        //         }
                        //     }
                        //     if (product?.color?.trim()) {
                        //         if (!colors.includes(product?.color?.trim())) {
                        //             colors.push(product?.color?.trim())
                        //             colorCodes.push(product?.html_color_code?.trim())
                        //         }
                        //     }
                    }
                    // let headCount = 0
                    const headRef = []
                    // for (let head of productHeads) {
                    //     headPromises.push((async () => {
                    //         let productHead = await db.ProductHead.findOne({
                    //             sku: head,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         })

                    //         if (!productHead) {
                    //             productHead = await db.ProductHead.create({
                    //                 sku: head,
                    //                 isDelete: false,
                    //                 name: head,
                    //                 refid: generateUniqueNumber(),
                    //                 products: [],
                    //                 type: "frame",
                    //                 storeId: storeid
                    //             })
                    //         }
                    //         headRef.push(productHead)
                    //         headCount++
                    //         res.write(`${headCount}/${productHeads.length} imported Product Heads\n\n`);
                    //     })())
                    // }
                    // let brandCount = 0
                    const brandRef = {}
                    // for (let brand of brands) {
                    //     brandPromises.push((async () => {
                    //         let brandDetails = await db.Brands.findOne({
                    //             "name.en": brand,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!brandDetails) brandDetails = await createBrand({ name: brand, storeId: storeid });
                    //         brandRef.push(brandDetails)
                    //         brandCount++
                    //         res.write(`${brandCount}/${brands.length} imported Brands\n\n`);
                    //     })())
                    // }

                    const catRef = [];
                    const subCatRef = [];
                    // let catCount = 0
                    // categoryPromises.push((async () => {
                    //     let categories = [];
                    //     let subCategories = [];
                    //     for (let cat of categoryStrings) {
                    //         let cats = cat.split(">");
                    //         let prev = null;
                    //         let root = null
                    //         for (let [key, cat] of cats?.entries()) {

                    //             if (prev) {
                    //                 let category = await db.Category.findOne({
                    //                     "name.en": cat?.trim(),
                    //                     isDelete: false,
                    //                     isRoot: false,
                    //                     parent: prev?._id,
                    //                     root: root?._id,
                    //                     storeId: storeid
                    //                 })
                    //                 if (category) {
                    //                     subCategories.push(category?._id);
                    //                     subCatRef.push(category)
                    //                 }
                    //                 else {
                    //                     const ref = subCatRef.find(item => ((item?.name?.en == cat?.trim() && item?.parent?.toString() == prev?._id?.toString()) && (item?.root?.toString() == root?._id?.toString() && item?.isRoot == false)) && item?.storeId == storeid)
                    //                     if (ref) {
                    //                         category = ref;
                    //                     } else {
                    //                         category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                    //                         subCatRef.push(category)
                    //                         subCategories.push(category?._id);
                    //                     }
                    //                 }
                    //                 prev = category
                    //             } else {
                    //                 let mainCatDetails = await db.Category.findOne({
                    //                     "name.en": cat?.trim(),
                    //                     isDelete: false,
                    //                     storeId: storeid
                    //                 });
                    //                 if (mainCatDetails) {
                    //                     categories.push(mainCatDetails?._id);
                    //                     catRef.push(mainCatDetails)
                    //                 }
                    //                 else {
                    //                     const ref = catRef.find(item => (item?.name?.en == cat?.trim() && item?.storeId == storeid) && item?.isDelete == false)
                    //                     if (ref) {
                    //                         mainCatDetails = ref;
                    //                     } else {
                    //                         mainCatDetails = await createCategory({ name: cat?.trim(), storeId: storeid });
                    //                         catRef.push(mainCatDetails)
                    //                         categories.push(mainCatDetails?._id);
                    //                     }
                    //                 }
                    //                 prev = mainCatDetails
                    //                 root = mainCatDetails
                    //             }
                    //         }
                    //         catCount++
                    //         res.write(`${catCount}/${categoryStrings.length} imported Category combinations\n\n`);
                    //     }
                    // })())

                    let lensBrand;
                    // let sphCount = 0
                    const sphRef = {}
                    // sphPromises.push((async () => {
                    //     if (!lensBrand) {
                    //         lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
                    //     }
                    //     let promise = [];
                    //     for (let power of sphs) promise.push(db.LensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of sphs.entries()) {
                    //         let powerDetails = details[key];
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.LensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid })
                    //         }
                    //         sphRef.push(powerDetails)
                    //         sphCount++
                    //         res.write(`${sphCount}/${sphs.length} imported Sph values\n\n`);
                    //     }
                    // })())

                    // let cylCount = 0
                    const cylRef = {}
                    // cylPromises.push((async () => {
                    //     if (!lensBrand) {
                    //         lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
                    //     }
                    //     let promise = [];
                    //     for (let power of cyls) promise.push(db.LensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of cyls.entries()) {
                    //         let powerDetails = details[key];
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.LensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    //         }
                    //         cylRef.push(powerDetails)
                    //         cylCount++
                    //         res.write(`${cylCount}/${cyls.length} imported Cyl values\n\n`);
                    //     }
                    // })())

                    // let axisCount = 0
                    const axisRef = {}
                    // axisPromises.push((async () => {
                    //     if (!lensBrand) {
                    //         lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
                    //     }
                    //     let promise = [];
                    //     for (let power of axis) promise.push(db.LensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of axis.entries()) {
                    //         let powerDetails = details[key];
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.LensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    //         }
                    //         axisRef.push(powerDetails)
                    //         axisCount++
                    //         res.write(`${axisCount}/${axis.length} imported Axis values\n\n`);
                    //     }
                    // })())

                    // let addCount = 0
                    const addRef = {};
                    // addPromises.push((async () => {
                    //     if (!lensBrand) {
                    //         lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
                    //     }
                    //     let promise = [];
                    //     for (let power of adds) promise.push(db.LensPower.findOne({ type: 'Add', name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of adds.entries()) {
                    //         let powerDetails = details[key];
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.LensPower.create({ type: 'Add', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    //         }
                    //         addRef.push(powerDetails)
                    //         addCount++
                    //         res.write(`${addCount}/${adds.length} imported Add values\n\n`);
                    //     }
                    // })())

                    // let colorCount = 0
                    const colorRef = {};
                    // for (let [key, color] of colors?.entries()) {
                    //     colorPromises.push((async () => {
                    //         let colorDetail = await db.Colors.findOne({
                    //             "name.en": color,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!colorDetail) colorDetail = await createColor(color, colorCodes[key], storeid);
                    //         colorRef.push(colorDetail)
                    //         colorCount++
                    //         res.write(`${colorCount}/${colors.length} imported Colors\n\n`);
                    //     })())
                    // }

                    // let frameTypeCount = 0
                    const frameTypeRef = {}
                    // for (let frameType of frameTypes) {
                    //     frameTypePromises.push((async () => {
                    //         let frameTypeDetail = await db.FrameTypes.findOne({
                    //             "name.en": frameType,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!frameTypeDetail)
                    //             frameTypeDetail = await createFrameType({
                    //                 name: frameType,
                    //                 storeId: storeid
                    //             });
                    //         frameTypeRef.push(frameTypeDetail)
                    //         frameTypeCount++
                    //         res.write(`${frameTypeCount}/${frameTypes.length} imported Frame Types\n\n`);
                    //     })())
                    // }

                    // let frameShapeCount = 0
                    const frameShapeRef = {}
                    // for (let frameShape of frameShapes) {
                    //     frameShapePromises.push((async () => {
                    //         let frameShapeDetail = await db.FrameShapes.findOne({
                    //             "name.en": frameShape,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!frameShapeDetail)
                    //             frameShapeDetail = await createFrameShape({
                    //                 name: frameShape,
                    //                 storeId: storeid
                    //             });
                    //         frameShapeRef.push(frameShapeDetail)
                    //         frameShapeCount++
                    //         res.write(`${frameShapeCount}/${frameShapes.length} imported Frame Shapes\n\n`);
                    //     })())
                    // }

                    // let labelCount = 0
                    const labelRef = {}
                    // for (let label of labels) {
                    //     labelPromises.push((async () => {
                    //         let labelDetail = await db.Labels.findOne({
                    //             "name.en": label,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!labelDetail) labelDetail = await createLabel({ name: label, storeId: storeid });
                    //         labelRef.push(labelDetail)
                    //         labelCount++
                    //         res.write(`${labelCount}/${labels.length} imported Labels\n\n`);
                    //     })())
                    // }

                    // let frontMaterialCount = 0
                    const frontMaterialRef = {}
                    // for (let material of frontMaterials) {
                    //     frontMaterialPromises.push((async () => {
                    //         let frontMaterialDetail = await db.FrontMaterial.findOne({
                    //             "name.en": material?.trim(),
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!frontMaterialDetail) {
                    //             frontMaterialDetail = await createFrontMaterial({
                    //                 name: { en: material?.trim() },
                    //                 storeId: storeid
                    //             });
                    //         }
                    //         frontMaterialRef.push(frontMaterialDetail)
                    //         frontMaterialCount++
                    //         res.write(`${frontMaterialCount}/${frontMaterials.length} imported Front Materials\n\n`);
                    //     })())
                    // }

                    // let lensMaterialCount = 0
                    const lensMaterialRef = {}
                    // for (let material of lensMaterials) {
                    //     lensMaterialPromises.push((async () => {
                    //         let lensMaterialDetail = await db.LensMaterial.findOne({
                    //             "name.en": material?.trim(),
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!lensMaterialDetail) {
                    //             lensMaterialDetail = await createLensMaterial({
                    //                 name: { en: material?.trim() },
                    //                 storeId: storeid
                    //             });
                    //         }
                    //         lensMaterialRef.push(lensMaterialDetail)
                    //         lensMaterialCount++
                    //         res.write(`${lensMaterialCount}/${lensMaterials.length} imported Lens Materials\n\n`);
                    //     })())
                    // }

                    // let typeCount = 0
                    const typeRef = {}
                    // for (let type of types) {
                    //     typePromises.push((async () => {
                    //         let typeDetail = await db.Type.findOne({
                    //             "name.en": type?.trim(),
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!typeDetail) {
                    //             typeDetail = await createType({ name: type?.trim(), storeId: storeid });
                    //         }
                    //         typeRef.push(typeDetail)
                    //         typeCount++
                    //         res.write(`${typeCount}/${types.length} imported Types\n\n`);
                    //     })())
                    // }

                    // let sizeCount = 0
                    const sizeRef = {}
                    // for (let size of sizes) {
                    //     sizePromises.push((async () => {
                    //         let sizeDetails = await db.Sizes.findOne({
                    //             "name": size?.trim(),
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!sizeDetails) sizeDetails = await createSize({ name: size?.trim(), storeId: storeid });
                    //         sizeRef.push(sizeDetails)
                    //         sizeCount++
                    //         res.write(`${sizeCount}/${sizes.length} imported Sizes\n\n`);
                    //     })())
                    // }


                    // await Promise.all([
                    //     ...headPromises,
                    //     ...brandPromises,
                    //     ...categoryPromises,
                    //     ...sphPromises,
                    //     ...axisPromises,
                    //     ...cylPromises,
                    //     ...addPromises,
                    //     ...colorPromises,
                    //     ...frameTypePromises,
                    //     ...frameShapePromises,
                    //     ...labelPromises,
                    //     ...frontMaterialPromises,
                    //     ...lensMaterialPromises,
                    //     ...typePromises,
                    //     ...sizePromises
                    // ])

                    res.write(`${0}/${products.length} processing products\n\n`);



                    for (const [key, product] of products?.entries()) {
                        // productPromises.push(
                        await processSunglassProducts(
                            req, res,
                            product, createdProducts, processedProducts, totalProducts,
                            imagesDir, productImagesDir, writer, key,
                            catRef, subCatRef, headRef, brandRef, sphRef, lensBrand,
                            cylRef, axisRef, addRef, colorRef, frameTypeRef, frameShapeRef,
                            labelRef, frontMaterialRef, lensMaterialRef, typeRef, sizeRef, productHeads
                        )
                        // )
                    };

                    // try {
                    //     await Promise.all(productPromises);
                    // } catch (error) {
                    //     throw error;
                    // }
                    const resp = await createdSunglassProductsUpdate(createdProducts, products, csvDir, req)

                    res.write(`event: message\ndata: Products imported successfully\n\n`);
                    setTimeout(() => {
                        cleanup(req.file.path, csvDir, imagesDir);
                    }, 5000);
                    // Close the connection
                    res.end();

                } catch (error) {
                    console.log(error)
                    setTimeout(() => {
                        res.write(`${error.message?.includes("Error") ? error.message : "Error: " + error.message}\n\n`);
                        res.end();
                    }, 1000);
                    return
                }
            })

        // res.status(200).json({ message: "Products imported successfully" });
    } catch (error) {
        console.log("error in bulk import")
        console.log(error, " :: Error caught in product import");
        res.status(422).json({
            error_code: "90",
            error_message: `Import error: ${error.message}`,
        });
    }
};

const processSunglassProducts = async (
    req, res,
    product, createdProducts,
    processedProducts, totalProducts,
    imagesDir, productImagesDir, writer, key,
    catRef, subCatRef, headRef, brandRef, sphRef, lensBrand,
    cylRef, axisRef, addRef, colorRef, frameTypeRef, frameShapeRef,
    labelRef, frontMaterialRef, lensMaterialRef, typeRef, sizeRef, productHeads
) => {
    const release = await mutex.acquire();
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"

        // if (product?.storeId) storeid = product?.storeId;
        if (!product?.sku?.trim()) {
            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
        }
        let newPayload = {};
        let brandDetails = null;
        let isNewProductHead = false;

        async function addProductHead() {
            if (product?.brand?.trim() && product?.model_name?.trim() && product?.model_code?.trim() && product?.color_code?.trim()) {
                let productHead;

                productHead = await db.ProductHead.findOne({
                    sku: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                    isDelete: false,
                    storeId: storeid
                })

                if (!productHead) {
                    isNewProductHead = true;
                    productHead = await db.ProductHead.create({
                        sku: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                        isDelete: false,
                        name: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                        refid: generateUniqueNumber(),
                        products: [],
                        type: "frame",
                        storeId: storeid
                    })
                }
                newPayload.parent = productHead._id
                if (productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`] === (key + 1)) {
                    newPayload.isDefaultVariant = true
                } else {
                    newPayload.isDefaultVariant = false
                }
                return productHead
            }

        }

        async function addBrand() {
            if (product?.brand?.trim()) {
                if (brandRef[product?.brand?.trim()]) {
                    brandDetails = brandRef[product?.brand?.trim()];
                } else {
                    brandDetails = await db.Brands.findOne({
                        "name.en": product.brand?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!brandDetails) brandDetails = await createBrand({ name: product.brand?.trim(), storeId: storeid });
                newPayload.brand = brandDetails._id
                brandRef[product?.brand?.trim()] = brandDetails
            }
        }

        async function addCategories() {
            if (product?.categories?.trim()) {
                let categories = [];
                let subCategories = [];
                const catStrings = product.categories?.split(",");
                const catPromises = [];
                for (let cat of catStrings) {
                    catPromises.push((async () => {
                        let cats = cat.split(">");
                        let prev = null;
                        let root = null
                        for (let cat of cats) {
                            if (prev) {
                                let category;

                                category = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    isRoot: false,
                                    parent: prev?._id,
                                    root: root?._id,
                                    storeId: storeid
                                })

                                if (category) subCategories.push(category?._id);
                                else {
                                    category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                                    subCategories.push(category?._id);
                                }
                                prev = category
                            } else {
                                let mainCatDetails

                                mainCatDetails = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    storeId: storeid
                                });

                                if (mainCatDetails) categories.push(mainCatDetails?._id);
                                else {
                                    mainCatDetails = await createCategory({ name: cat?.trim(), storeId: storeid });
                                    categories.push(mainCatDetails?._id);
                                }
                                prev = mainCatDetails
                                root = mainCatDetails
                            }
                        }
                    })())
                }
                await Promise.all(catPromises);
                // console.log("categories", categories)
                newPayload.category = Array.from(new Set(categories.map(i => i?.toString())));
                newPayload.subCategory = Array.from(new Set(subCategories.map(i => i?.toString())));
            }
        }

        async function addSph() {
            if (product?.sphValues?.trim()) {
                if (!lensBrand) {
                    lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
                }
                let sphValues = [];
                const values = product?.sphValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (sphRef[power?.trim()]) {
                        return sphRef[power?.trim()];
                    } else {
                        let item = await db.LensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid })
                        sphRef[power?.trim()] = item;
                        return item
                    }
                })()
                );
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key];
                    if (!powerDetails) {
                        let refid = generateUniqueNumber();
                        powerDetails = await db.LensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid })
                    }
                    sphValues.push(powerDetails._id)
                }
                newPayload.sphValues = sphValues
            }
        }

        async function addCyl() {
            if (!lensBrand) {
                lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
            }
            if (product?.cylValues?.trim()) {
                let cylValues = [];
                const values = product?.cylValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (cylRef[power?.trim()]) {
                        return cylRef[power?.trim()];
                    } else {
                        let item = await db.LensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid })
                        cylRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key];
                    if (!powerDetails) {
                        let refid = generateUniqueNumber();
                        powerDetails = await db.LensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    }
                    cylValues.push(powerDetails._id)
                }
                newPayload.cylValues = cylValues
            }
        }

        async function addAxis() {
            if (!lensBrand) {
                lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
            }
            if (product?.axisValues?.trim()) {
                let axisValues = [];
                const values = product?.axisValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (axisRef[power?.trim()]) {
                        return axisRef[power?.trim()];
                    } else {
                        let item = await db.LensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid })
                        axisRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key];
                    if (!powerDetails) {
                        let refid = generateUniqueNumber();
                        powerDetails = await db.LensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    }
                    axisValues.push(powerDetails._id)
                }
                newPayload.axisValues = axisValues
            }
        }

        async function addAdd() {
            if (product?.addValues?.trim()) {
                let addValues = [];
                const values = product?.addValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (addRef[power?.trim()]) {
                        return addRef[power?.trim()];
                    } else {
                        let item = await db.LensPower.findOne({ type: 'Add', name: power?.trim(), isDelete: false, storeId: storeid })
                        addRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key];
                    if (!powerDetails) {
                        let refid = generateUniqueNumber();
                        powerDetails = await db.LensPower.create({ type: 'Add', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                    }
                    addValues.push(powerDetails._id)
                }
                newPayload.addValues = addValues
            }
        }


        async function addColor() {
            if (product?.color?.trim()) {
                let colorDetail;
                if (colorRef[product?.color?.trim()]) {
                    colorDetail = colorRef[product?.color?.trim()];
                } else {
                    colorDetail = await db.Colors.findOne({
                        "name.en": product.color?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!colorDetail) colorDetail = await createColor(product.color?.trim(), product.html_color_code, storeid);
                newPayload.color = colorDetail._id
                colorRef[product?.color?.trim()] = colorDetail
            }
        }

        async function addFrameType() {
            if (product?.frame_type?.trim()) {
                let frameTypeDetail;
                if (frameTypeRef[product?.frame_type?.trim()]) {
                    frameTypeDetail = frameTypeRef[product?.frame_type?.trim()];
                } else {
                    frameTypeDetail = await db.FrameTypes.findOne({
                        "name.en": product.frame_type?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!frameTypeDetail)
                    frameTypeDetail = await createFrameType({
                        name: product.frame_type?.trim(),
                        storeId: storeid
                    });
                newPayload.frameType = frameTypeDetail._id
                frameTypeRef[product?.frame_type?.trim()] = frameTypeDetail
            }
        }

        async function addFrameShape() {
            if (product?.frame_shape?.trim()) {
                let frameShapeDetail;
                if (frameShapeRef[product?.frame_shape?.trim()]) {
                    frameShapeDetail = frameShapeRef[product?.frame_shape?.trim()];
                } else {
                    frameShapeDetail = await db.FrameShapes.findOne({
                        "name.en": product.frame_shape?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!frameShapeDetail)
                    frameShapeDetail = await createFrameShape({
                        name: product.frame_shape?.trim(),
                        storeId: storeid
                    });
                newPayload.frameShape = frameShapeDetail._id
                frameShapeRef[product?.frame_shape?.trim()] = frameShapeDetail
            }
        }

        async function addLabel() {
            if (product?.label?.trim()) {
                let labelDetail;
                if (labelRef[product?.label?.trim()]) {
                    labelDetail = labelRef[product?.label?.trim()];
                } else {
                    labelDetail = await db.Labels.findOne({
                        "name.en": product.label?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
                newPayload.label = labelDetail._id
                labelRef[product?.label?.trim()] = labelDetail
            }
        }

        async function addFrontMaterial() {
            if (product?.front_material?.length > 0) {
                let frontMaterials = [];
                const productFrontMaterials = product?.front_material?.split(",");
                for (let material of productFrontMaterials) {
                    let frontMaterialDetail;
                    if (frontMaterialRef[material?.trim()]) {
                        frontMaterialDetail = frontMaterialRef[material?.trim()];
                    } else {
                        frontMaterialDetail = await db.FrontMaterial.findOne({
                            "name.en": material?.trim(),
                            isDelete: false,
                            storeId: storeid
                        });
                    }
                    if (frontMaterialDetail) frontMaterials.push(frontMaterialDetail?._id);
                    else {
                        frontMaterialDetail = await createFrontMaterial({
                            "name.en": material?.trim(),
                            storeId: storeid
                        });
                        frontMaterials.push(frontMaterialDetail?._id);
                    }
                    frontMaterialRef[material?.trim()] = frontMaterialDetail
                }
                newPayload.frontMaterial = frontMaterials
            }
        }

        async function addLensMaterial() {
            if (product?.lens_material?.length > 0) {
                let lensMaterials = [];
                const productLensMaterials = product?.lens_material?.split(",");
                for (let material of productLensMaterials) {
                    let lensMaterialDetail;
                    if (lensMaterialRef[material?.trim()]) {
                        lensMaterialDetail = lensMaterialRef[material?.trim()];
                    } else {
                        lensMaterialDetail = await db.LensMaterial.findOne({
                            "name.en": material?.trim(),
                            isDelete: false,
                            storeId: storeid
                        });
                    }
                    if (lensMaterialDetail) lensMaterials.push(lensMaterialDetail?._id)
                    else {
                        lensMaterialDetail = await createLensMaterial({
                            name: { en: material?.trim() },
                            storeId: storeid
                        });
                        lensMaterials.push(lensMaterialDetail?._id);
                    }
                    lensMaterialRef[material?.trim()] = lensMaterialDetail
                }
                newPayload.lensMaterial = lensMaterials
            }
        }

        async function addType() {
            if (product?.type?.length > 0) {
                let types = [];
                const productTypes = product?.type?.split(",");
                for (let type of productTypes) {
                    let typeDetail;
                    if (typeRef[type?.trim()]) {
                        typeDetail = typeRef[type?.trim()];
                    } else {
                        typeDetail = await db.Type.findOne({
                            "name.en": type?.trim(),
                            isDelete: false,
                            storeId: storeid
                        });
                    }
                    if (typeDetail) types.push(typeDetail?._id);
                    else {
                        typeDetail = await createType({ name: type?.trim(), storeId: storeid });
                        types.push(typeDetail?._id);
                    }
                    typeRef[type?.trim()] = typeDetail
                }
                newPayload.type = types
            }
        }

        async function addSize() {
            if (product?.size?.trim()) {
                let sizeDetails;
                if (sizeRef[product?.size?.trim()]) {
                    sizeDetails = sizeRef[product?.size?.trim()];
                } else {
                    sizeDetails = await db.Sizes.findOne({
                        "name": product.size?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!sizeDetails) sizeDetails = await createSize({ name: product.size?.trim(), storeId: storeid });
                newPayload.size = sizeDetails._id
                sizeRef[product?.size?.trim()] = sizeDetails
            }
        }

        async function uploadImages() {
            if (product?.product_images?.trim()) {
                let files = [];
                let images = product?.product_images?.split(",");
                for (let _image of images) {
                    if (_image) {
                        if (_image.includes("/")) {
                            files.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/images/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            files.push(key)
                        }
                    }
                }
                newPayload.images = files
            }
        }

        async function uploadDescriptionImages() {
            if (product?.description_images?.trim()) {
                let descImages = [];
                let descriptionImages = product?.description_images?.split(",");
                for (let _image of descriptionImages) {
                    if (_image) {
                        if (_image.includes("/")) {
                            descImages.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/descriptionImages/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            descImages.push(key);
                        }
                    }
                }
                newPayload.descriptionImages = descImages;
            }
        }

        async function uploadThumbnail() {
            if (product?.thumb_image?.trim()) {
                if (product.thumb_image.includes("/")) {
                    newPayload.thumbnail = product.thumb_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.thumb_image);
                    const name = `products/${product?.sku}/thumbnails/${Date.now()}-${product.thumb_image}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload.thumbnail = key
                }
            }
        }

        async function getProductDetails() {
            let productDetail = await service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid })
            if (productDetail && productDetail.productType != "frame") throw new Error(`SKU Exist in product type ${productDetail?.productType}`);
            return productDetail
        }

        async function uploadOgImage() {
            if (product?.meta_og_image?.trim()) {
                if (product.meta_og_image.includes("/")) {
                    newPayload["seoDetails.ogImage"] = product.meta_og_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.meta_og_image?.trim());
                    const name = `products/${product?.sku}/ogImages/${Date.now()}-${product?.meta_og_image?.trim()}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload["seoDetails.ogImage"] = key
                }
            }
        }

        const [
            productDetail,
            productHead
        ] = await Promise.all([
            getProductDetails(),
            addProductHead(),
            addBrand(),
            addSph(),
            addCyl(),
            addAxis(),
            addAdd(),
            addCategories(),
            addColor(),
            addFrameType(),
            addFrameShape(),
            addLabel(),
            addFrontMaterial(),
            addLensMaterial(),
            addType(),
            addSize(),
            uploadImages(),
            uploadDescriptionImages(),
            uploadThumbnail(),
            uploadOgImage(),
        ])

        if (product?.gender) {
            let gender = product?.gender?.split(",").map((gender) => gender.trim()) || [];
            newPayload.gender = gender
        }

        if (product?.sku?.trim()) newPayload.sku = product?.sku?.trim();
        if (product?.size_bridge?.trim()) newPayload.sizeBridge = product?.size_bridge?.trim();
        if (product?.model_name?.trim()) newPayload.modelName = product?.model_name?.trim();
        if (product?.model_code?.trim()) newPayload.modelCode = product?.model_code?.trim();
        if (product?.color_code?.trim()) newPayload.colorCode = product?.color_code?.trim();
        if (product?.lense_color?.trim()) newPayload.lenseColor = product?.lense_color?.trim();
        if (product?.temple_length?.trim()) newPayload.templeLength = product?.temple_length?.trim();
        if (brandDetails && product?.model_name?.trim() && product?.model_code?.trim() && product?.color_code?.trim()) {
            newPayload.name = { en: `${brandDetails?.name?.en} ${product?.model_name?.trim()} ${product?.model_code?.trim()} ${product?.color_code?.trim()}` };
        }
        // if (product?.name_en?.trim()) newPayload.name = { en: product?.name_en?.trim() };
        // if (product?.name_ar?.trim()) newPayload.name = { en: newPayload?.name?.en ?? productDetail?.name.en, ar: product?.name_ar?.trim() };
        if (product?.description_en?.trim()) newPayload.description = { en: product?.description_en?.trim() };
        if (product?.description_ar?.trim()) newPayload.description = { en: newPayload.description?.en, ar: product?.description_ar?.trim() };
        if (product?.description_two_en?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim() };
        if (product?.description_two_ar?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim(), ar: product?.description_two_ar?.trim() };
        if (product?.stock?.trim()) {
            if (isNaN(product?.stock)) throw new Error(`Stock must be numeric for ${product?.sku}`);
            newPayload.stock = Number(product?.stock);
            newPayload.customizable = false
        };
        if (product?.position?.trim()) {
            if (isNaN(product?.position)) throw new Error(`Position must be numeric for ${product?.sku}`);
            newPayload.position = Number(product?.position);
        }
        if (product?.price?.trim()) {
            if (isNaN(product?.price)) throw new Error(`Price must be numeric for ${product?.sku}`);
            newPayload.price = { aed: Number(product?.price) };
            newPayload.customizable = false
        };
        if (product?.offer_price?.trim()) {
            if (isNaN(product?.offer_price)) throw new Error(`Offer_price must be numeric for ${product?.sku}`);
            newPayload.offerPrice = { aed: Number(product?.offer_price) };
            newPayload.customizable = false
        }
        if (product?.supplier_sku?.trim()) newPayload.supplierSku = product?.supplier_sku?.trim();
        if (product?.upc?.trim()) newPayload.upc = product?.upc?.trim();
        if (product?.weight?.trim()) {
            if (isNaN(product?.weight)) throw new Error(`Weight must be numeric for ${product?.sku}`);
            newPayload.weight = Number(product?.weight);
        }
        if (product?.cashbackPercentage?.trim()) {
            if (isNaN(product?.cashbackPercentage)) throw new Error(`CashbackPercentage must be numeric for ${product?.sku}`);
            newPayload.cashbackPercentage = Number(product?.cashbackPercentage);
        }

        if (product?.meta_title_en?.trim()) newPayload["seoDetails.title.en"] = product?.meta_title_en?.trim()
        if (product?.meta_title_ar?.trim()) newPayload["seoDetails.title.ar"] = product?.meta_title_ar?.trim();
        if (product?.meta_description_en?.trim()) newPayload["seoDetails.description.en"] = product?.meta_description_en?.trim();
        if (product?.meta_description_ar?.trim()) newPayload["seoDetails.description.ar"] = product?.meta_description_ar?.trim();
        if (product?.meta_keywords_en?.trim()) newPayload["seoDetails.keywords.en"] = product?.meta_keywords_en?.trim();
        if (product?.meta_keywords_ar?.trim()) newPayload["seoDetails.keywords.ar"] = product?.meta_keywords_ar?.trim();
        if (product?.meta_canonical_en?.trim()) newPayload["seoDetails.canonical.en"] = product?.meta_canonical_en?.trim();
        if (product?.meta_canonical_ar?.trim()) newPayload["seoDetails.canonical.ar"] = product?.meta_canonical_ar?.trim();


        newPayload.productType = "frame";
        if (product?.offer_price?.trim() || product?.price?.trim()) {
            newPayload.sellingPrice = Number(product?.offer_price) || Number(product?.price);
        }

        if (newPayload.name?.en) {
            newPayload.slug = productDetail?.name?.en != newPayload.name?.en
                ? await slug.createSlug(db.Products, newPayload.name?.en, {
                    slug: await slug.generateSlug(newPayload.name?.en),
                    storeId: storeid,
                    isDelete: false
                })
                : productDetail?.slug;
        }

        newPayload.refid = productDetail ? productDetail?.refid : generateUniqueNumber();

        if (product?.is_customizable?.trim()) {
            if (product?.is_customizable?.trim() != "1" && product?.is_customizable?.trim() != "0") throw new Error(`is_customizable must be either 1 or 0 for ${product?.sku}`);
            newPayload.customizable = product?.is_customizable?.trim() == "1" ? true : false;
        };
        if (product?.add_to_cart?.trim()) {
            if (product?.add_to_cart?.trim() != "1" && product?.add_to_cart?.trim() != "0") throw new Error(`add_to_cart must be either 1 or 0 for ${product?.sku}`);
            newPayload.isAddToCart = product?.add_to_cart?.trim() == "1" ? true : false
        };
        if (product?.choose_lens?.trim()) {
            if (product?.choose_lens?.trim() != "1" && product?.choose_lens?.trim() != "0") throw new Error(`choose_lens must be either 1 or 0 for ${product?.sku}`);
            newPayload.isChooseLens = product?.choose_lens?.trim() == "1" ? true : false
        };
        if (product?.is_active?.trim()) {
            if (product?.is_active?.trim() != "1" && product?.is_active?.trim() != "0") throw new Error(`is_active must be either 1 or 0 for ${product?.sku}`);
            newPayload.isActive = product?.is_active?.trim() == "1" ? true : false
        };
        // if (product?.is_default_variant?.trim()) {
        //     if (product?.is_default_variant?.trim() != "1" && product?.is_default_variant?.trim() != "0") throw new Error(`is_default_variant must be either 1 or 0 for ${product?.sku}`);
        //     newPayload.isDefaultVariant = product?.is_default_variant?.trim() == "1" ? true : false
        // };
        if (product?.show_discount_percentage?.trim()) {
            if (product?.show_discount_percentage?.trim() != "1" && product?.show_discount_percentage?.trim() != "0") throw new Error(`show_discount_percentage must be either 1 or 0 for ${product?.sku}`);
            newPayload.showDiscountPercentage = product?.show_discount_percentage?.trim() == "1" ? true : false
        };
        if (product?.is_new_arrival?.trim()) {
            if (product?.is_new_arrival?.trim() != "1" && product?.is_new_arrival?.trim() != "0") throw new Error(`is_new_arrival must be either 1 or 0 for ${product?.sku}`);
            newPayload.isNewArrival = product?.is_new_arrival?.trim() == "1" ? true : false
        };
        if (product?.is_returnable?.trim()) {
            if (product?.is_returnable?.trim() != "1" && product?.is_returnable?.trim() != "0") throw new Error(`is_returnable must be either 1 or 0 for ${product?.sku}`);
            newPayload.isReturnable = product?.is_returnable?.trim() == "1" ? true : false
        };
        if (product?.virtual_try_available?.trim()) {
            if (product?.virtual_try_available?.trim() != "1" && product?.virtual_try_available?.trim() != "0") throw new Error(`virtual_try_available must be either 1 or 0 for ${product?.sku}`);
            newPayload.isVirtualTry = product?.virtual_try_available?.trim() == "1" ? true : false
        };
        if (product?.isTaxIncluded?.trim()) {
            if (product?.isTaxIncluded?.trim() != "1" && product?.isTaxIncluded?.trim() != "0") throw new Error(`isTaxIncluded must be either 1 or 0 for ${product?.sku}`);
            newPayload.isTaxIncluded = product?.isTaxIncluded?.trim() == "1" ? true : false
        };
        if (product?.isCashbackEnabled?.trim()) {
            if (product?.isCashbackEnabled?.trim() != "1" && product?.isCashbackEnabled?.trim() != "0") throw new Error(`isCashbackEnabled must be either 1 or 0 for ${product?.sku}`);
            newPayload.isCashbackEnabled = product?.isCashbackEnabled?.trim() == "1" ? true : false;
        };

        let i = 1
        const techDetails = [];
        while (true) {
            if (product?.[`t${i}_en`]) {
                techDetails.push({
                    title: {
                        en: product[`t${i}_en`],
                        ar: product[`t${i}_ar`],
                    },
                    description: {
                        en: product[`d${i}_en`],
                        ar: product[`d${i}_ar`],
                    }
                })
                i++
            } else {
                break;
            }
        }

        if (i !== 1) {
            newPayload.technicalInfo = techDetails
        }

        async function addProducts(newProduct) {
            if (productHead) {
                let products = productHead?.products || [];
                if (!products.map(item => item?.toString()).includes(newProduct?._id?.toString())) {
                    products?.push(newProduct?._id)
                    await db.ProductHead.updateOne({ _id: newProduct?.parent }, { products });
                }
            }
        }
        newPayload.storeId = storeid
        let newProduct;
        if (productDetail) {
            const [newProd] = await Promise.all([
                service.update({ _id: productDetail?._id }, newPayload),
                addProducts(productDetail)
            ])
            newProduct = newProd
        } else {
            if (!brandDetails) {
                throw new Error(`Brand is required`)
            } else if (!product?.model_name?.trim()) {
                throw new Error(`Model name is required`)
            } else if (!product?.model_code?.trim()) {
                throw new Error(`Model code is required`)
            } else if (!product?.color_code?.trim()) {
                throw new Error(`Color code is required`)
            }
            if (!product?.supplier_sku?.trim()) {
                throw new Error(`Supplier SKU is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.upc?.trim()) {
                throw new Error(`UPC is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.thumb_image?.trim()) {
                throw new Error(`Thumbnail is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.stock?.trim()) {
                throw new Error(`Stock is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.price?.trim()) {
                throw new Error(`Price is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.weight?.trim()) {
                throw new Error(`Weight is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.is_new_arrival?.trim()) {
                throw new Error(`is_new_arrival required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.description_en?.trim()) {
                throw new Error(`description_en required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            newProduct = await service.create(newPayload);
            await addProducts(newProduct)
        }

        newProduct.key = key

        createdProducts.push(newProduct);
        writer.count += 1
        res.write(`${parseInt(writer.count)}/${totalProducts} products processed\n\n`);
        release()
    } catch (error) {
        release()
        console.log(error);
        // console.log(error, " :: Error caught in product " + product?.sku);
        // res.write(`event: error\nError caught in product ${product?.sku} ${error.message}\n\n`);
        throw new Error(`Error caught in product ${product?.sku}: ${error.message}`);
        // throw error;
    }
}

const createdSunglassProductsUpdate = async (createdProducts, products, csvDir, req) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        let promises = [];
        async function updateProduct(product) {
            const csvRow = products.find((row) => row.sku === product.sku);
            // if (product?.storeId) storeid = product?.storeId
            if (csvRow) {

                let recommended;
                let boughtTogether;

                async function getRecommended() {
                    if (csvRow?.recommended_products?.trim()) {
                        const recommendedSkus = csvRow?.recommended_products?.trim()?.split(",");
                        const recommendedProducts = await service.find({
                            sku: { $in: recommendedSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        recommended = recommendedProducts.map((product) => product?._id);
                    }
                }
                async function getBoughtTogether() {
                    if (csvRow?.bought_together?.trim()) {
                        const boughtTogetherSkus = csvRow?.bought_together?.trim()?.split(",");
                        const boughtTogetherProducts = await service.find({
                            sku: { $in: boughtTogetherSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        boughtTogether = boughtTogetherProducts.map((product) => product?._id);
                    }
                }

                await Promise.all([getRecommended(), getBoughtTogether()])

                await db.Products.findByIdAndUpdate(product?._id, {
                    recommendedProducts: recommended,
                    boughtTogether
                })
                // await product.save();
            }
        }
        for (const product of createdProducts) {
            promises.push(updateProduct(product))
        }
        await Promise.all(promises)
    } catch (error) {
        console.error("Error in cretedProductsUpdate:", error);
    }
};

exports.bulkAccessoryImport = async (req, res) => {
    let csvDir, imagesDir;
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        // const zip = new AdmZip(req.file.path);
        csvDir = "tmp/csv";
        imagesDir = "tmp/images";
        fs.mkdirSync(csvDir, { recursive: true });
        fs.mkdirSync(imagesDir, { recursive: true });
        // const zip = new StreamZip({ file: req.file.path });
        const zip = new StreamZip.async({ file: req.file.path });
        const entries = await zip.entries();
        const files = []
        for (let entry of Object.values(entries)) {
            files.push(entry.name)
            const fileName = entry.name;
            if (fileName.endsWith(".csv")) {
                await zip.extract(fileName, csvDir, err => console.log(err));
            } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
                await zip.extract(fileName, imagesDir, err => console.log(err));
            }
        }

        if (!files.includes("products.csv")) throw new Error("products.csv not found")

        await zip.close();

        res.setHeader("Content-Type", "text/event-stream");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        res.flushHeaders();

        const productsFilePath = `${csvDir}/products.csv`;
        const products = [];
        fs.createReadStream(productsFilePath)
            .pipe(csv({
                mapHeaders: (({ header }) => {
                    if (header.charCodeAt(0) === 0xFEFF) {
                        header = header.substr(1);
                    }
                    return header;
                })
            }))
            .on("data", (data) => products.push(data))
            .on("end", async () => {
                try {
                    const totalProducts = products.length;
                    if (totalProducts == 0) throw new Error("Error: No products in CSV")
                    let processedProducts = 0;
                    let writer = {
                        count: 0
                    }

                    const productImagesDir = "uploads/products/";
                    fs.mkdirSync(productImagesDir, { recursive: true });

                    const createdProducts = [];
                    const productPromises = {};
                    const productHeads = [], headPromises = [];
                    const categoryStrings = [], categoryPromises = [];
                    const brands = [], brandPromises = [];
                    const colors = [], colorCodes = [], colorPromises = [];
                    const labels = [], labelPromises = [];

                    for (const [key, product] of products?.entries()) {
                        if (!product?.sku?.trim()) {
                            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
                        }
                        if (product?.brand?.trim() && product?.model_name?.trim()) {
                            if (!productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`]) {
                                productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`] = key + 1
                            }
                        }
                        //     if (product?.brand?.trim()) {
                        //         if (!brands.includes(product?.brand?.trim())) {
                        //             brands.push(product?.brand?.trim())
                        //         }
                        //     }
                        //     if (product?.categories?.trim()) {
                        //         const catStrings = product.categories?.split(",");
                        //         for (let cat of catStrings) {
                        //             if (!categoryStrings.includes(cat.trim())) {
                        //                 categoryStrings.push(cat.trim())
                        //             }
                        //         }
                        //     }
                        //     if (product?.label?.trim()) {
                        //         if (!labels.includes(product?.label?.trim())) {
                        //             labels.push(product?.label?.trim())
                        //         }
                        //     }
                        //     if (product?.color?.trim()) {
                        //         if (!colors.includes(product?.color?.trim())) {
                        //             colors.push(product?.color?.trim())
                        //             colorCodes.push(product?.html_color_code?.trim())
                        //         }
                        //     }
                    }
                    // let headCount = 0
                    const headRef = {}
                    // for (let head of productHeads) {
                    //     headPromises.push((async () => {
                    //         let productHead = await db.ProductHead.findOne({
                    //             sku: head,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         })

                    //         if (!productHead) {
                    //             productHead = await db.ProductHead.create({
                    //                 sku: head,
                    //                 isDelete: false,
                    //                 name: head,
                    //                 refid: generateUniqueNumber(),
                    //                 products: [],
                    //                 type: "accessory",
                    //                 storeId: storeid
                    //             })
                    //         }
                    //         headRef.push(productHead)
                    //         headCount++
                    //         res.write(`${headCount}/${productHeads.length} imported Product Heads\n\n`);
                    //     })())
                    // }
                    // let brandCount = 0
                    const brandRef = []
                    // for (let brand of brands) {
                    //     brandPromises.push((async () => {
                    //         let brandDetails = await db.Brands.findOne({
                    //             "name.en": brand,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!brandDetails) brandDetails = await createBrand({ name: brand, storeId: storeid });
                    //         brandRef.push(brandDetails)
                    //         brandCount++
                    //         res.write(`${brandCount}/${brands.length} imported Brands\n\n`);
                    //     })())
                    // }

                    const catRef = [];
                    const subCatRef = [];
                    // let catCount = 0
                    // categoryPromises.push((async () => {
                    //     let categories = [];
                    //     let subCategories = [];
                    //     for (let cat of categoryStrings) {
                    //         let cats = cat.split(">");
                    //         let prev = null;
                    //         let root = null
                    //         for (let [key, cat] of cats?.entries()) {

                    //             if (prev) {
                    //                 let category = await db.Category.findOne({
                    //                     "name.en": cat?.trim(),
                    //                     isDelete: false,
                    //                     isRoot: false,
                    //                     parent: prev?._id,
                    //                     root: root?._id,
                    //                     storeId: storeid
                    //                 })
                    //                 if (category) {
                    //                     subCategories.push(category?._id);
                    //                     subCatRef.push(category)
                    //                 }
                    //                 else {
                    //                     const ref = subCatRef.find(item => ((item?.name?.en == cat?.trim() && item?.parent?.toString() == prev?._id?.toString()) && (item?.root?.toString() == root?._id?.toString() && item?.isRoot == false)) && item?.storeId == storeid)
                    //                     if (ref) {
                    //                         category = ref;
                    //                     } else {
                    //                         category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                    //                         subCatRef.push(category)
                    //                         subCategories.push(category?._id);
                    //                     }
                    //                 }
                    //                 prev = category
                    //             } else {
                    //                 let mainCatDetails = await db.Category.findOne({
                    //                     "name.en": cat.trim(),
                    //                     isDelete: false,
                    //                     storeId: storeid
                    //                 });
                    //                 if (mainCatDetails) {
                    //                     categories.push(mainCatDetails?._id);
                    //                     catRef.push(mainCatDetails)
                    //                 }
                    //                 else {
                    //                     const ref = catRef.find(item => (item?.name?.en == cat?.trim() && item?.storeId == storeid) && item?.isDelete == false)
                    //                     if (ref) {
                    //                         mainCatDetails = ref;
                    //                     } else {
                    //                         mainCatDetails = await createCategory({ name: cat?.trim(), storeId: storeid });
                    //                         catRef.push(mainCatDetails)
                    //                         categories.push(mainCatDetails?._id);
                    //                     }
                    //                 }
                    //                 prev = mainCatDetails
                    //                 root = mainCatDetails
                    //             }
                    //         }
                    //         catCount++
                    //         res.write(`${catCount}/${categoryStrings.length} imported Category combinations\n\n`);
                    //     }
                    // })())

                    // let colorCount = 0
                    const colorRef = {};
                    // for (let [key, color] of colors?.entries()) {
                    //     colorPromises.push((async () => {
                    //         let colorDetail = await db.Colors.findOne({
                    //             "name.en": color,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!colorDetail) colorDetail = await createColor(color, colorCodes[key], storeid);
                    //         colorRef.push(colorDetail)
                    //         colorCount++
                    //         res.write(`${colorCount}/${colors.length} imported Colors\n\n`);
                    //     })())
                    // }

                    // let labelCount = 0
                    const labelRef = {}
                    // for (let label of labels) {
                    //     labelPromises.push((async () => {
                    //         let labelDetail = await db.Labels.findOne({
                    //             "name.en": label,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!labelDetail) labelDetail = await createLabel({ name: label, storeId: storeid });
                    //         labelRef.push(labelDetail)
                    //         labelCount++
                    //         res.write(`${labelCount}/${labels.length} imported Labels\n\n`);
                    //     })())
                    // }

                    // await Promise.all([
                    //     ...headPromises,
                    //     ...brandPromises,
                    //     ...categoryPromises,
                    //     ...colorPromises,
                    //     ...labelPromises,
                    // ])

                    res.write(`${0}/${products.length} processing products\n\n`);



                    for (const [key, product] of products?.entries()) {
                        // productPromises.push(
                        await processAccessoryProducts(
                            req, res,
                            product, createdProducts, processedProducts, totalProducts,
                            imagesDir, productImagesDir, writer, key,
                            catRef, subCatRef, headRef, brandRef,
                            colorRef,
                            labelRef, productHeads
                        )
                        // )
                    };

                    // try {
                    //     await Promise.all(productPromises);
                    // } catch (error) {
                    //     throw error;
                    // }
                    const resp = await createdAccessoryProductsUpdate(createdProducts, products, csvDir, req)

                    res.write(`event: message\ndata: Products imported successfully\n\n`);
                    setTimeout(() => {
                        cleanup(req.file.path, csvDir, imagesDir);
                    }, 5000);
                    // Close the connection
                    res.end();

                } catch (error) {
                    console.log(error)
                    setTimeout(() => {
                        res.write(`${error.message?.includes("Error") ? error.message : `Error: ${error.message}`}\n\n`);
                        res.end();
                    }, 1000);
                    return
                }
            })

        // res.status(200).json({ message: "Products imported successfully" });
    } catch (error) {
        console.log("error in bulk import")
        console.log(error, " :: Error caught in product import");
        res.status(422).json({
            error_code: "90",
            error_message: `Import error: ${error.message}`,
        });
    }
};

const processAccessoryProducts = async (
    req, res,
    product, createdProducts,
    processedProducts, totalProducts,
    imagesDir, productImagesDir, writer, key,
    catRef, subCatRef, headRef, brandRef,
    colorRef,
    labelRef, productHeads
) => {
    const release = await mutex.acquire();
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"

        // if (product?.storeId) storeid = product?.storeId;
        if (!product?.sku?.trim()) {
            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
        }
        let newPayload = {};
        let brandDetails = null;


        async function addProductHead() {
            if (product?.brand?.trim() && product?.model_name?.trim()) {
                let productHead;
                productHead = await db.ProductHead.findOne({
                    sku: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                    isDelete: false,
                    storeId: storeid
                })

                if (!productHead) {
                    productHead = await db.ProductHead.create({
                        sku: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                        isDelete: false,
                        name: `${product?.brand?.trim()} ${product?.model_name?.trim()}`,
                        refid: generateUniqueNumber(),
                        products: [],
                        type: "accessory",
                        storeId: storeid
                    })
                }
                newPayload.parent = productHead._id
                if (productHeads[`${product?.brand?.trim()} ${product?.model_name?.trim()}`] === (key + 1)) {
                    newPayload.isDefaultVariant = true
                } else {
                    newPayload.isDefaultVariant = false
                }
                return productHead
            }

        }

        async function addBrand() {
            if (product?.brand?.trim()) {
                if (brandRef[product?.brand?.trim()]) {
                    brandDetails = brandRef[product?.brand?.trim()];
                } else {
                    brandDetails = await db.Brands.findOne({
                        "name.en": product.brand?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!brandDetails) brandDetails = await createBrand({ name: product.brand?.trim(), storeId: storeid });
                newPayload.brand = brandDetails._id
                brandRef[product?.brand?.trim()] = brandDetails
            }

        }

        async function addCategories() {
            if (product?.categories?.trim()) {
                let categories = [];
                let subCategories = [];
                const catStrings = product.categories?.split(",");
                const catPromises = [];
                for (let cat of catStrings) {
                    catPromises.push((async () => {
                        let cats = cat.split(">");
                        let prev = null;
                        let root = null
                        for (let cat of cats) {
                            if (prev) {
                                let category;
                                category = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    isRoot: false,
                                    parent: prev?._id,
                                    root: root?._id,
                                    storeId: storeid
                                })

                                if (category) subCategories.push(category?._id);
                                else {
                                    category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                                    subCategories.push(category?._id);
                                }
                                prev = category
                            } else {
                                let mainCatDetails
                                mainCatDetails = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    storeId: storeid
                                });

                                if (mainCatDetails) categories.push(mainCatDetails?._id);
                                else {
                                    mainCatDetails = await createCategory({ name: cat?.trim(), storeId: storeid });
                                    categories.push(mainCatDetails?._id);
                                }
                                prev = mainCatDetails
                                root = mainCatDetails
                            }
                        }
                    })())
                }
                await Promise.all(catPromises);
                // console.log("categories", categories)
                newPayload.category = Array.from(new Set(categories.map(i => i?.toString())));
                newPayload.subCategory = Array.from(new Set(subCategories.map(i => i?.toString())));
            }

        }

        async function addColor() {
            if (product?.color?.trim()) {
                let colorDetail;
                if (colorRef[product?.color?.trim()]) {
                    colorDetail = colorRef[product?.color?.trim()];
                } else {
                    colorDetail = await db.Colors.findOne({
                        "name.en": product.color?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!colorDetail) colorDetail = await createColor(product.color?.trim(), product.html_color_code, storeid);
                newPayload.color = colorDetail._id
                colorRef[product?.color?.trim()] = colorDetail
            }

        }

        async function addLabel() {
            if (product?.label?.trim()) {
                let labelDetail;
                if (labelRef[product?.label?.trim()]) {
                    labelDetail = labelRef[product?.label?.trim()];
                } else {
                    labelDetail = await db.Labels.findOne({
                        "name.en": product.label?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
                newPayload.label = labelDetail._id
                labelRef[product?.label?.trim()] = labelDetail
            }

        }

        async function uploadImages() {
            if (product?.product_images?.trim()) {
                let files = [];
                let images = product?.product_images?.split(",");
                for (let _image of images) {
                    if (_image) {
                        if (_image.includes("/")) {
                            files.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/images/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            files.push(key)
                        }
                    }
                }
                newPayload.images = files
            }

        }

        async function uploadDescriptionImages() {
            if (product?.description_images?.trim()) {
                let descImages = [];
                let descriptionImages = product?.description_images?.split(",");
                for (let _image of descriptionImages) {
                    if (_image) {
                        if (_image.includes("/")) {
                            descImages.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/descriptionImages/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            descImages.push(key);
                        }
                    }
                }
                newPayload.descriptionImages = descImages;
            }

        }

        async function uploadThumbnail() {
            if (product?.thumb_image?.trim()) {
                if (product.thumb_image.includes("/")) {
                    newPayload.thumbnail = product.thumb_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.thumb_image);
                    const name = `products/${product?.sku}/thumbnails/${Date.now()}-${product.thumb_image}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload.thumbnail = key
                }
            }

        }


        async function getProductDetails() {
            let productDetail = await service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid })
            if (productDetail && productDetail.productType != "accessory") throw new Error(`SKU Exist in product type ${productDetail?.productType == "frame" ? "sunglass" : productDetail.productType}`);
            return productDetail
        }

        async function uploadOgImage() {
            if (product?.meta_og_image?.trim()) {
                if (product.meta_og_image.includes("/")) {
                    newPayload["seoDetails.ogImage"] = product.meta_og_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.meta_og_image?.trim());
                    const name = `products/${product?.sku}/ogImages/${Date.now()}-${product?.meta_og_image?.trim()}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload["seoDetails.ogImage"] = key
                }
            }

        }

        const [
            productDetail,
            productHead
        ] = await Promise.all([
            getProductDetails(),
            addProductHead(),
            addBrand(),
            addCategories(),
            addColor(),
            addLabel(),
            uploadImages(),
            uploadDescriptionImages(),
            uploadThumbnail(),
            uploadOgImage(),
        ])


        if (product?.gender) {
            let gender = product?.gender?.split(",").map((gender) => gender.trim()) || [];
            newPayload.gender = gender
        }

        if (product?.sku?.trim()) newPayload.sku = product?.sku?.trim();
        if (product?.model_name?.trim()) newPayload.modelName = product?.model_name?.trim();
        if (product?.color_code?.trim()) newPayload.colorCode = product?.color_code?.trim();

        if (product?.name_en?.trim()) newPayload.name = { en: product?.name_en?.trim() };
        if (product?.name_ar?.trim()) newPayload.name = { en: newPayload?.name?.en ?? productDetail?.name.en, ar: product?.name_ar?.trim() };
        if (product?.description_en?.trim()) newPayload.description = { en: product?.description_en?.trim() };
        if (product?.description_ar?.trim()) newPayload.description = { en: newPayload.description?.en, ar: product?.description_ar?.trim() };
        if (product?.description_two_en?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim() };
        if (product?.description_two_ar?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim(), ar: product?.description_two_ar?.trim() };
        if (product?.stock?.trim()) {
            if (isNaN(product?.stock)) throw new Error(`Stock must be numeric for ${product?.sku}`);
            newPayload.stock = Number(product?.stock);
            newPayload.customizable = false
        };
        if (product?.position?.trim()) {
            if (isNaN(product?.position)) throw new Error(`Position must be numeric for ${product?.sku}`);
            newPayload.position = Number(product?.position);
        }
        if (product?.price?.trim()) {
            if (isNaN(product?.price)) throw new Error(`Price must be numeric for ${product?.sku}`);
            newPayload.price = { aed: Number(product?.price) };
            newPayload.customizable = false
        };
        if (product?.offer_price?.trim()) {
            if (isNaN(product?.offer_price)) throw new Error(`Offer_price must be numeric for ${product?.sku}`);
            newPayload.offerPrice = { aed: Number(product?.offer_price) };
            newPayload.customizable = false
        }
        if (product?.supplier_sku?.trim()) newPayload.supplierSku = product?.supplier_sku?.trim();
        if (product?.upc?.trim()) newPayload.upc = product?.upc?.trim();
        if (product?.weight?.trim()) {
            if (isNaN(product?.weight)) throw new Error(`Weight must be numeric for ${product?.sku}`);
            newPayload.weight = Number(product?.weight);
        }
        if (product?.cashbackPercentage?.trim()) {
            if (isNaN(product?.cashbackPercentage)) throw new Error(`CashbackPercentage must be numeric for ${product?.sku}`);
            newPayload.cashbackPercentage = Number(product?.cashbackPercentage);
        }
        if (product?.meta_title_en?.trim()) newPayload["seoDetails.title.en"] = product?.meta_title_en?.trim()
        if (product?.meta_title_ar?.trim()) newPayload["seoDetails.title.ar"] = product?.meta_title_ar?.trim();
        if (product?.meta_description_en?.trim()) newPayload["seoDetails.description.en"] = product?.meta_description_en?.trim();
        if (product?.meta_description_ar?.trim()) newPayload["seoDetails.description.ar"] = product?.meta_description_ar?.trim();
        if (product?.meta_keywords_en?.trim()) newPayload["seoDetails.keywords.en"] = product?.meta_keywords_en?.trim();
        if (product?.meta_keywords_ar?.trim()) newPayload["seoDetails.keywords.ar"] = product?.meta_keywords_ar?.trim();
        if (product?.meta_canonical_en?.trim()) newPayload["seoDetails.canonical.en"] = product?.meta_canonical_en?.trim();
        if (product?.meta_canonical_ar?.trim()) newPayload["seoDetails.canonical.ar"] = product?.meta_canonical_ar?.trim();


        newPayload.productType = "accessory";
        if (product?.offer_price?.trim() || product?.price?.trim()) {
            newPayload.sellingPrice = Number(product?.offer_price) || Number(product?.price);
        }

        if (newPayload.name?.en) {
            newPayload.slug = productDetail?.name?.en != newPayload.name?.en
                ? await slug.createSlug(db.Products, newPayload.name?.en, {
                    slug: await slug.generateSlug(newPayload.name?.en),
                    storeId: storeid,
                    isDelete: false
                })
                : productDetail?.slug;
        }

        newPayload.refid = productDetail ? productDetail?.refid : generateUniqueNumber();

        if (product?.is_customizable?.trim()) {
            if (product?.is_customizable?.trim() != "1" && product?.is_customizable?.trim() != "0") throw new Error(`is_customizable must be either 1 or 0 for ${product?.sku}`);
            newPayload.customizable = product?.is_customizable?.trim() == "1" ? true : false;
        };
        if (product?.add_to_cart?.trim()) {
            if (product?.add_to_cart?.trim() != "1" && product?.add_to_cart?.trim() != "0") throw new Error(`add_to_cart must be either 1 or 0 for ${product?.sku}`);
            newPayload.isAddToCart = product?.add_to_cart?.trim() == "1" ? true : false
        };
        if (product?.choose_lens?.trim()) {
            if (product?.choose_lens?.trim() != "1" && product?.choose_lens?.trim() != "0") throw new Error(`choose_lens must be either 1 or 0 for ${product?.sku}`);
            newPayload.isChooseLens = product?.choose_lens?.trim() == "1" ? true : false
        };
        if (product?.is_active?.trim()) {
            if (product?.is_active?.trim() != "1" && product?.is_active?.trim() != "0") throw new Error(`is_active must be either 1 or 0 for ${product?.sku}`);
            newPayload.isActive = product?.is_active?.trim() == "1" ? true : false
        };
        // if (product?.is_default_variant?.trim()) {
        //     if (product?.is_default_variant?.trim() != "1" && product?.is_default_variant?.trim() != "0") throw new Error(`is_default_variant must be either 1 or 0 for ${product?.sku}`);
        //     newPayload.isDefaultVariant = product?.is_default_variant?.trim() == "1" ? true : false
        // };
        if (product?.show_discount_percentage?.trim()) {
            if (product?.show_discount_percentage?.trim() != "1" && product?.show_discount_percentage?.trim() != "0") throw new Error(`show_discount_percentage must be either 1 or 0 for ${product?.sku}`);
            newPayload.showDiscountPercentage = product?.show_discount_percentage?.trim() == "1" ? true : false
        };
        if (product?.is_new_arrival?.trim()) {
            if (product?.is_new_arrival?.trim() != "1" && product?.is_new_arrival?.trim() != "0") throw new Error(`is_new_arrival must be either 1 or 0 for ${product?.sku}`);
            newPayload.isNewArrival = product?.is_new_arrival?.trim() == "1" ? true : false
        };
        if (product?.is_returnable?.trim()) {
            if (product?.is_returnable?.trim() != "1" && product?.is_returnable?.trim() != "0") throw new Error(`is_returnable must be either 1 or 0 for ${product?.sku}`);
            newPayload.isReturnable = product?.is_returnable?.trim() == "1" ? true : false
        };
        if (product?.virtual_try_available?.trim()) {
            if (product?.virtual_try_available?.trim() != "1" && product?.virtual_try_available?.trim() != "0") throw new Error(`virtual_try_available must be either 1 or 0 for ${product?.sku}`);
            newPayload.isVirtualTry = product?.virtual_try_available?.trim() == "1" ? true : false
        };
        if (product?.isTaxIncluded?.trim()) {
            if (product?.isTaxIncluded?.trim() != "1" && product?.isTaxIncluded?.trim() != "0") throw new Error(`isTaxIncluded must be either 1 or 0 for ${product?.sku}`);
            newPayload.isTaxIncluded = product?.isTaxIncluded?.trim() == "1" ? true : false
        };
        if (product?.isCashbackEnabled?.trim()) {
            if (product?.isCashbackEnabled?.trim() != "1" && product?.isCashbackEnabled?.trim() != "0") throw new Error(`isCashbackEnabled must be either 1 or 0 for ${product?.sku}`);
            newPayload.isCashbackEnabled = product?.isCashbackEnabled?.trim() == "1" ? true : false;
        };

        let i = 1
        const techDetails = [];
        while (true) {
            if (product?.[`t${i}_en`]) {
                techDetails.push({
                    title: {
                        en: product[`t${i}_en`],
                        ar: product[`t${i}_ar`],
                    },
                    description: {
                        en: product[`d${i}_en`],
                        ar: product[`d${i}_ar`],
                    }
                })
                i++
            } else {
                break;
            }
        }

        if (i !== 1) {
            newPayload.technicalInfo = techDetails
        }

        async function addProducts(newProduct) {
            if (productHead) {
                let products = productHead?.products || [];
                if (!products.map(item => item?.toString()).includes(newProduct?._id.toString())) {
                    products?.push(newProduct?._id)
                    await db.ProductHead.updateOne({ _id: newProduct?.parent }, { products });
                }
            }
        }
        newPayload.storeId = storeid
        let newProduct;
        if (productDetail) {
            const [newProd] = await Promise.all([
                service.update({ _id: productDetail?._id }, newPayload),
                addProducts(productDetail)
            ])
            newProduct = newProd
        } else {
            if (!brandDetails) {
                throw new Error(`Brand is required`)
            } else if (!product?.model_name?.trim()) {
                throw new Error(`Model name is required`)
            }
            if (!product?.supplier_sku?.trim()) {
                throw new Error(`Supplier SKU is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.upc?.trim()) {
                throw new Error(`UPC is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.thumb_image?.trim()) {
                throw new Error(`Thumbnail is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.stock?.trim()) {
                throw new Error(`Stock is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.price?.trim()) {
                throw new Error(`Price is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.weight?.trim()) {
                throw new Error(`Weight is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.is_new_arrival?.trim()) {
                throw new Error(`is_new_arrival required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.description_en?.trim()) {
                throw new Error(`description_en required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            newProduct = await service.create(newPayload);
            await addProducts(newProduct)
        }

        newProduct.key = key

        createdProducts.push(newProduct);
        writer.count += 1
        res.write(`${parseInt(writer.count)}/${totalProducts} products processed\n\n`);
        release()
    } catch (error) {
        release()
        console.log(error);
        // console.log(error, " :: Error caught in product " + product?.sku);
        // res.write(`event: error\nError caught in product ${product?.sku} ${error.message}\n\n`);
        throw new Error(`Error caught in product ${product?.sku}: ${error.message}`);
        // throw error;
    }
}

const createdAccessoryProductsUpdate = async (createdProducts, products, csvDir, req) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        let promises = [];
        async function updateProduct(product) {
            const csvRow = products.find((row) => row.sku === product.sku);
            // if (product?.storeId) storeid = product?.storeId
            if (csvRow) {

                let recommended;
                let boughtTogether;

                async function getRecommended() {
                    if (csvRow?.recommended_products?.trim()) {
                        const recommendedSkus = csvRow?.recommended_products?.trim()?.split(",");
                        const recommendedProducts = await service.find({
                            sku: { $in: recommendedSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        recommended = recommendedProducts.map((product) => product?._id);
                    }
                }
                async function getBoughtTogether() {
                    if (csvRow?.bought_together?.trim()) {
                        const boughtTogetherSkus = csvRow?.bought_together?.trim()?.split(",");
                        const boughtTogetherProducts = await service.find({
                            sku: { $in: boughtTogetherSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        boughtTogether = boughtTogetherProducts.map((product) => product?._id);
                    }
                }

                await Promise.all([getRecommended(), getBoughtTogether()])

                await db.Products.findByIdAndUpdate(product?._id, {
                    recommendedProducts: recommended,
                    boughtTogether
                })
                // await product.save();
            }
        }
        for (const product of createdProducts) {
            promises.push(updateProduct(product))
        }
        await Promise.all(promises)
    } catch (error) {
        console.error("Error in cretedProductsUpdate:", error);
    }
};

exports.bulkContactLensImport = async (req, res) => {
    let csvDir, imagesDir;
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        // const zip = new AdmZip(req.file.path);
        csvDir = "tmp/csv";
        imagesDir = "tmp/images";
        fs.mkdirSync(csvDir, { recursive: true });
        fs.mkdirSync(imagesDir, { recursive: true });
        // const zip = new StreamZip({ file: req.file.path });
        const zip = new StreamZip.async({ file: req.file.path });
        const entries = await zip.entries();
        const files = []
        for (let entry of Object.values(entries)) {
            files.push(entry.name)
            const fileName = entry.name;
            if (fileName.endsWith(".csv")) {
                await zip.extract(fileName, csvDir, err => console.log(err));
            } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
                await zip.extract(fileName, imagesDir, err => console.log(err));
            }
        }

        if (!files.includes("products.csv")) throw new Error("products.csv not found")

        await zip.close();

        res.setHeader("Content-Type", "text/event-stream");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        res.flushHeaders();

        const productsFilePath = `${csvDir}/products.csv`;
        const products = [];
        // let encoding = 'utf8'
        // const fileInfo = await languageEncoding(productsFilePath)
        // console.log(fileInfo)
        // if(fileInfo.encoding == 'UTF-16LE') encoding = 'utf16le'
        const fileStream = fs.createReadStream(productsFilePath);
        const client = {
            disconnected: false
        };
        req.on('close', () => {
            console.log('Client disconnected. Aborting file read.');
            client.disconnected = true;
            fileStream.destroy(); // Stop reading the file
            res.end(); // Close the stream to avoid memory leaks
        });
        // console.log(fileStream)

        fileStream
            // .pipe(iconv.decodeStream('utf8'))
            .pipe(csv({
                mapHeaders: (({ header }) => {
                    if (header.charCodeAt(0) === 0xFEFF) {
                        header = header.substr(1);
                    }
                    return header;
                })
            }))
            .on("data", (data) => products.push(data))
            .on("end", async () => {
                try {
                    if (client.disconnected) return;
                    const totalProducts = products.length;
                    if (totalProducts == 0) throw new Error("Error: No products in CSV")
                    let processedProducts = 0;
                    let writer = {
                        count: 0
                    }

                    const productImagesDir = "uploads/products/";
                    fs.mkdirSync(productImagesDir, { recursive: true });

                    const createdProducts = [];
                    const productHeads = {};
                    const categoryStrings = []
                    const brands = [], brandPromises = [];
                    const colors = [], colorCodes = []
                    const sphs = []
                    const cyls = []
                    const axis = []
                    const sizes = [], sizePromises = []
                    const labels = []
                    let subBrands = []

                    for (const [key, product] of products?.entries()) {
                        if (!product?.sku?.trim()) {
                            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
                        }
                        if (product?.brand?.trim() && product?.categories?.trim()) {
                            let category = product?.categories?.trim()?.split(",")[0]?.split(">")
                            let subCategory = ""
                            if (category?.length > 1) category = category[1]?.trim()
                            else if (category?.length == 1) { } else {
                                if (product?.sub_brand?.trim()) subCategory = product?.sub_brand?.trim();

                                let head = `${product?.brand?.trim()} ${category}`
                                if (subCategory) head = `${product?.brand?.trim()} ${category} ${subCategory?.trim()}`;

                                if (!productHeads[head]) {
                                    productHeads[head] = key + 1
                                }
                            };

                        }
                        if (product?.brand?.trim()) {
                            if (product?.sub_brand?.trim()) {
                                if (!subBrands[product?.brand?.trim()]) {
                                    subBrands[product?.brand?.trim()] = [product?.sub_brand?.trim()]
                                } else {
                                    if (!subBrands[product?.brand?.trim()].includes(product?.sub_brand?.trim())) {
                                        subBrands[product?.brand?.trim()].push(product?.sub_brand?.trim())
                                    }
                                }
                            }
                            if (!brands.includes(product?.brand?.trim())) {
                                brands.push(product?.brand?.trim())
                            }
                        }
                        if (product?.categories?.trim()) {
                            const catStrings = product.categories?.split(",");
                            for (let cat of catStrings) {
                                if (!categoryStrings.includes(cat.trim())) {
                                    categoryStrings.push(cat.trim())
                                }
                            }
                        }
                        if (product?.sphValues?.trim()) {
                            const sphValues = product?.sphValues?.split(",");
                            for (let sph of sphValues) {
                                if (!sphs.includes(sph.trim())) {
                                    sphs.push(sph.trim())
                                }
                            }
                        }
                        if (product?.cylValues?.trim()) {
                            const cylValues = product?.cylValues?.split(",");
                            for (let cyl of cylValues) {
                                if (!cyls.includes(cyl.trim())) {
                                    cyls.push(cyl.trim())
                                }
                            }
                        }
                        if (product?.axisValues?.trim()) {
                            const axisValues = product?.axisValues?.split(",");
                            for (let item of axisValues) {
                                if (!axis.includes(item?.trim())) {
                                    axis.push(item?.trim())
                                }
                            }
                        }
                        if (product?.size?.trim()) {
                            if (!sizes.includes(product?.size?.trim())) {
                                sizes.push(product?.size?.trim())
                            }
                        }
                        if (product?.label?.trim()) {
                            if (!labels.includes(product?.label?.trim())) {
                                labels.push(product?.label?.trim())
                            }
                        }
                        if (product?.color?.trim()) {
                            if (!colors.includes(product?.color?.trim())) {
                                colors.push(product?.color?.trim())
                                colorCodes.push(product?.html_color_code?.trim())
                            }
                        }
                    }

                    let headCount = 0
                    const headRef = []
                    // for (let head of productHeads) {
                    //     headPromises.push((async () => {
                    //         let productHead = await db.ProductHead.findOne({
                    //             sku: head,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         })

                    //         if (!productHead) {
                    //             productHead = await db.ProductHead.create({
                    //                 sku: head,
                    //                 isDelete: false,
                    //                 name: head,
                    //                 refid: generateUniqueNumber(),
                    //                 products: [],
                    //                 type: "contactLens",
                    //                 storeId: storeid
                    //             })
                    //         }
                    //         headRef.push(productHead)
                    //         headCount++
                    //         res.write(`${headCount}/${productHeads.length} imported Product Heads\n\n`);
                    //     })())
                    // }
                    let brandCount = 0
                    const brandRef = {}
                    for (let brand of brands) {
                        brandPromises.push((async () => {
                            let brandDetails = await db.Brands.findOne({
                                "name.en": brand,
                                isDelete: false,
                                storeId: storeid
                            });

                            if (brandDetails && !brandRef[brand]) brandRef[brand] = brandDetails
                        })())
                    }

                    const catRef = [];
                    const subCatRef = [];
                    let catCount = 0
                    // categoryPromises.push((async () => {
                    //     let categories = [];
                    //     let subCategories = [];
                    //     for (let cat of categoryStrings) {
                    //         let cats = cat.split(">");
                    //         let prev = null;
                    //         let root = null
                    //         for (let [key, cat] of cats?.entries()) {

                    //             if (prev) {
                    //                 let category = await db.Category.findOne({
                    //                     "name.en": cat?.trim(),
                    //                     isDelete: false,
                    //                     isRoot: false,
                    //                     parent: prev?._id,
                    //                     root: root?._id,
                    //                     storeId: storeid
                    //                 })
                    //                 if (category) {
                    //                     subCategories.push(category?._id);
                    //                     subCatRef.push(category)
                    //                 }
                    //                 else {
                    //                     const ref = subCatRef.find(item => ((item?.name?.en == cat?.trim() && item?.parent?.toString() == prev?._id?.toString()) && (item?.root?.toString() == root?._id?.toString() && item?.isRoot == false)) && item?.storeId == storeid)
                    //                     if (ref) {
                    //                         category = ref;
                    //                     } else {
                    //                         category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                    //                         subCatRef.push(category)
                    //                         subCategories.push(category?._id);
                    //                     }
                    //                 }
                    //                 prev = category
                    //             } else {
                    //                 let mainCatDetails = await db.Category.findOne({
                    //                     "name.en": cat?.trim(),
                    //                     isDelete: false,
                    //                     storeId: storeid
                    //                 });
                    //                 if (mainCatDetails) {
                    //                     categories.push(mainCatDetails?._id);
                    //                     catRef.push(mainCatDetails)
                    //                 }
                    //                 else {
                    //                     const ref = catRef.find(item => (item?.name?.en == cat?.trim() && item?.storeId == storeid) && item?.isDelete == false)
                    //                     if (ref) {
                    //                         mainCatDetails = ref;
                    //                     } else {
                    //                         mainCatDetails = await createCategory({ name: cat, storeId: storeid });
                    //                         catRef.push(mainCatDetails)
                    //                         categories.push(mainCatDetails?._id);
                    //                     }
                    //                 }
                    //                 prev = mainCatDetails
                    //                 root = mainCatDetails
                    //             }
                    //         }
                    //         catCount++
                    //         res.write(`${catCount}/${categoryStrings.length} imported Category combinations\n\n`);
                    //     }
                    // })())

                    let sphCount = 0
                    const sphRef = {}
                    // sphPromises.push((async () => {
                    //     let promise = [];
                    //     for (let power of sphs) promise.push(db.ContactLensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of sphs.entries()) {
                    //         let powerDetails = details[key]
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.ContactLensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, refid, storeId: storeid })
                    //         }
                    //         sphRef.push(powerDetails)
                    //         sphCount++
                    //         res.write(`${sphCount}/${sphs.length} imported Sph values\n\n`);
                    //     }
                    // })())

                    let cylCount = 0
                    const cylRef = {}
                    // cylPromises.push((async () => {
                    //     let promise = [];
                    //     for (let power of cyls) promise.push(db.ContactLensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of cyls.entries()) {
                    //         let powerDetails = details[key]
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.ContactLensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                    //         }
                    //         cylRef.push(powerDetails)
                    //         cylCount++
                    //         res.write(`${cylCount}/${cyls.length} imported Cyl values\n\n`);
                    //     }
                    // })())

                    let axisCount = 0
                    const axisRef = {}
                    // axisPromises.push((async () => {
                    //     let promise = [];
                    //     for (let power of axis) promise.push(db.ContactLensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid }));
                    //     const details = await Promise.all(promise);
                    //     for (let [key, power] of axis.entries()) {
                    //         let powerDetails = details[key]
                    //         if (!powerDetails) {
                    //             let refid = generateUniqueNumber();
                    //             powerDetails = await db.ContactLensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                    //         }
                    //         axisRef.push(powerDetails)
                    //         axisCount++
                    //         res.write(`${axisCount}/${axis.length} imported Axis values\n\n`);
                    //     }
                    // })())

                    let colorCount = 0
                    const colorRef = {}
                    // for (let [key, color] of colors?.entries()) {
                    //     colorPromises.push((async () => {
                    //         let colorDetail = await db.Colors.findOne({
                    //             "name.en": color,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!colorDetail) colorDetail = await createColor(color, colorCodes[key], storeid);
                    //         colorRef.push(colorDetail)
                    //         colorCount++
                    //         res.write(`${colorCount}/${colors.length} imported Colors\n\n`);
                    //     })())
                    // }

                    let labelCount = 0
                    const labelRef = {}
                    // for (let label of labels) {
                    //     labelPromises.push((async () => {
                    //         let labelDetail = await db.Labels.findOne({
                    //             "name.en": label,
                    //             isDelete: false,
                    //             storeId: storeid
                    //         });
                    //         if (!labelDetail) labelDetail = await createLabel({ name: label, storeId: storeid });
                    //         labelRef.push(labelDetail)
                    //         labelCount++
                    //         res.write(`${labelCount}/${labels.length} imported Labels\n\n`);
                    //     })())
                    // }

                    let sizeCount = 0
                    const sizeRef = {}
                    for (let size of sizes) {
                        sizePromises.push((async () => {
                            let sizeDetails = await db.ContactSize.findOne({
                                "name": size?.trim(),
                                isDelete: false,
                                storeId: storeid
                            });
                            if (sizeDetails && !sizeRef[size?.trim()]) sizeRef[size?.trim()] = sizeDetails
                        })())
                    }


                    await Promise.all([
                        // ...headPromises,
                        ...brandPromises,
                        // ...categoryPromises,
                        // ...colorPromises,
                        // ...labelPromises,
                        ...sizePromises,
                        // ...sphPromises,
                        // ...axisPromises,
                        // ...cylPromises,
                    ])

                    res.write(`${0}/${products.length} processing products\n\n`);



                    for (const [key, product] of products?.entries()) {
                        // productPromises.push(
                        await processContactLensProducts(
                            req, res,
                            product, createdProducts, processedProducts, totalProducts,
                            imagesDir, productImagesDir, writer, key,
                            catRef, subCatRef, headRef, brandRef, colorRef,
                            labelRef, sizeRef, sphRef, cylRef, axisRef, productHeads
                        )
                        // )
                    };

                    // try {
                    //     await Promise.all(productPromises);
                    // } catch (error) {
                    //     throw error;
                    // }
                    const resp = await createdContactLensProductsUpdate(createdProducts, products, csvDir, req)

                    res.write(`event: message\ndata: Products imported successfully\n\n`);
                    setTimeout(() => {
                        cleanup(req.file.path, csvDir, imagesDir);
                    }, 5000);
                    // Close the connection
                    res.end();

                } catch (error) {
                    console.log(error)
                    setTimeout(() => {
                        res.write(`${error.message?.includes("Error") ? error.message : `Error: ${error.message}`}\n\n`);
                        res.end();
                    }, 1000);
                    return
                }
            })

        // res.status(200).json({ message: "Products imported successfully" });
    } catch (error) {
        console.log("error in bulk import")
        console.log(error, " :: Error caught in product import");
        res.status(422).json({
            error_code: "90",
            error_message: `Import Error: ${error.message}`,
        });
    }
};

const processContactLensProducts = async (
    req, res,
    product, createdProducts,
    processedProducts, totalProducts,
    imagesDir, productImagesDir, writer, key,
    catRef, subCatRef, headRef, brandRef, colorRef,
    labelRef, sizeRef, sphRef, cylRef, axisRef, productHeads
) => {
    const release = await mutex.acquire();
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        console.log(product?.sku?.trim())
        // if (product?.storeId) storeid = product?.storeId;
        if (!product?.sku?.trim()) {
            throw new Error(`SKU not found for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
        }
        let newPayload = {};
        let brandDetails = null;
        let headCategory = null;


        async function addProductHead() {
            // console.time(`productHead`)
            if (product?.brand?.trim() && product?.categories?.trim()) {
                let productHead;

                let category = product?.categories?.trim()?.split(",")[0]?.split(">")
                let subCategory = ""
                if (category?.length > 1) headCategory = category[1]?.trim()
                else if (category?.length == 1) return

                if (product?.sub_brand?.trim()) subCategory = product?.sub_brand?.trim();

                let head = `${product?.brand?.trim()} ${headCategory}`
                if (subCategory) head = `${product?.brand?.trim()} ${headCategory} ${subCategory?.trim()}`;

                productHead = await db.ProductHead.findOne({
                    sku: head,
                    isDelete: false,
                    storeId: storeid
                })

                if (!productHead) {
                    productHead = await db.ProductHead.create({
                        sku: head,
                        isDelete: false,
                        name: head,
                        refid: generateUniqueNumber(),
                        products: [],
                        type: "contactLens",
                        storeId: storeid
                    })
                }
                newPayload.parent = productHead._id

                if (productHeads[head] === (key + 1)) {
                    newPayload.isDefaultVariant = true
                } else {
                    newPayload.isDefaultVariant = false
                }

                // console.timeEnd(`productHead`)
                return productHead
            }
            // console.timeEnd(`productHead`)
        }

        async function addBrand() {
            // console.time(`brand`)
            if (product?.brand?.trim()) {
                if (brandRef[product?.brand?.trim()]) {
                    brandDetails = brandRef[product?.brand?.trim()];
                } else {
                    brandDetails = await db.Brands.findOne({
                        "name.en": product.brand?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!brandDetails) brandDetails = await createBrand({ name: product.brand?.trim(), storeId: storeid });
                newPayload.brand = brandDetails._id
                brandRef[product?.brand?.trim()] = brandDetails
            }
            if (product?.sub_brand?.trim()) {
                let subBrand = await db.Brands.findOne({
                    "name.en": product.sub_brand?.trim(),
                    isDelete: false,
                    storeId: storeid,
                    isMain: false,
                    parent: brandDetails._id
                });
                if (!subBrand) {
                    subBrand = await createBrand({ name: product.sub_brand?.trim(), storeId: storeid, isMain: false, parent: brandDetails._id });
                    brandDetails.subBrands.push(subBrand._id)
                    await db.Brands.updateOne({ _id: brandDetails._id }, { subBrands: brandDetails.subBrands })
                }

                newPayload.subBrand = subBrand._id
            }
            // console.timeEnd(`brand`)
        }

        async function addSph() {
            // console.time(`sph`)
            if (product?.sphValues?.trim()) {
                let contactSph = [];
                const values = product?.sphValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (sphRef[power?.trim()]) {
                        return sphRef[power?.trim()];
                    } else {
                        const item = await db.ContactLensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid })
                        sphRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key]
                    if (!powerDetails) {
                        let refid = (await db.ContactLensPower.count({})) + 1;
                        powerDetails = await db.ContactLensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, refid, storeId: storeid })
                    }
                    contactSph.push(powerDetails._id)
                }
                newPayload.contactSph = contactSph
            }
            // console.timeEnd(`sph`)
        }

        async function addCyl() {
            // console.time(`cyl`)
            if (product?.cylValues?.trim()) {
                let contactCyl = [];
                const values = product?.cylValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (cylRef[power?.trim()]) {
                        return cylRef[power?.trim()];
                    } else {
                        const item = await await db.ContactLensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid })
                        cylRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key]
                    if (!powerDetails) {
                        let refid = (await db.ContactLensPower.count({})) + 1;
                        powerDetails = await db.ContactLensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                    }
                    contactCyl.push(powerDetails._id)
                }
                newPayload.contactCyl = contactCyl
            }
            // console.timeEnd(`cyl`)
        }

        async function addAxis() {
            // console.time(`axis`)
            if (product?.axisValues?.trim()) {
                let contactAxis = [];
                const values = product?.axisValues?.split(",")
                let promise = [];
                for (let power of values) promise.push((async () => {
                    if (axisRef[power?.trim()]) {
                        return axisRef[power?.trim()];
                    } else {
                        const item = await db.ContactLensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid })
                        axisRef[power?.trim()] = item;
                        return item
                    }
                })());
                const details = await Promise.all(promise);
                for (let [key, power] of values.entries()) {
                    let powerDetails = details[key]
                    if (!powerDetails) {
                        let refid = (await db.ContactLensPower.count({})) + 1;
                        powerDetails = await db.ContactLensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                    }
                    contactAxis.push(powerDetails._id)
                }
                newPayload.contactAxis = contactAxis
            }
            // console.timeEnd(`axis`)
        }

        async function addCategories() {
            // console.time(`category`)
            if (product?.categories?.trim()) {
                let categories = [];
                let subCategories = [];
                const catStrings = product.categories?.split(",");
                const catPromises = [];
                for (let cat of catStrings) {
                    catPromises.push((async () => {
                        let cats = cat.split(">");
                        let prev = null;
                        let root = null
                        for (let cat of cats) {
                            if (prev) {
                                let category;

                                category = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    isRoot: false,
                                    parent: prev?._id,
                                    root: root?._id,
                                    storeId: storeid
                                })

                                if (category) subCategories.push(category?._id);
                                else {
                                    category = await createCategory({ name: cat?.trim(), parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                                    subCategories.push(category?._id);
                                }
                                prev = category
                            } else {
                                let mainCatDetails

                                mainCatDetails = await db.Category.findOne({
                                    "name.en": cat?.trim(),
                                    isDelete: false,
                                    storeId: storeid
                                });

                                if (mainCatDetails) categories.push(mainCatDetails?._id);
                                else {
                                    mainCatDetails = await createCategory({ name: cat?.trim(), storeId: storeid });
                                    categories.push(mainCatDetails?._id);
                                }
                                prev = mainCatDetails
                                root = mainCatDetails
                            }
                        }
                    })())
                }
                await Promise.all(catPromises);
                // console.log("categories", categories)
                newPayload.category = Array.from(new Set(categories.map(i => i?.toString())));
                newPayload.subCategory = Array.from(new Set(subCategories.map(i => i?.toString())));
            }
            // console.timeEnd(`category`)
        }

        async function addColor() {
            // console.time(`color`)
            if (product?.color?.trim()) {
                let colorDetail;
                if (colorRef[product?.color?.trim()]) {
                    colorDetail = colorRef[product?.color?.trim()];
                } else {
                    colorDetail = await db.Colors.findOne({
                        "name.en": product.color?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!colorDetail) colorDetail = await createColor(product.color?.trim(), product.html_color_code, storeid);
                colorRef[product?.color?.trim()] = colorDetail
                newPayload.color = colorDetail._id
            }
            // console.timeEnd(`color`)
        }

        async function addSize() {
            // console.time(`size`)
            if (product?.size?.trim()) {
                let sizeDetails;
                if (sizeRef[product?.size?.trim()]) {
                    sizeDetails = sizeRef[product?.size?.trim()];
                } else {
                    sizeDetails = await db.ContactSize.findOne({
                        "name": product.size?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!sizeDetails) sizeDetails = await createContactSize({ name: product.size?.trim(), storeId: storeid });
                sizeRef[product?.size?.trim()] = sizeDetails
                newPayload.contactSize = sizeDetails._id
            }
            // console.timeEnd(`size`)
        }

        async function addLabel() {
            // console.time(`label`)
            if (product?.label?.trim()) {
                let labelDetail;
                if (labelRef[product?.label?.trim()]) {
                    labelDetail = labelRef[product?.label?.trim()];
                } else {
                    labelDetail = await db.Labels.findOne({
                        "name.en": product.label?.trim(),
                        isDelete: false,
                        storeId: storeid
                    });
                }
                if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
                labelRef[product?.label?.trim()] = labelDetail
                newPayload.label = labelDetail._id
            }
            // console.timeEnd(`label`)
        }

        async function uploadImages() {

            if (product?.product_images?.trim()) {
                let files = [];
                let images = product?.product_images?.split(",");
                for (let _image of images) {
                    if (_image) {
                        if (_image.includes("/")) {
                            files.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/images/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            files.push(key)
                        }
                    }
                }
                newPayload.images = files
            }
        }

        async function uploadDescriptionImages() {

            if (product?.description_images?.trim()) {
                let descImages = [];
                let descriptionImages = product?.description_images?.split(",");
                for (let _image of descriptionImages) {
                    if (_image) {
                        if (_image.includes("/")) {
                            descImages.push(_image)
                        } else {
                            const sourcePath = path.join(imagesDir, _image);
                            const name = `products/${product?.sku}/descriptionImages/${Date.now()}-${_image}.webp`
                            const { key } = await uploadWebp(sourcePath, name)
                            descImages.push(key);
                        }
                    }
                }
                newPayload.descriptionImages = descImages;
            }
        }

        async function uploadThumbnail() {

            if (product?.thumb_image?.trim()) {
                if (product.thumb_image.includes("/")) {
                    newPayload.thumbnail = product.thumb_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.thumb_image);
                    const name = `products/${product?.sku}/thumbnails/${Date.now()}-${product.thumb_image}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload.thumbnail = key
                }
            }
        }

        async function uploadOgImage() {

            if (product?.meta_og_image?.trim()) {
                if (product.meta_og_image.includes("/")) {
                    newPayload["seoDetails.ogImage"] = product.meta_og_image
                } else {
                    const thumbImageSource = path.join(imagesDir, product.meta_og_image?.trim());
                    const name = `products/${product?.sku}/ogImages/${Date.now()}-${product?.meta_og_image?.trim()}.webp`
                    const { key } = await uploadWebp(thumbImageSource, name)
                    newPayload["seoDetails.ogImage"] = key
                }
            }
        }

        async function getProductDetails() {

            let productDetail = await service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid })
            if (productDetail && productDetail.productType != "contactLens") throw new Error(`SKU Exist in product type ${productDetail?.productType == "frame" ? "sunglass" : productDetail.productType}`);
            return productDetail
        }
        const [
            productDetail,
            productHead
        ] = await Promise.all([
            getProductDetails(),
            addProductHead(),
            addBrand(),
            addSph(),
            addCyl(),
            addAxis(),
            addCategories(),
            addColor(),
            addLabel(),
            addSize(),
            uploadImages(),
            uploadDescriptionImages(),
            uploadThumbnail(),
            uploadOgImage()
        ])

        if (product?.gender) {
            let gender = product?.gender?.split(",").map((gender) => gender.trim()) || [];
            newPayload.gender = gender
        }

        if (product?.sku?.trim()) newPayload.sku = product?.sku?.trim();
        if (product?.color_code?.trim()) newPayload.colorCode = product?.color_code?.trim();
        if (product?.name_en?.trim()) newPayload.name = { en: product?.name_en?.trim() };
        if (product?.name_ar?.trim()) newPayload.name = { en: newPayload?.name?.en ?? productDetail?.name.en, ar: product?.name_ar?.trim() };
        if (product?.description_en?.trim()) newPayload.description = { en: product?.description_en?.trim() };
        if (product?.description_ar?.trim()) newPayload.description = { en: newPayload.description?.en, ar: product?.description_ar?.trim() };
        if (product?.description_two_en?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim() };
        if (product?.description_two_ar?.trim()) newPayload.descriptionTwo = { en: product?.description_two_en?.trim(), ar: product?.description_two_ar?.trim() };
        // if (product?.sub_brand?.trim()) newPayload.subBrand = product?.sub_brand?.trim();
        if (product?.stock?.trim()) {
            if (isNaN(product?.stock)) throw new Error(`Stock must be numeric for ${product?.sku}`);
            newPayload.stock = Number(product?.stock);
            newPayload.customizable = false
        };
        if (product?.position?.trim()) {
            if (isNaN(product?.position)) throw new Error(`Position must be numeric for ${product?.sku}`);
            newPayload.position = Number(product?.position);
        }
        if (product?.price?.trim()) {
            if (isNaN(product?.price)) throw new Error(`Price must be numeric for ${product?.sku}`);
            newPayload.price = { aed: Number(product?.price) };
            newPayload.customizable = false
        };
        if (product?.offer_price?.trim()) {
            if (isNaN(product?.offer_price)) throw new Error(`Offer_price must be numeric for ${product?.sku}`);
            newPayload.offerPrice = { aed: Number(product?.offer_price) };
            newPayload.customizable = false
        }
        if (product?.supplier_sku?.trim()) newPayload.supplierSku = product?.supplier_sku?.trim();
        if (product?.upc?.trim()) newPayload.upc = product?.upc?.trim();
        if (product?.weight?.trim()) {
            if (isNaN(product?.weight)) throw new Error(`Weight must be numeric for ${product?.sku}`);
            newPayload.weight = Number(product?.weight);
        }
        if (product?.cashbackPercentage?.trim()) {
            if (isNaN(product?.cashbackPercentage)) throw new Error(`CashbackPercentage must be numeric for ${product?.sku}`);
            newPayload.cashbackPercentage = Number(product?.cashbackPercentage);
        }
        if (product?.meta_title_en?.trim()) newPayload["seoDetails.title.en"] = product?.meta_title_en?.trim()
        if (product?.meta_title_ar?.trim()) newPayload["seoDetails.title.ar"] = product?.meta_title_ar?.trim();
        if (product?.meta_description_en?.trim()) newPayload["seoDetails.description.en"] = product?.meta_description_en?.trim();
        if (product?.meta_description_ar?.trim()) newPayload["seoDetails.description.ar"] = product?.meta_description_ar?.trim();
        if (product?.meta_keywords_en?.trim()) newPayload["seoDetails.keywords.en"] = product?.meta_keywords_en?.trim();
        if (product?.meta_keywords_ar?.trim()) newPayload["seoDetails.keywords.ar"] = product?.meta_keywords_ar?.trim();
        if (product?.meta_canonical_en?.trim()) newPayload["seoDetails.canonical.en"] = product?.meta_canonical_en?.trim();
        if (product?.meta_canonical_ar?.trim()) newPayload["seoDetails.canonical.ar"] = product?.meta_canonical_ar?.trim();


        newPayload.productType = "contactLens";
        if (product?.offer_price?.trim() || product?.price?.trim()) {
            newPayload.sellingPrice = Number(product?.offer_price) || Number(product?.price);
        }

        if (newPayload.name?.en) {
            newPayload.slug = productDetail?.name?.en != newPayload.name?.en
                ? await slug.createSlug(db.Products, newPayload.name?.en, {
                    slug: await slug.generateSlug(newPayload.name?.en),
                    storeId: storeid,
                    isDelete: false
                })
                : productDetail?.slug;
        }


        newPayload.refid = productDetail ? productDetail?.refid : generateUniqueNumber();

        if (product?.is_customizable?.trim()) {
            if (product?.is_customizable?.trim() != "1" && product?.is_customizable?.trim() != "0") throw new Error(`is_customizable must be either 1 or 0 for ${product?.sku}`);
            newPayload.customizable = product?.is_customizable?.trim() == "1" ? true : false;
        };
        if (product?.add_to_cart?.trim()) {
            if (product?.add_to_cart?.trim() != "1" && product?.add_to_cart?.trim() != "0") throw new Error(`add_to_cart must be either 1 or 0 for ${product?.sku}`);
            newPayload.isAddToCart = product?.add_to_cart?.trim() == "1" ? true : false
        };
        if (product?.is_active?.trim()) {
            if (product?.is_active?.trim() != "1" && product?.is_active?.trim() != "0") throw new Error(`is_active must be either 1 or 0 for ${product?.sku}`);
            newPayload.isActive = product?.is_active?.trim() == "1" ? true : false
        };
        // if (product?.is_default_variant?.trim()) {
        //     if (product?.is_default_variant?.trim() != "1" && product?.is_default_variant?.trim() != "0") throw new Error(`is_default_variant must be either 1 or 0 for ${product?.sku}`);
        //     newPayload.isDefaultVariant = product?.is_default_variant?.trim() == "1" ? true : false
        // };
        if (product?.show_discount_percentage?.trim()) {
            if (product?.show_discount_percentage?.trim() != "1" && product?.show_discount_percentage?.trim() != "0") throw new Error(`show_discount_percentage must be either 1 or 0 for ${product?.sku}`);
            newPayload.showDiscountPercentage = product?.show_discount_percentage?.trim() == "1" ? true : false
        };
        if (product?.is_new_arrival?.trim()) {
            if (product?.is_new_arrival?.trim() != "1" && product?.is_new_arrival?.trim() != "0") throw new Error(`is_new_arrival must be either 1 or 0 for ${product?.sku}`);
            newPayload.isNewArrival = product?.is_new_arrival?.trim() == "1" ? true : false
        };
        if (product?.is_returnable?.trim()) {
            if (product?.is_returnable?.trim() != "1" && product?.is_returnable?.trim() != "0") throw new Error(`is_returnable must be either 1 or 0 for ${product?.sku}`);
            newPayload.isReturnable = product?.is_returnable?.trim() == "1" ? true : false
        };
        if (product?.isTaxIncluded?.trim()) {
            if (product?.isTaxIncluded?.trim() != "1" && product?.isTaxIncluded?.trim() != "0") throw new Error(`isTaxIncluded must be either 1 or 0 for ${product?.sku}`);
            newPayload.isTaxIncluded = product?.isTaxIncluded?.trim() == "1" ? true : false
        };
        if (product?.isCashbackEnabled?.trim()) {
            if (product?.isCashbackEnabled?.trim() != "1" && product?.isCashbackEnabled?.trim() != "0") throw new Error(`isCashbackEnabled must be either 1 or 0 for ${product?.sku}`);
            newPayload.isCashbackEnabled = product?.isCashbackEnabled?.trim() == "1" ? true : false;
        };

        if (product?.sph?.trim()) {
            if (product?.sph?.trim() != "1" && product?.sph?.trim() != "0") throw new Error(`sph must be either 1 or 0 for ${product?.sku}`);
            newPayload.sph = product?.sph?.trim() == "1" ? true : false;
        }
        if (product?.cyl?.trim()) {
            if (product?.cyl?.trim() != "1" && product?.cyl?.trim() != "0") throw new Error(`cyl must be either 1 or 0 for ${product?.sku}`);
            newPayload.cyl = product?.cyl?.trim() == "1" ? true : false;
        }
        if (product?.axis?.trim()) {
            if (product?.axis?.trim() != "1" && product?.axis?.trim() != "0") throw new Error(`axis must be either 1 or 0 for ${product?.sku}`);
            newPayload.axis = product?.axis?.trim() == "1" ? true : false;
        }
        if (product?.multi_focal?.trim()) {
            if (product?.multi_focal?.trim() != "1" && product?.multi_focal?.trim() != "0") throw new Error(`multi_focal must be either 1 or 0 for ${product?.sku}`);
            newPayload.multiFocal = product?.multi_focal?.trim() == "1" ? true : false;
        }

        let i = 1
        const techDetails = [];
        while (true) {
            if (product?.[`t${i}_en`]) {
                techDetails.push({
                    title: {
                        en: product[`t${i}_en`],
                        ar: product[`t${i}_ar`],
                    },
                    description: {
                        en: product[`d${i}_en`],
                        ar: product[`d${i}_ar`],
                    }
                })
                i++
            } else {
                break;
            }
        }

        if (i !== 1) {
            newPayload.technicalInfo = techDetails
        }

        async function addProducts(newProduct) {
            if (productHead) {
                let products = productHead?.products || [];
                if (!products.map(item => item?.toString()).includes(newProduct?._id.toString())) {
                    products?.push(newProduct?._id)
                    await db.ProductHead.updateOne({ _id: newProduct?.parent }, { products });
                }
            }
        }
        newPayload.storeId = storeid
        let newProduct;

        if (productDetail) {
            const [newProd] = await Promise.all([
                service.update({ _id: productDetail?._id }, newPayload),
                addProducts(productDetail)
            ])
            newProduct = newProd
        } else {
            if (!brandDetails) {
                throw new Error(`Brand is required`)
            } else if (!product?.categories?.trim() || !headCategory) {
                throw new Error(`Category is required "Contact Lens>color" or "Contact Lens>clear"`)
            }
            if (!product?.supplier_sku?.trim()) {
                throw new Error(`Supplier SKU is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.upc?.trim()) {
                throw new Error(`UPC is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.thumb_image?.trim()) {
                throw new Error(`Thumbnail is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.stock?.trim()) {
                throw new Error(`Stock is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.price?.trim()) {
                throw new Error(`Price is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.weight?.trim()) {
                throw new Error(`Weight is required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.is_new_arrival?.trim()) {
                throw new Error(`is_new_arrival required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            if (!product?.description_en?.trim()) {
                throw new Error(`description_en required for ${key + 1}${key + 1 == 1 ? 'st' : key + 1 == 2 ? 'nd' : key + 1 == 3 ? 'rd' : 'th'} product`)
            }
            newProduct = await service.create(newPayload);
            await addProducts(newProduct)
        }

        newProduct.key = key


        createdProducts.push(newProduct);
        writer.count += 1
        res.write(`${parseInt(writer.count)}/${totalProducts} products processed\n\n`);
        release()
    } catch (error) {
        release()
        console.log(error);
        // console.log(error, " :: Error caught in product " + product?.sku);
        // res.write(`event: error\nError caught in product ${product?.sku} ${error.message}\n\n`);
        throw new Error(`Error caught in product ${product?.sku}: ${error.message}`);
        // throw error;
    }
}

const createdContactLensProductsUpdate = async (createdProducts, products, csvDir, req) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        let promises = [];
        async function updateProduct(product) {
            const csvRow = products.find((row) => row.sku === product.sku);
            // if (product?.storeId) storeid = product?.storeId
            if (csvRow) {

                let recommended;
                let boughtTogether;

                async function getRecommended() {
                    if (csvRow?.recommended_products?.trim()) {
                        const recommendedSkus = csvRow?.recommended_products?.trim()?.split(",");
                        const recommendedProducts = await service.find({
                            sku: { $in: recommendedSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        recommended = recommendedProducts.map((product) => product?._id);
                    }
                }
                async function getBoughtTogether() {
                    if (csvRow?.bought_together?.trim()) {
                        const boughtTogetherSkus = csvRow?.bought_together?.trim()?.split(",");
                        const boughtTogetherProducts = await service.find({
                            sku: { $in: boughtTogetherSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        boughtTogether = boughtTogetherProducts.map((product) => product?._id);
                    }
                }

                await Promise.all([getRecommended(), getBoughtTogether()])

                await db.Products.findByIdAndUpdate(product?._id, {
                    recommendedProducts: recommended,
                    boughtTogether
                })
                // await product.save();
            }
        }
        for (const product of createdProducts) {
            promises.push(updateProduct(product))
        }
        await Promise.all(promises)
    } catch (error) {
        console.error("Error in cretedProductsUpdate:", error);
    }
};

exports.bulkImports = async (req, res) => {
    let csvDir, imagesDir;
    try {
        // const zip = new AdmZip(req.file.path);
        csvDir = "tmp/csv";
        imagesDir = "tmp/images";
        fs.mkdirSync(csvDir, { recursive: true });
        fs.mkdirSync(imagesDir, { recursive: true });
        // const zip = new StreamZip({ file: req.file.path });
        const zip = new StreamZip.async({ file: req.file.path });
        const entries = await zip.entries();

        for (let entry of Object.values(entries)) {

            const fileName = entry.name;
            if (fileName.endsWith(".csv")) {
                await zip.extract(fileName, csvDir, err => console.log(err));
            } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
                await zip.extract(fileName, imagesDir, err => console.log(err));
            }
        }

        await zip.close();


        // zipEntries.forEach((entry) => {
        //     const fileName = entry.entryName;
        //     if (fileName.endsWith(".csv")) {
        //         zip.extractEntryTo(fileName, csvDir, false, true);
        //     } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
        //         zip.extractEntryTo(fileName, imagesDir, false, true);
        //     }
        // });

        await processCSVs(req, csvDir, imagesDir, res);

        // res.status(200).json({ message: "Products imported successfully" });
    } catch (error) {
        console.log(error, " :: Error caught in product import");
        res.status(422).json({
            error_code: "90",
            error_message: "An error occurred while processing the import.",
        });
    }
};

const
    processCSVs = async (req, csvDir, imagesDir, res) => {
        res.setHeader("Content-Type", "text/event-stream");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        res.flushHeaders();
        try {
            const productsFilePath = `${csvDir}/products.csv`;
            const products = [];
            fs.createReadStream(productsFilePath)
                .pipe(csv({
                    mapHeaders: (({ header }) => {
                        if (header.charCodeAt(0) === 0xFEFF) {
                            header = header.substr(1);
                        }
                        return header;
                    })
                }))
                .on("data", (data) => products.push(data))
                .on("end", async () => {
                    const totalProducts = products.length;
                    let processedProducts = 0;
                    let writer = {
                        count: 0
                    }

                    const productImagesDir = "uploads/products/";
                    fs.mkdirSync(productImagesDir, { recursive: true });

                    const createdProducts = [];
                    const createdContactLens = [];
                    try {

                        for (const product of products) {
                            if (product?.product_type?.trim() === "contactLens") {
                                await processContactLens(req, res, product, createdContactLens, processedProducts, totalProducts, imagesDir, productImagesDir, writer);
                            } else {
                                await processProducts(req, res, product, createdProducts, processedProducts, totalProducts, imagesDir, productImagesDir, writer);
                            }
                        }

                        const resp = await Promise.all([
                            createdProductsUpdate(createdProducts, products, csvDir, req),
                            createdLensProductsUpdate(createdContactLens, products, csvDir, req),
                        ])

                    } catch (error) {
                        console.log(error)
                        helper.deliverResponse(res, 422, error, {
                            error_code: messages.SERVER_ERROR.error_code,
                            error_message: messages.SERVER_ERROR.error_message,
                        });
                    }

                    res.write(`event: message\ndata: Products imported successfully\n\n`);
                    setTimeout(() => {
                        cleanup(req.file.path, csvDir, imagesDir);
                    }, 5000);
                    // Close the connection
                    res.end();
                });
        } catch (error) {
            console.log(error)
            helper.deliverResponse(res, 422, error, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            });
        }
    };

const processProducts = async (req, res, product, createdProducts, processedProducts, totalProducts, imagesDir, productImagesDir, writer) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"

    if (product?.storeId) storeid = product?.storeId;
    if (!product?.sku?.trim()) return;
    console.log(product?.sku)
    let newPayload = {};
    let lensBrand = null

    async function addProductHead() {
        if (product?.parent_sku?.trim()) {
            let productHead = await db.ProductHead.findOne({
                sku: product?.parent_sku?.trim(),
                isDelete: false,
                storeId: storeid
            })

            if (!productHead) {
                const [refid, productData] = await Promise.all([
                    productHeadService.count({}),
                    service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid })
                ])
                productHead = await db.ProductHead.create({
                    sku: product?.parent_sku?.trim(),
                    isDelete: false,
                    name: product?.parent_sku?.trim(),
                    refid: refid + 1,
                    products: [],
                    type: productData?.productType,
                    storeId: storeid
                })
            }
            newPayload.parent = productHead._id
            return productHead
        }
    }

    async function addBrand() {
        if (product?.brand?.trim()) {
            let brandDetails = await db.Brands.findOne({
                "name.en": product.brand?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!brandDetails) brandDetails = await createBrand({ name: product.brand?.trim(), storeId: storeid });
            newPayload.brand = brandDetails._id
        }
    }

    async function addCategories() {
        if (product?.categories?.trim()) {
            let categories = [];
            let subCategories = [];
            const catStrings = product.categories?.split(",");
            for (let cat of catStrings) {
                let cats = cat.split(">");
                let prev = null;
                let root = null
                for (let cat of cats) {
                    if (prev) {
                        let category = await db.Category.findOne({
                            "name.en": cat,
                            isDelete: false,
                            isRoot: false,
                            parent: prev?._id,
                            root: root?._id,
                            storeId: storeid
                        })
                        if (category) subCategories.push(category?._id);
                        else {
                            category = await createCategory({ name: cat, parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                            subCategories.push(category?._id);
                        }
                        prev = category
                    } else {
                        let mainCatDetails = await db.Category.findOne({
                            "name.en": cat,
                            isDelete: false,
                            storeId: storeid
                        });
                        if (mainCatDetails) categories.push(mainCatDetails?._id);
                        else {
                            mainCatDetails = await createCategory({ name: cat, storeId: storeid });
                            categories.push(mainCatDetails?._id);
                        }
                        prev = mainCatDetails
                        root = mainCatDetails
                    }
                }
            }
            console.log("categories", categories)
            newPayload.category = Array.from(new Set(categories.map(i => i?.toString())));
            newPayload.subCategory = Array.from(new Set(subCategories.map(i => i?.toString())));
        }
    }

    async function addSph() {
        if (product?.sphValues?.trim()) {
            if (!lensBrand) {
                lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
            }
            let sphValues = [];
            const values = product?.sphValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.LensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key];
                if (!powerDetails) {
                    let refid = (await db.LensPower.count({})) + 1;
                    powerDetails = await db.LensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid })
                }
                sphValues.push(powerDetails._id)
            }
            newPayload.sphValues = sphValues
        }
    }

    async function addCyl() {
        if (!lensBrand) {
            lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
        }
        if (product?.cylValues?.trim()) {
            let cylValues = [];
            const values = product?.cylValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.LensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key];
                if (!powerDetails) {
                    let refid = (await db.LensPower.count({})) + 1;
                    powerDetails = await db.LensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                }
                cylValues.push(powerDetails._id)
            }
            newPayload.cylValues = cylValues
        }
    }

    async function addAxis() {
        if (!lensBrand) {
            lensBrand = await db.LensBrand.findOne({ isDelete: false, isActive: true, storeId: storeid })
        }
        if (product?.axisValues?.trim()) {
            let axisValues = [];
            const values = product?.axisValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.LensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key];
                if (!powerDetails) {
                    let refid = (await db.LensPower.count({})) + 1;
                    powerDetails = await db.LensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                }
                axisValues.push(powerDetails._id)
            }
            newPayload.axisValues = axisValues
        }
    }

    async function addAdd() {
        if (product?.addValues?.trim()) {
            let addValues = [];
            const values = product?.addValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.LensPower.findOne({ type: 'Add', name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key];
                if (!powerDetails) {
                    let refid = (await db.LensPower.count({})) + 1;
                    powerDetails = await db.LensPower.create({ type: 'Add', name: power?.trim(), isDelete: false, brand: lensBrand._id, refid, storeId: storeid });
                }
                addValues.push(powerDetails._id)
            }
            newPayload.addValues = addValues
        }
    }


    async function addColor() {
        if (product?.colour?.trim()) {
            let colorDetail = await db.Colors.findOne({
                "name.en": product.colour?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!colorDetail) colorDetail = await createColor(product.colour?.trim(), product.colour_value, storeid);
            newPayload.color = colorDetail._id
        }
    }

    async function addFrameType() {
        if (product?.frame_type?.trim()) {
            let frameTypeDetail = await db.FrameTypes.findOne({
                "name.en": product.frame_type?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!frameTypeDetail)
                frameTypeDetail = await createFrameType({
                    name: product.frame_type?.trim(),
                    storeId: storeid
                });
            newPayload.frameType = frameTypeDetail._id
        }
    }

    async function addFrameShape() {
        if (product?.frame_shape?.trim()) {
            let frameShapeDetail = await db.FrameShapes.findOne({
                "name.en": product.frame_shape?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!frameShapeDetail)
                frameShapeDetail = await createFrameShape({
                    name: product.frame_shape?.trim(),
                    storeId: storeid
                });
            newPayload.frameShape = frameShapeDetail._id
        }
    }

    async function addLabel() {
        if (product?.label?.trim()) {
            let labelDetail = await db.Labels.findOne({
                "name.en": product.label?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
            newPayload.label = labelDetail._id
        }
    }

    async function addFrontMaterial() {
        if (product?.front_material?.length > 0) {
            let frontMaterials = [];
            const productFrontMaterials = product?.front_material?.split(",");
            for (let material of productFrontMaterials) {
                let frontMaterialDetail = await db.FrontMaterial.findOne({
                    "name.en": material?.trim(),
                    isDelete: false,
                    storeId: storeid
                });
                if (frontMaterialDetail) frontMaterials.push(frontMaterialDetail?._id);
                else {
                    frontMaterialDetail = await createFrontMaterial({
                        "name.en": material?.trim(),
                        storeId: storeid
                    });
                    frontMaterials.push(frontMaterialDetail?._id);
                }
            }
            newPayload.frontMaterial = frontMaterials
        }
    }

    async function addLensMaterial() {
        if (product?.lens_material?.length > 0) {
            let lensMaterials = [];
            const productLensMaterials = product?.lens_material?.split(",");
            for (let material of productLensMaterials) {
                let lensMaterialDetail = await db.LensMaterial.findOne({
                    "name.en": material?.trim(),
                    isDelete: false,
                    storeId: storeid
                });
                if (lensMaterialDetail) lensMaterials.push(lensMaterialDetail?._id)
                else {
                    lensMaterialDetail = await createLensMaterial({
                        name: { en: material?.trim() },
                        storeId: storeid
                    });
                    lensMaterials.push(lensMaterialDetail?._id);
                }
            }
            newPayload.lensMaterial = lensMaterials
        }
    }

    async function addType() {
        if (product?.type?.length > 0) {
            let types = [];
            const productTypes = product?.type?.split(",");
            for (let type of productTypes) {
                let typeDetail = await db.Type.findOne({
                    "name.en": type?.trim(),
                    isDelete: false,
                    storeId: storeid
                });
                if (typeDetail) types.push(typeDetail?._id);
                else {
                    typeDetail = await createType({ name: type?.trim(), storeId: storeid });
                    types.push(typeDetail?._id);
                }
            }
            newPayload.type = types
        }
    }

    async function addSize() {
        if (product?.size?.trim()) {
            let sizeDetails = await db.Sizes.findOne({
                "name": product.size?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!sizeDetails) sizeDetails = await createSize({ name: product.size?.trim(), storeId: storeid });
            newPayload.size = sizeDetails._id
        }
    }

    async function uploadImages() {
        if (product?.product_images?.trim()) {
            let files = [];
            let images = product?.product_images?.split(",");
            for (let _image of images) {
                if (_image) {
                    if (_image.includes("/")) {
                        files.push(_image)
                    } else {
                        const sourcePath = path.join(imagesDir, _image);
                        const name = `products/${body?.sku}/images/${Date.now()}-${_image}.webp`
                        const { key } = await uploadWebp(sourcePath, name)
                        files.push(key)
                    }
                }
            }
            newPayload.images = files
        }
    }

    async function uploadDescriptionImages() {
        if (product?.description_images?.trim()) {
            let descImages = [];
            let descriptionImages = product?.description_images?.split(",");
            for (let _image of descriptionImages) {
                if (_image) {
                    if (_image.includes("/")) {
                        descImages.push(_image)
                    } else {
                        const sourcePath = path.join(imagesDir, _image);
                        const name = `products/${body?.sku}/descriptionImages/${Date.now()}-${_image}.webp`
                        const { key } = await uploadWebp(sourcePath, name)
                        descImages.push(key);
                    }
                }
            }
            newPayload.descriptionImages = descImages;
        }
    }

    async function uploadThumbnail() {
        if (product?.thumb_image) {
            if (product.thumb_image.includes("/")) {
                newPayload.thumbnail = product.thumb_image
            } else {
                const thumbImageSource = path.join(imagesDir, product.thumb_image);
                const name = `products/${body?.sku}/thumbnails/${Date.now()}-${product.thumb_image}.webp`
                const { key } = await uploadWebp(thumbImageSource, name)
                newPayload.thumbnail = key
            }
        }
    }

    const [
        productCounter,
        productDetail,
        productHead
    ] = await Promise.all([
        service.count({}),
        service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid }),
        addProductHead(),
        addBrand(),
        addSph(),
        addCyl(),
        addAxis(),
        addAdd(),
        addCategories(),
        addColor(),
        addFrameType(),
        addFrameShape(),
        addLabel(),
        addFrontMaterial(),
        addLensMaterial(),
        addType(),
        addSize(),
        uploadImages(),
        uploadDescriptionImages(),
        uploadThumbnail()
    ])

    if (product?.gender) {
        let gender = product?.gender?.split(",").map((gender) => gender.trim()) || [];
        newPayload.gender = gender
    }

    if (product?.sku?.trim()) newPayload.sku = product?.sku?.trim()
    if (product?.name_en?.trim()) newPayload.name = { en: product?.name_en?.trim() };
    if (product?.name_ar?.trim()) newPayload.name = { en: newPayload?.name?.en ?? productDetail?.name.en, ar: product?.name_ar?.trim() };
    if (product?.description_en?.trim()) newPayload.description = { en: product?.description_en?.trim() };
    if (product?.description_ar?.trim()) newPayload.description = { en: newPayload.description?.en, ar: product?.description_ar?.trim() };
    if (product?.stock?.trim() && !isNaN(product?.stock)) {
        newPayload.stock = Number(product?.stock);
        newPayload.customizable = false
    };
    if (product?.position?.trim() && !isNaN(product?.stock)) newPayload.position = Number(product?.position);
    if (product?.price?.trim() && !isNaN(product?.price)) {
        newPayload.price = { aed: Number(product?.price) };
        newPayload.customizable = false
    };
    if (product?.offer_price?.trim() && !isNaN(product?.offer_price)) {
        newPayload.offerPrice = { aed: Number(product?.offer_price) };
        newPayload.customizable = false
    }
    if (product?.supplier_sku?.trim()) newPayload.supplierSku = product?.supplier_sku?.trim();
    if (product?.upc?.trim()) newPayload.upc = product?.upc?.trim();
    if (product?.weight?.trim() && !isNaN(product?.weight)) newPayload.weight = Number(product?.weight);
    if (product?.cashbackPercentage?.trim() && !isNaN(product?.weight)) newPayload.cashbackPercentage = Number(product?.cashbackPercentage);

    newPayload.productType = "frame";
    newPayload.sellingPrice = Number(product?.offer_price) || Number(product?.price);

    if (product?.name_en) {
        newPayload.slug = productDetail?.name?.en != product?.name_en
            ? await slug.createSlug(db.Products, product?.name_en, {
                slug: await slug.generateSlug(product?.name_en),
                storeId: storeid,
                isDelete: false
            })
            : productDetail?.slug;
    }

    newPayload.refid = productDetail ? productDetail?.refid : productCounter + 1;

    if (product?.is_customizable?.trim()) {
        newPayload.customizable = product?.is_customizable?.trim() == "1" ? true : false;
    };
    if (product?.add_to_cart?.trim()) {
        newPayload.isAddToCart = product?.add_to_cart?.trim() == "1" ? true : false
    };
    if (product?.choose_lens?.trim()) {
        newPayload.isChooseLens = product?.choose_lens?.trim() == "1" ? true : false
    };
    if (product?.is_active?.trim()) {
        newPayload.isActive = product?.is_active?.trim() == "1" ? true : false
    };
    if (product?.is_default_variant?.trim()) {
        newPayload.isDefaultVariant = product?.is_default_variant?.trim() == "1" ? true : false
    };
    if (product?.show_discount_percentage?.trim()) {
        newPayload.showDiscountPercentage = product?.show_discount_percentage?.trim() == "1" ? true : false
    };
    if (product?.is_new_arrival?.trim()) {
        newPayload.isNewArrival = product?.is_new_arrival?.trim() == "1" ? true : false
    };
    if (product?.is_returnable?.trim()) {
        newPayload.isReturnable = product?.is_returnable?.trim() == "1" ? true : false
    };
    if (product?.virtual_try_available?.trim()) {
        newPayload.isVirtualTry = product?.virtual_try_available?.trim() == "1" ? true : false
    };
    if (product?.isTaxIncluded?.trim()) {
        newPayload.isTaxIncluded = product?.isTaxIncluded?.trim() == "1" ? true : false
    };
    if (product?.isCashbackEnabled?.trim()) {
        newPayload.isCashbackEnabled = product?.isCashbackEnabled?.trim() == "1" ? true : false;
    };

    let i = 1
    const techDetails = [];
    while (true) {
        if (product?.[`t${i}_en`]) {
            techDetails.push({
                title: {
                    en: product[`t${i}_en`],
                    ar: product[`t${i}_ar`],
                },
                description: {
                    en: product[`d${i}_en`],
                    ar: product[`d${i}_ar`],
                }
            })
            i++
        } else {
            break;
        }
    }

    if (i !== 1) {
        newPayload.technicalInfo = techDetails
    }

    async function addProducts(newProduct) {
        if (productHead) {
            let products = productHead?.products || [];
            if (!products.map(item => item?.toString()).includes(newProduct?._id.toString())) {
                products?.push(newProduct?._id)
                await db.ProductHead.updateOne({ _id: newProduct?.parent }, { products });
            }
        }
    }
    newPayload.storeId = storeid
    let newProduct;
    if (productDetail) {
        const [newProd] = await Promise.all([
            service.update({ _id: productDetail?._id }, newPayload),
            addProducts(productDetail)
        ])
        newProduct = newProd
    } else {
        newProduct = await service.create(newPayload);
        await addProducts(newProduct)
    }

    createdProducts.push(newProduct);
    writer.count += 1
    res.write(`${writer.count}/${totalProducts}\n\n`);
}

const processContactLens = async (req, res, product, createdProducts, processedProducts, totalProducts, imagesDir, productImagesDir, writer) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"

    if (product?.storeId) storeid = product?.storeId
    if (!product?.sku?.trim()) return;

    console.log(product?.sku)

    let newPayload = {};
    newPayload.storeId = storeid

    async function addProductHead() {
        if (product?.parent_sku?.trim()) {
            let productHead = await db.ProductHead.findOne({
                sku: product?.parent_sku?.trim(),
                isDelete: false,
                storeId: storeid
            })

            if (!productHead) {
                const [refid, productData] = await Promise.all([
                    productHeadService.count({}),
                    service.findOne({ sku: product.sku?.trim(), isDelete: false, storeId: storeid })
                ])
                productHead = await db.ProductHead.create({
                    sku: product?.parent_sku?.trim(),
                    isDelete: false,
                    name: product?.parent_sku?.trim(),
                    refid: refid + 1,
                    products: [],
                    type: productData?.productType,
                    storeId: storeid
                })
            }
            newPayload.parent = productHead._id
            return productHead
        }
    }

    async function addBrand() {
        if (product?.brand?.trim()) {
            let brandDetails = await db.Brands.findOne({
                "name.en": product.brand?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!brandDetails) brandDetails = await createBrand({ name: product.brand?.trim(), storeId: storeid });
            newPayload.brand = brandDetails._id
        }
    }

    async function addSph() {
        if (product?.sphValues?.trim()) {
            let contactSph = [];
            const values = product?.sphValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.ContactLensPower.findOne({ type: "Sph", name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key]
                if (!powerDetails) {
                    let refid = (await db.ContactLensPower.count({})) + 1;
                    powerDetails = await db.ContactLensPower.create({ type: "Sph", name: power?.trim(), isDelete: false, refid, storeId: storeid })
                }
                contactSph.push(powerDetails._id)
            }
            newPayload.contactSph = contactSph
        }
    }

    async function addCyl() {
        if (product?.cylValues?.trim()) {
            let contactCyl = [];
            const values = product?.cylValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.ContactLensPower.findOne({ type: 'Cyl', name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key]
                if (!powerDetails) {
                    let refid = (await db.ContactLensPower.count({})) + 1;
                    powerDetails = await db.ContactLensPower.create({ type: 'Cyl', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                }
                contactCyl.push(powerDetails._id)
            }
            newPayload.contactCyl = contactCyl
        }
    }

    async function addAxis() {
        if (product?.axisValues?.trim()) {
            let contactAxis = [];
            const values = product?.axisValues?.split(",")
            let promise = [];
            for (let power of values) promise.push(db.ContactLensPower.findOne({ type: 'Axis', name: power?.trim(), isDelete: false, storeId: storeid }));
            const details = await Promise.all(promise);
            for (let [key, power] of values.entries()) {
                let powerDetails = details[key]
                if (!powerDetails) {
                    let refid = (await db.ContactLensPower.count({})) + 1;
                    powerDetails = await db.ContactLensPower.create({ type: 'Axis', name: power?.trim(), isDelete: false, refid, storeId: storeid });
                }
                contactAxis.push(powerDetails._id)
            }
            newPayload.contactAxis = contactAxis
        }
    }

    async function addCategories() {
        if (product?.categories?.trim()) {
            let categories = [];
            let subCategories = [];
            const catStrings = product.categories?.split(",");
            for (let cat of catStrings) {
                let cats = cat.split(">");
                let prev = null;
                let root = null
                for (let cat of cats) {
                    if (prev) {
                        let category = await db.Category.findOne({
                            "name.en": cat,
                            isDelete: false,
                            isRoot: false,
                            parent: prev?._id,
                            root: root?._id,
                            storeId: storeid
                        })
                        if (category) subCategories.push(category?._id);
                        else {
                            category = await createCategory({ name: cat, parent: prev?._id, root: root?._id, isRoot: false, storeId: storeid });
                            subCategories.push(category?._id);
                        }
                        prev = category
                    } else {
                        let mainCatDetails = await db.Category.findOne({
                            "name.en": cat,
                            isDelete: false,
                            storeId: storeid
                        });
                        if (mainCatDetails) categories.push(mainCatDetails?._id);
                        else {
                            mainCatDetails = await createCategory({ name: cat, storeId: storeid });
                            categories.push(mainCatDetails?._id);
                        }
                        prev = mainCatDetails
                        root = mainCatDetails
                    }
                }
            }
            newPayload.category = Array.from(new Set(categories.map(i => i?.toString())));
            newPayload.subCategory = Array.from(new Set(subCategories.map(i => i?.toString())));
        }
    }

    async function addColors() {
        if (product?.colour?.trim()) {
            let colorDetail = await db.Colors.findOne({
                name: product.colour?.trim(),
                isDelete: false,
                storeId: storeid
            });

            if (!colorDetail) colorDetail = await createColor(product.colour?.trim(), product.colour_value, storeid);
            newPayload.color = colorDetail._id
        }
    }

    async function addFrameType() {
        if (product?.frame_type?.trim()) {
            let labelDetail = await db.Labels.findOne({
                "name.en": product.label?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
            newPayload.frameType = frameTypeDetail._id
        }
    }

    async function uploadImages() {
        if (product?.product_images?.trim()) {
            let files = [];
            let images = product?.product_images?.split(",");
            for (let _image of images) {
                if (_image) {
                    if (_image.includes("/")) {
                        files.push(_image)
                    } else {
                        const sourcePath = path.join(imagesDir, _image);
                        const name = `products/${body?.sku}/images/${Date.now()}-${_image}.webp`
                        const { key } = await uploadWebp(sourcePath, name)
                        files.push(key)
                    }
                }
            }
            newPayload.images = files
        }
    }

    async function uploadDescriptionImages() {
        if (product?.description_images?.trim()) {
            let descImages = [];
            let descriptionImages = product?.description_images?.split(",");
            for (let _image of descriptionImages) {
                if (_image) {
                    if (_image.includes("/")) {
                        descImages.push(_image)
                    } else {
                        const sourcePath = path.join(imagesDir, _image);
                        const name = `products/${body?.sku}/descriptionImages/${Date.now()}-${_image}.webp`
                        const { key } = await uploadWebp(sourcePath, name)
                        descImages.push(key);
                    }
                }
            }
            newPayload.descriptionImages = descImages;
        }
    }

    async function uploadThumbnail() {
        if (product?.thumb_image) {
            if (product.thumb_image.includes("/")) {
                newPayload.thumbnail = product.thumb_image
            } else {
                const thumbImageSource = path.join(imagesDir, product.thumb_image);
                const name = `products/${body?.sku}/thumbnails/${Date.now()}-${product.thumb_image}.webp`
                const { key } = await uploadWebp(thumbImageSource, name)
                newPayload.thumbnail = key
            }
        }
    }

    async function addLabel() {
        if (product?.label?.trim()) {
            let labelDetail = await db.Labels.findOne({
                "name.en": product.label?.trim(),
                isDelete: false,
                storeId: storeid
            });
            if (!labelDetail) labelDetail = await createLabel({ name: product.label?.trim(), storeId: storeid });
            newPayload.label = labelDetail._id
        }
    }

    const [
        productCounter,
        productDetail,
        productHead
    ] = await Promise.all([
        service.count({}),
        service.findOne({ sku: product.sku?.trim(), isDelete: false }),
        addProductHead(),
        addBrand(),
        addSph(),
        addCyl(),
        addAxis(),
        addCategories(),
        addColors(),
        addFrameType(),
        uploadImages(),
        uploadDescriptionImages(),
        uploadThumbnail(),
        addLabel()
    ])

    if (product?.gender) {
        let gender = product?.gender?.split(",").map((gender) => gender) || [];
        newPayload.gender = gender
    }

    if (product?.sph == "TRUE" || product?.cyl == "TRUE" || product?.axis == "TRUE") {
        if (product?.power_price) newPayload.powerPrice = product?.power_price
    }

    if (product?.sku?.trim()) newPayload.sku = product?.sku?.trim()
    if (product?.name_en?.trim()) newPayload.name = { en: product?.name_en?.trim() };
    if (product?.name_ar?.trim()) newPayload.name = { en: newPayload?.name.en ?? productDetail?.name?.en, ar: product?.name_ar?.trim() };
    if (product?.description_en?.trim()) newPayload.description = { en: product?.description_en?.trim() };
    if (product?.description_ar?.trim()) newPayload.description = { en: newPayload?.description?.en ?? productDetail?.description?.en, ar: product?.description_ar?.trim() };
    if (product?.stock?.trim() && !isNaN(product?.stock)) newPayload.stock = Number(product?.stock);
    if (product?.position?.trim() && !isNaN(product?.stock)) newPayload.position = Number(product?.position);
    if (product?.price?.trim() && !isNaN(product?.price)) newPayload.price = { aed: Number(product?.price) };
    if (product?.offer_price?.trim() && !isNaN(product?.offer_price)) newPayload.offerPrice = { aed: Number(product?.offer_price) };
    if (product?.supplier_sku?.trim()) newPayload.supplierSku = product?.supplier_sku?.trim();
    if (product?.upc?.trim()) newPayload.upc = product?.upc?.trim();
    if (product?.weight?.trim() && !isNaN(product?.weight)) newPayload.weight = Number(product?.weight);
    if (product?.cashbackPercentage?.trim() && !isNaN(product?.weight)) newPayload.cashbackPercentage = Number(product?.cashbackPercentage);

    newPayload.productType = "contactLens";
    newPayload.sellingPrice = Number(product?.offer_price) || Number(product?.price);
    newPayload.slug = productDetail?.name?.en != product?.name_en
        ? await slug.createSlug(db.Products, product?.name_en, {
            slug: await slug.generateSlug(product?.name_en),
            storeId: storeid,
            isDelete: false
        })
        : productDetail?.slug;

    newPayload.refid = productDetail ? productDetail?.refid : productCounter + 1;

    if (product?.is_customizable) {
        newPayload.customizable = product?.is_customizable == "1" ? true : false;
    };
    if (product?.is_active?.trim()) {
        newPayload.isActive = product?.is_active?.trim() == "1" ? true : false
    };
    if (product?.is_default_variant?.trim()) {
        newPayload.isDefaultVariant = product?.is_default_variant?.trim() == "1" ? true : false
    };
    if (product?.show_discount_percentage?.trim()) {
        newPayload.showDiscountPercentage = product?.show_discount_percentage?.trim() == "1" ? true : false
    };
    if (product?.is_new_arrival?.trim()) {
        newPayload.isNewArrival = product?.is_new_arrival?.trim() == "1" ? true : false
    };
    if (product?.is_returnable?.trim()) {
        newPayload.isReturnable = product?.is_returnable?.trim() == "1" ? true : false
    };
    if (product?.isTaxIncluded?.trim()) {
        newPayload.isTaxIncluded = product?.isTaxIncluded?.trim() == "1" ? true : false
    };
    if (product?.isCashbackEnabled?.trim()) {
        newPayload.isCashbackEnabled = product?.isCashbackEnabled?.trim() == "1" ? true : false;
    }
    if (product?.sph?.trim()) {
        newPayload.sph = product?.sph?.trim() == "TRUE" ? true : false;
    }
    if (product?.cyl?.trim()) {
        newPayload.cyl = product?.cyl?.trim() == "TRUE" ? true : false;
    }
    if (product?.axis?.trim()) {
        newPayload.axis = product?.axis?.trim() == "TRUE" ? true : false;
    }
    if (product?.multi_focal?.trim()) {
        newPayload.multiFocal = product?.multi_focal?.trim() == "TRUE" ? true : false;
    }


    if (!newPayload.name.en) newPayload.name.en = "";
    if (!newPayload.sku) newPayload.sku = ""

    let i = 1;
    const techDetails = [];
    while (true) {
        if (product?.[`t${i}_en`]) {
            techDetails.push({
                title: {
                    en: product[`t${i}_en`],
                    ar: product[`t${i}_ar`],
                },
                description: {
                    en: product[`d${i}_en`],
                    ar: product[`d${i}_ar`],
                }
            })
            i++
        } else {
            break;
        }
    }

    if (i !== 1) {
        newPayload.technicalInfo = techDetails
    }

    async function addProducts(newProduct) {
        if (productHead) {
            let products = productHead?.products || [];
            if (!products.map(item => item?.toString()).includes(newProduct?._id.toString())) {
                products?.push(newProduct?._id)
                await db.ProductHead.updateOne({ _id: newProduct?.parent }, { products });
            }
        }
    }

    let newProduct;
    if (productDetail) {
        const [newProd] = await Promise.all([
            service.update({ _id: productDetail?._id }, newPayload),
            addProducts(productDetail)
        ])
        newProduct = newProd
    } else {
        newProduct = await service.create(newPayload)
        await addProducts(newProduct)
    }


    createdProducts.push(newProduct);
    writer.count += 1
    // processedProducts++;
    res.write(`${writer.count}/${totalProducts}\n\n`);
}

const cleanup = async (uploadedFilePath, csvDir, imagesDir) => {
    try {
        if (fs.existsSync(csvDir)) {
            await fs.promises.rm(csvDir, { recursive: true, force: true });
        }

        if (fs.existsSync(imagesDir)) {
            await fs.promises.rm(imagesDir, { recursive: true, force: true });
        }
    } catch (error) {
        console.error("Error during cleanup:", error);
    }
};

const createdProductsUpdate = async (createdProducts, products, csvDir, req) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        for (const product of createdProducts) {
            const csvRow = products.find((row) => row.sku === product.sku);
            if (product?.storeId) storeid = product?.storeId
            if (csvRow) {

                let recommended;
                let boughtTogether;

                async function getRecommended() {
                    if (csvRow?.recommended_products?.trim()) {
                        const recommendedSkus = csvRow?.recommended_products?.trim()?.split(",");
                        const recommendedProducts = await service.find({
                            sku: { $in: recommendedSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        recommended = recommendedProducts.map((product) => product?._id);
                    }
                }
                async function getBoughtTogether() {
                    if (csvRow?.bought_together?.trim()) {
                        const boughtTogetherSkus = csvRow?.bought_together?.trim()?.split(",");
                        const boughtTogetherProducts = await service.find({
                            sku: { $in: boughtTogetherSkus },
                            isDelete: false,
                            storeId: storeid
                        });
                        boughtTogether = boughtTogetherProducts.map((product) => product?._id);
                    }
                }

                await Promise.all([getRecommended(), getBoughtTogether()])

                await db.Products.findByIdAndUpdate(product?._id, {
                    recommendedProducts: recommended,
                    boughtTogether
                })
                // await product.save();
            }
        }
    } catch (error) {
        console.error("Error in cretedProductsUpdate:", error);
    }
};

const updateTechnicalDetails = async (technicalDetailsFilePath) => {
    try {
        const technicalDetails = [];
        fs.createReadStream(technicalDetailsFilePath)
            .pipe(csv())
            .on("data", (data) => technicalDetails.push(data))
            .on("end", async () => {
                let obj = {};
                for (const detail of technicalDetails) {
                    // const product = await db.Products.findOne({
                    //     sku: detail.sku,
                    //     isDelete: false,
                    // });
                    if (!obj[detail.sku]) {
                        obj[detail.sku] = [
                            {
                                title: {
                                    en: detail.title,
                                    ar: detail.title,
                                },
                                description: {
                                    en: detail.description,
                                    ar: detail.description,
                                },
                            },
                        ];
                    } else {
                        obj[detail.sku].push({
                            title: {
                                en: detail.title,
                                ar: detail.title,
                            },
                            description: {
                                en: detail.description,
                                ar: detail.description,
                            },
                        });
                    }

                    // if (product) {
                    //     // product.technicalInfo = [];
                    //     product.technicalInfo.push({
                    //         title: {
                    //             en: detail.title,
                    //             ar: detail.title,
                    //         },
                    //         description: {
                    //             en: detail.description,
                    //             ar: detail.description,
                    //         },
                    //     });
                    //     await product.save();
                    // }
                }
                // console.log(obj)

                Object.keys(obj).forEach(async (item) => {
                    await db.Products.findOneAndUpdate({ sku: item }, { technicalInfo: obj[item] }, { new: true });
                });
            });
    } catch (error) {
        console.error("Error in updateTechnicalDetails:", error);
    }
};

const updateSizeDetails = async (sizeDetailsFilePath) => {
    try {
        const sizeDetails = [];
        fs.createReadStream(sizeDetailsFilePath)
            .pipe(csv())
            .on("data", (data) => sizeDetails.push(data))
            .on("end", async () => {
                let obj = {};
                for (const detail of sizeDetails) {
                    let size = await sizeService.findOne({
                        name: detail.size,
                        isDelete: false,
                    });
                    if (!size) size = await createSize({ name: detail.size });

                    // const product = await service.findOne({
                    //     sku: detail.sku,
                    //     isDelete: false,
                    // });

                    if (!obj[detail.sku]) {
                        obj[detail.sku] = [
                            {
                                size: size?._id,
                                stock: detail?.stock,
                                price: detail?.price,
                                offerPrice: detail?.offer_price || null,
                            },
                        ];
                    } else {
                        obj[detail.sku].push({
                            size: size?._id,
                            stock: detail?.stock,
                            price: detail?.price,
                            offerPrice: detail?.offer_price || null,
                        });
                    }

                    // if (product) {
                    //     const sizeIndex = product.sizes?.findIndex((s) => s?.size?._id.toString() === size?._id?.toString());

                    //     const sizeData = {
                    //         size: size?._id,
                    //         stock: detail?.stock,
                    //         price: detail?.price,
                    //         offerPrice: detail?.offer_price || null,
                    //     };

                    //     if (sizeIndex === -1) {
                    //         product.sizes.push(sizeData);
                    //     } else {
                    //         product.sizes[sizeIndex] = sizeData;
                    //     }

                    //     product.customizable = true;
                    //     product.sellingPrice = sizeDetails[0]?.offer_price || sizeDetails[0]?.price;
                    //     await service.update({ _id: product._id }, product);
                    // }
                }
                Object.keys(obj).forEach(async (item) => {
                    await db.Products.findOneAndUpdate({ sku: item }, {
                        sizes: obj[item],
                        sellingPrice: obj[item][0]?.offerPrice || obj[item][0]?.price,
                        customizable: true
                    }, { new: true });
                });
            });
    } catch (error) {
        console.error("Error in updateSizeDetails:", error);
    }
};
const createCategory = async (category) => {
    try {
        const refid = generateUniqueNumber();
        let payload = {
            name: {
                en: category.name,
                ar: category.name,
            },
            slug: await slug.createSlug(db.Category, category.name, {
                slug: await slug.generateSlug(category.name),
                storeId: category?.storeId,
                isDelete: false
            }),
            isRoot: category?.isRoot,
            parent: category?.parent,
            root: category?.root,
            refid: refid,
            categoryId: `CAT-${refid}`,
            storeId: category?.storeId
        };
        let data = await categoryService.create(payload);
        return data;
    } catch (error) {
        console.log(error)
        return error;
    }
};

const createSubCategory = async (category) => {
    try {
        let payload = {
            name: {
                en: category.name,
                ar: category.name,
            },
            slug: await slug.createSlug(db.SubCategory, category.name, {
                slug: await slug.generateSlug(category.name),
            }),
            refid: (await categoryService.SubCount()) + 1,
        };
        let data = await categoryService.createSubCategory(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createAgeGroup = async (agegroup) => {
    try {
        let payload = {
            name: agegroup.name,
            refid: generateUniqueNumber(),
        };
        let data = await ageGroupService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createFrameType = async (frametype) => {
    try {
        let payload = {
            name: {
                en: frametype.name,
                ar: frametype.name,
            },
            slug: await slug.createSlug(db.FrameTypes, frametype.name, {
                slug: await slug.generateSlug(frametype.name),
                storeId: frametype?.storeId
            }),
            refid: generateUniqueNumber(),
            storeId: frametype?.storeId
        };
        let data = await frameTypeService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createFrameShape = async (frameshape) => {
    try {
        let payload = {
            name: {
                en: frameshape.name,
                ar: frameshape.name,
            },
            slug: await slug.createSlug(db.FrameShapes, frameshape.name, {
                slug: await slug.generateSlug(frameshape.name),
                storeId: frameshape?.storeId
            }),
            refid: generateUniqueNumber(),
            storeId: frameshape?.storeId
        };
        let data = await frameShapeService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createLabel = async (label) => {
    try {
        let payload = {
            name: {
                en: label.name,
                ar: label.name,
            },
            refid: generateUniqueNumber(),
            storeId: label?.storeId
        };
        let data = await labelService.create(payload);
        return data;
    } catch (error) {
        console.log(error)
        return error;
    }
};

const createFrontMaterial = async (frontMaterial) => {
    try {
        let payload = {
            name: frontMaterial.name,
            refid: generateUniqueNumber(),
            storeId: frontMaterial?.storeId
        };
        let data = await frontMaterialService.create(payload);
        return data;
    } catch (error) {
        console.log(error)
        return error;
    }
};

const createType = async (type) => {
    try {
        let payload = {
            name: { en: type.name },
            refid: generateUniqueNumber(),
            storeId: type?.storeId
        };
        let data = await typeService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createLensMaterial = async (lensMaterial) => {
    try {
        let payload = {
            name: lensMaterial.name,
            refid: generateUniqueNumber(),
            storeId: lensMaterial?.storeId
        };
        let data = await lensMaterialService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createSize = async (size) => {
    try {
        let payload = {
            name: size.name,
            refid: generateUniqueNumber(),
            storeId: size?.storeId
        };
        let data = await sizeService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createContactSize = async (size) => {
    try {
        let payload = {
            name: size.name,
            refid: generateUniqueNumber(),
            storeId: size?.storeId
        };
        let data = await contactSizeService.create(payload);
        return data;
    } catch (error) {
        return error;
    }
};

const createColor = async (name, value, storeid) => {
    try {
        let colorValues = [];
        let values = value?.split(",");
        for (let value of values) colorValues.push(value);
        let payload = {
            name: { en: name },
            color: colorValues.length > 0 ? colorValues : null,
            refid: generateUniqueNumber(),
            storeId: storeid
        };
        let data = await colorService.create(payload);
        return data;
    } catch (error) {
        console.log(error)
        return error;
    }
};

exports.createContactLens = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const sku = await service.findOne({ sku: body?.sku, isDelete: false, storeId: storeid });
        if (sku) {
            return helper.deliverResponse(
                res,
                200,
                {},
                {
                    error_code: messages.PRODUCTS.SKU_EXISTS.error_code,
                    error_message: messages.PRODUCTS.SKU_EXISTS.error_message,
                }
            );
        }
        let stock = 0;
        if (body?.stock) stock = body?.stock;
        const { email } = res?.locals?.user;
        const adminDetails = await adminService.findOne({ email, isDelete: false });
        body.slug = await slug.createSlug(db.Products, body?.name?.en, {
            slug: await slug.generateSlug(body?.name?.en),
        });
        body.refid = generateUniqueNumber();
        body.createdBy = adminDetails?._id;
        body.storeId = storeid;

        body.offerPercentage = body?.offerPrice?.aed ? ((body?.price?.aed - body?.offerPrice?.aed) / body?.price?.aed) * 100 : 0;
        body.sellingPrice = body?.offerPrice?.aed || body?.price?.aed;

        body.stock = stock;
        const response = await service.create(body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.PRODUCTS.CREATED.error_code,
                error_message: messages.PRODUCTS.CREATED.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(
            res,
            422,
            {},
            {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message,
            }
        );
    }
};

exports.updateContactLens = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const sku = await service.findOne({
            sku: body?.sku,
            isDelete: false,
            refid: { $ne: body?.refid },
            storeId: storeid
        });
        if (sku) {
            return helper.deliverResponse(
                res,
                422,
                {},
                {
                    error_code: messages.PRODUCTS.SKU_EXISTS.error_code,
                    error_message: messages.PRODUCTS.SKU_EXISTS.error_message,
                }
            );
        }

        const productDetails = await service.findOne({
            refid: body?.refid,
            isDelete: false,
        });
        if (body?.name?.en !== productDetails?.name?.en)
            body.slug = await slug.createSlug(db.Products, body?.name?.en, {
                slug: await slug.generateSlug(body?.name?.en),
            });

        if (body?.plans) body.plans = body?.plans;
        body.offerPercentage = body?.offerPrice?.aed ? ((body?.price?.aed - body?.offerPrice?.aed) / body?.price?.aed) * 100 : 0;
        if (!body?.offerPrice?.aed || body?.offerPrice?.aed == "null") body.offerPrice.aed = null;
        body.sellingPrice = body?.offerPrice?.aed || body?.price?.aed;

        if (body?.thumbnail && body?.thumbnail?.includes(process.env.DOMAIN)) {
            body.thumbnail = body.thumbnail.replace(process.env.DOMAIN, "");
        }

        if (body?.seoDetails?.ogImage && body?.seoDetails?.ogImage?.includes(process.env.DOMAIN)) {
            body.seoDetails.ogImage = body.seoDetails?.ogImage.replace(process.env.DOMAIN, "");
        }

        if (body?.images) {
            for (let i = 0; i < body?.images?.length; i++) {
                if (body?.images[i]?.includes(process.env.DOMAIN)) {
                    body.images[i] = body.images[i].replace(process.env.DOMAIN, "");
                }
            }
        }

        if (body?.descriptionImages) {
            for (let i = 0; i < body?.descriptionImages?.length; i++) {
                if (body?.descriptionImages[i]?.includes(process.env.DOMAIN)) {
                    body.descriptionImages[i] = body.descriptionImages[i].replace(process.env.DOMAIN, "");
                }
            }
        }

        const response = await service.update({ refid: body?.refid, isDelete: false }, body);
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.PRODUCTS.UPDATED.error_code,
                error_message: messages.PRODUCTS.UPDATED.error_message,
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

// exports.bulkContactLensImport = async (req, res) => {
//     let csvDir, imagesDir;
//     try {
//         const zip = new AdmZip(req.file.path);
//         const zipEntries = zip.getEntries();

//         csvDir = "tmp/csv";
//         imagesDir = "tmp/images";
//         fs.mkdirSync(csvDir, { recursive: true });
//         fs.mkdirSync(imagesDir, { recursive: true });

//         zipEntries.forEach((entry) => {
//             const fileName = entry.entryName;
//             if (fileName.endsWith(".csv")) {
//                 zip.extractEntryTo(fileName, csvDir, false, true);
//             } else if (/\.(gif|jpe?g|png|webp)$/i.test(fileName)) {
//                 zip.extractEntryTo(fileName, imagesDir, false, true);
//             }
//         });

//         await processContactCSVs(req, csvDir, imagesDir, res);

//         // res.status(200).json({ message: "Products imported successfully" });
//     } catch (error) {
//         console.error(error, " :: Error caught in product import");
//         res.status(422).json({
//             error_code: "90",
//             error_message: "An error occurred while processing the import.",
//         });
//     }
// };

// const processContactCSVs = async (req, csvDir, imagesDir, res) => {
//     res.setHeader("Content-Type", "text/event-stream");
//     res.setHeader("Cache-Control", "no-cache");
//     res.setHeader("Connection", "keep-alive");
//     res.flushHeaders();
//     try {
//         const productsFilePath = `${csvDir}/products.csv`;
//         const products = [];
//         fs.createReadStream(productsFilePath)
//             .pipe(csv())
//             .on("data", (data) => products.push(data))
//             .on("end", async () => {
//                 const totalProducts = products.length;
//                 let processedProducts = 0;

//                 const productImagesDir = "uploads/products/";
//                 fs.mkdirSync(productImagesDir, { recursive: true });

//                 const createdProducts = [];

//                 for (const product of products) {
//                     let productCounter = await service.count({});

//                     const productDetail = await service.findOne({
//                         sku: product.sku,
//                         isDelete: false,
//                     });

//                     let brandDetails = await db.Brands.findOne({
//                         "name.en": product.brand,
//                         isDelete: false,
//                     });
//                     if (!brandDetails) brandDetails = await createBrand({ name: product.brand });

//                     let categories = [];
//                     const productCategories = product.categories?.split(",");
//                     for (let category of productCategories) {
//                         let catDetails = await db.Category.findOne({
//                             "name.en": category,
//                             isDelete: false,
//                         });
//                         if (catDetails) categories.push(catDetails?._id);
//                         else {
//                             catDetails = await createCategory({ name: category, storeId: storeid });
//                             categories.push(catDetails?._id);
//                         }
//                     }

//                     let files = [];
//                     if (product?.product_images) {
//                         let images = product?.product_images?.split(",");
//                         for (let _image of images) {
//                             if (_image) {
//                                 const sourcePath = path.join(imagesDir, _image);
//                                 const destinationPath = path.join(productImagesDir, _image);
//                                 if (fs.existsSync(sourcePath)) {
//                                     fs.copyFileSync(sourcePath, destinationPath);
//                                     files.push(`uploads/products/${_image}`);
//                                 }
//                             }
//                         }
//                     }

//                     let ageGroups = [];
//                     const productAgeGroup = product?.age_groups?.split(",");
//                     for (let age of productAgeGroup) {
//                         let ageDetails = await db.AgeGroups.findOne({
//                             name: age,
//                             isDelete: false,
//                         });
//                         if (ageDetails) ageGroups.push(ageDetails?._id);
//                         else {
//                             ageDetails = await createAgeGroup({ name: age });
//                             ageGroups.push(ageDetails?._id);
//                         }
//                     }

//                     let colorDetail = await db.Colors.findOne({
//                         name: product.colour,
//                         isDelete: false,
//                     });
//                     if (!colorDetail) colorDetail = await createColor(product.colour, product.color_value);

//                     let descImages = [];
//                     if (product?.description_images) {
//                         let descriptionImages = product?.description_images?.split(",");
//                         for (let _image of descriptionImages) {
//                             if (_image) {
//                                 const sourcePath = path.join(imagesDir, _image);
//                                 const destinationPath = path.join(productImagesDir, _image);
//                                 if (fs.existsSync(sourcePath)) {
//                                     fs.copyFileSync(sourcePath, destinationPath);
//                                     descImages.push(`uploads/products/${_image}`);
//                                 }
//                             }
//                         }
//                     }

//                     let labelDetail = await db.Labels.findOne({
//                         "name.en": product.label,
//                         isDelete: false,
//                     });
//                     if (!labelDetail) labelDetail = await createLabel({ name: product.label });

//                     let gender = product?.gender?.split(",").map((gender) => gender) || [];

//                     if (product?.thumb_image) {
//                         const thumbImageSource = path.join(imagesDir, product.thumb_image);
//                         const thumbImageDestination = path.join(productImagesDir, product.thumb_image);
//                         if (fs.existsSync(thumbImageSource)) {
//                             fs.copyFileSync(thumbImageSource, thumbImageDestination);
//                         }
//                     }

//                     let powerPrice = null;
//                     if (product?.sph == "TRUE" || product?.cyl == "TRUE" || product?.axis == "TRUE") {
//                         powerPrice = product?.power_price ? product?.power_price : null;
//                     }

//                     let payload = {
//                         name: {
//                             en: product?.name,
//                             ar: product?.name,
//                         },
//                         refid: productDetail ? productDetail?.refid : productCounter + 1,
//                         slug:
//                             productDetail?.name != product?.name
//                                 ? await slug.createSlug(db.Products, product?.name, {
//                                     slug: await slug.generateSlug(product?.name),
//                                     storeId: storeid,
//                                     isDelete: false
//                                 })
//                                 : productDetail?.slug,
//                         productType: "contactLens",
//                         images: product?.product_images ? files : productDetail?.images ? productDetail?.images : null,
//                         thumbnail: product?.thumb_image ? `uploads/products/${product.thumb_image}` : productDetail?.thumbnail ? productDetail?.thumbnail : null,
//                         brand: brandDetails?._id,
//                         category: categories,
//                         sku: product?.sku,
//                         ageGroup: ageGroups,
//                         color: colorDetail?._id,
//                         stock: product?.stock,
//                         price: {
//                             aed: Number(product?.price),
//                         },
//                         offerPrice: {
//                             aed: product?.offer_price ? Number(product?.offer_price) : null,
//                         },
//                         description: {
//                             en: product?.description,
//                             ar: product?.description,
//                         },
//                         sellingPrice: Number(product?.offer_price) || Number(product?.price),
//                         isActive: product?.is_active == "1" ? true : false,
//                         descriptionImages: product?.description_images ? descImages : productDetail?.descriptionImages ? productDetail?.descriptionImages : null,
//                         isNewArrival: product?.is_new_arrival == "1" ? true : false,
//                         label: labelDetail?._id,
//                         supplierSku: product?.supplier_sku,
//                         upc: product?.upc,
//                         weight: product?.weight,
//                         sph: product?.sph == "TRUE" ? true : false,
//                         cyl: product?.cyl == "TRUE" ? true : false,
//                         axis: product?.axis == "TRUE" ? true : false,
//                         powerPrice: powerPrice,
//                         gender: gender,
//                         weight: product?.weight && Number(product?.weight),
//                     };

//                     const newProduct = productDetail ? await service.update({ _id: productDetail?._id }, payload) : await service.create(payload);

//                     createdProducts.push(newProduct);

//                     processedProducts++;
//                     res.write(`${processedProducts}/${totalProducts}\n\n`);
//                 }

//                 await createdLensProductsUpdate(createdProducts, products, csvDir);
//                 res.write(`event: message\ndata: Products imported successfully\n\n`);
//                 setTimeout(() => {
//                     cleanup(req.file.path, csvDir, imagesDir);
//                 }, 5000);
//                 // Close the connection
//                 res.end();
//             });
//     } catch (error) {
//         helper.deliverResponse(res, 422, error, {
//             error_code: messages.SERVER_ERROR.error_code,
//             error_message: messages.SERVER_ERROR.error_message,
//         });
//     }
// };

// const createdLensProductsUpdate = async (createdProducts, products, csvDir) => {
//     try {
//         for (const product of createdProducts) {
//             const csvRow = products.find((row) => row.sku === product.sku);
//             let boughtTogether;
//             let recommended;
//             async function getRecommended() {
//                 if (csvRow && csvRow?.recommended_products) {
//                     const recommendedSkus = csvRow?.recommended_products?.split(",");
//                     const recommendedProducts = await service.find({
//                         sku: { $in: recommendedSkus },
//                         isDelete: false,
//                     });
//                     // product.recommendedProducts = recommendedProducts.map((product) => product._id);

//                     recommended = recommendedProducts.map((product) => product._id)
//                     // await product.save();
//                 }
//             }
//             async function getBoughtTogether() {
//                 if (csvRow?.bought_together?.trim()) {
//                     const boughtTogetherSkus = csvRow?.bought_together?.split(",");
//                     const boughtTogetherProducts = await service.find({
//                         sku: { $in: boughtTogetherSkus },
//                         isDelete: false,
//                     });
//                     boughtTogether = boughtTogetherProducts.map((product) => product?._id);
//                 }
//             }
//             await Promise.all([getRecommended(), getBoughtTogether()]);
//             await db.Products.findByIdAndUpdate(product?._id, {
//                 boughtTogether,
//                 recommendedProducts: recommended
//             })
//             // await product.save();
//         }
//         // const technicalDetailsFilePath = `${csvDir}/technical_details.csv`;
//         // await updateTechnicalDetails(technicalDetailsFilePath);
//     } catch (error) {
//         console.error("Error in cretedProductsUpdate:", error);
//     }
// };

exports.getVariantColors = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae"
    try {
        const { refid } = req.params;
        const mainProduct = await service.findOne({ refid: refid, isDelete: false, storeId: storeid });
        const variants = mainProduct?.variants || [];
        let colors = [];
        colors.push({
            _id: mainProduct?.color?._id,
            color: mainProduct?.color?.name,
        });
        for (let variant of variants) {
            colors.push({
                _id: variant?.color?._id,
                color: variant?.color?.name,
            });
        }

        const response = {
            mainColor: mainProduct?.color?.name,
            colors: colors,
        };

        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.bulkUpdate = async (req, res) => {
    try {
        const products = await service.find({ isDelete: false });
        for (let product of products) {
            if (product?.customizable) {
                product.sellingPrice = product?.sizes[0]?.offerPrice || product?.sizes[0]?.price;
            } else {
                product.sellingPrice = product?.offerPrice?.aed || product?.price?.aed;
            }
            const updateProduct = await service.updateMany({ _id: product._id }, product);
        }
        res.status(200).json({ message: "Products updated successfully" });
    } catch (error) {
        res.status(500).json(error);
    }
};

exports.exportCSV = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let query = { isDelete: false, storeId: storeid };
        const products = await service.findLean(query)

        let items = []
        let m = 0;
        for (let product of products) {
            const strings = getAllHierarchies([...product?.category, ...product?.subCategory])
            const result = removeSubstrings(strings);
            // console.log(product?.subCategory)
            let sphValues, cylValues, axisValues, addValues;
            if (product.productType == "frame") {
                sphValues = product?.sphValues?.map((power) => power?.name).join(",");
                cylValues = product?.cylValues?.map((power) => power?.name).join(",");
                axisValues = product?.axisValues?.map((power) => power?.name).join(",");
                addValues = product?.addValues?.map((power) => power?.name).join(",");
            } else {
                sphValues = product?.contactSph?.map((power) => power?.name).join(",");
                cylValues = product?.contactCyl?.map((power) => power?.name).join(",");
                axisValues = product?.contactAxis?.map((power) => power?.name).join(",");
            }
            // let sizes = {};
            // for (let [j, size] of product?.sizes?.entries()) {
            //     sizes[`size_${j + 1}`] = size?.size?.name || "";
            //     sizes[`stock_${j + 1}`] = size?.stock || "";
            //     sizes[`price_${j + 1}`] = size?.price || "";
            //     sizes[`offer_price_${j + 1}`] = size?.offerPrice || "";

            // if (j > m) m = j;
            // // The first column need to be defined for all possible columns
            // if (!rowData[0][`size_${j + 1}`]) rowData[0][`size_${j + 1}`] = ""
            // if (!rowData[0][`stock_${j + 1}`]) rowData[0][`stock_${j + 1}`] = ""
            // if (!rowData[0][`price_${j + 1}`]) rowData[0][`price_${j + 1}`] = ""
            // if (!rowData[0][`offer_price_${j + 1}`]) rowData[0][`offer_price_${j + 1}`] = ""
            // }
            console.log(product?.size)
            items.push(
                {
                    slug: product.slug || "",
                    sku: product.sku || "",
                    parent_sku: product?.parent?.sku || "",
                    name_en: product.name.en || "",
                    name_ar: product.name.ar || "",
                    brand: product?.brand?.name?.en || "",
                    categories: result.join(','),
                    product_type: product?.productType || "",
                    description_en: product?.description?.en || "",
                    description_ar: product?.description?.ar || "",
                    description_images: product?.descriptionImages?.join(","),
                    colour: product?.color?.name?.en || "",
                    colour_value: product?.color?.color?.join(","),
                    price: product?.price?.aed || "",
                    offer_price: product?.offerPrice?.aed || "",
                    stock: product?.stock || "",
                    size: product?.productType == "frame" ? (product?.size?.name || "") : (product?.contactSize?.name || ""),
                    thumb_image: product?.thumbnail,
                    product_images: product?.images?.join(","),
                    frame_shape: product?.frameShape?.name?.en || "",
                    frame_type: product?.frameType?.name?.en || "",
                    is_new_arrival: product?.isNewArrival ? 1 : 0,
                    is_returnable: product?.isReturnable ? 1 : 0,
                    label: product.label?.name?.en || "",
                    virtual_try_available: product?.isVirtualTry ? 1 : 0,
                    is_active: product?.isActive ? 1 : 0,
                    is_default_variant: product?.isDefaultVariant ? 1 : 0,
                    // main_product: product?.mainProduct?.sku || "",
                    // variants: product?.variants
                    //     .map((item) => item.sku)
                    //     .join(','),
                    supplier_sku: product?.supplierSku || "",
                    upc: product?.upc || "",
                    sph: product?.sph ? "TRUE" : "",
                    cyl: product?.cyl ? "TRUE" : "",
                    axis: product?.axis ? "TRUE" : "",
                    sphValues: sphValues || "",
                    cylValues: cylValues || "",
                    axisValues: axisValues || "",
                    addValues: addValues || "",
                    power_price: product?.powerPrice || "",
                    front_material: product?.frontMaterial?.map((item) => item?.name?.en)?.join(","),
                    lens_material: product?.lensMaterial?.map((item) => item?.name?.en)?.join(","),
                    type: product?.type?.map((item) => item?.name?.en)?.join(","),
                    gender: product?.gender?.join(","),
                    weight: product?.weight || "",
                    recommended_products: product?.recommendedProducts?.map((item) => item.sku)?.join(','),
                    bought_together: product?.boughtTogether?.map((item) => item.sku)?.join(','),
                    add_to_cart: product?.isAddToCart ? 1 : 0,
                    choose_lens: product?.isChooseLens ? 1 : 0,
                    position: product?.position || "",
                    multi_focal: product?.multiFocal ? "TRUE" : "",
                    show_discount_percentage: product?.showDiscountPercentage ? 1 : 0,
                    isTaxIncluded: product?.isTaxIncluded ? 1 : 0,
                    isCashbackEnabled: product?.isCashbackEnabled ? 1 : 0,
                    cashbackPercentage: product?.cashbackPercentage ?? 0,
                    // ...sizes
                }
            )
        }
        const header = [
            // { id: "slug", title: "slug" },
            { id: "sku", title: "sku" },
            { id: "parent_sku", title: "parent_sku" },
            { id: "name_en", title: "name_en" },
            { id: "name_ar", title: "name_ar" },
            { id: "brand", title: "brand" },
            { id: "categories", title: "categories" },
            { id: "product_type", title: "product_type" },
            { id: "description_en", title: "description_en" },
            { id: "description_ar", title: "description_ar" },
            { id: "description_images", title: "description_images" },
            { id: "colour", title: "colour" },
            { id: "colour_value", title: "colour_value" },
            { id: "price", title: "price" },
            { id: "offer_price", title: "offer_price" },
            { id: "stock", title: "stock" },
            { id: "size", title: "size" },
            { id: "thumb_image", title: "thumb_image" },
            { id: "product_images", title: "product_images" },
            { id: "frame_shape", title: "frame_shape" },
            { id: "frame_type", title: "frame_type" },
            { id: "is_new_arrival", title: "is_new_arrival" },
            { id: "is_returnable", title: "is_returnable" },
            { id: "label", title: "label" },
            { id: "virtual_try_available", title: "virtual_try_available" },
            { id: "is_active", title: "is_active" },
            { id: "is_default_variant", title: "is_default_variant" },
            // { id: "main_product", title: "main_product" },
            // { id: "variants", title: "variants" },
            { id: "supplier_sku", title: "supplier_sku" },
            { id: "upc", title: "upc" },
            { id: "sph", title: "sph" },
            { id: "cyl", title: "cyl" },
            { id: "axis", title: "axis" },
            { id: "sphValues", title: "sphValues" },
            { id: "cylValues", title: "cylValues" },
            { id: "axisValues", title: "axisValues" },
            { id: "addValues", title: "addValues" },
            { id: "power_price", title: "power_price" },
            { id: "front_material", title: "front_material" },
            { id: "lens_material", title: "lens_material" },
            { id: "type", title: "type" },
            { id: "gender", title: "gender" },
            { id: "weight", title: "weight" },
            { id: "recommended_products", title: "recommended_products" },
            { id: "bought_together", title: "bought_together" },
            { id: "add_to_cart", title: "add_to_cart" },
            { id: "choose_lens", title: "choose_lens" },
            { id: "position", title: "position" },
            { id: "multi_focal", title: "multi_focal" },
            { id: "show_discount_percentage", title: "show_discount_percentage" },
            { id: "isTaxIncluded", title: "isTaxIncluded" },
            { id: "isCashbackEnabled", title: "isCashbackEnabled" },
            { id: "cashbackPercentage", title: "cashbackPercentage" },
        ]

        // for (let i of [...Array(m + 1).keys()]) {
        //     header.push({ id: `size_${i + 1}`, title: `size_${i + 1}` })
        //     header.push({ id: `stock_${i + 1}`, title: `stock_${i + 1}` })
        //     header.push({ id: `price_${i + 1}`, title: `price_${i + 1}` })
        //     header.push({ id: `offer_price_${i + 1}`, title: `offer_price_${i + 1}` })
        // }

        const csvWriter = createCsvWriter({
            path: "products.csv",
            header,
            alwaysQuote: true,
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="products.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('products.csv');


        // helper.deliverResponse(res, 200, response, {
        //     error_code: messages.SUCCESS_RESPONSE.error_code,
        //     error_message: messages.SUCCESS_RESPONSE.error_message,
        // });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.exportSunglassCSV = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let query = { isDelete: false, storeId: storeid, productType: "frame" };
        const products = await service.findLean(query)
        let items = []
        let m = 0;
        for (let product of products) {
            const strings = getAllHierarchies([...product?.category, ...product?.subCategory])
            const result = removeSubstrings(strings);
            // console.log(product?.subCategory)
            let sphValues, cylValues, axisValues, addValues;

            sphValues = product?.sphValues?.map((power) => power?.name).join(",");
            cylValues = product?.cylValues?.map((power) => power?.name).join(",");
            axisValues = product?.axisValues?.map((power) => power?.name).join(",");
            addValues = product?.addValues?.map((power) => power?.name).join(",");

            items.push(
                {
                    upc: product?.upc?.trim() || "",
                    sku: product.sku?.trim() || "",
                    supplier_sku: product?.supplierSku?.trim() || "",
                    brand: product?.brand?.name?.en?.trim() || "",
                    categories: result.join(','),
                    model_name: product?.modelName?.trim() || "",
                    model_code: product?.modelCode?.trim() || "",
                    color_code: product?.colorCode?.trim() || "",
                    color: product?.color?.name?.en?.trim() || "",
                    html_color_code: product?.color?.color?.join(","),
                    lense_color: typeof product?.lenseColor == "string" ? product?.lenseColor?.trim() : "",
                    size: product?.size?.name?.trim() || "",
                    size_bridge: product?.sizeBridge?.trim() || "",
                    temple_length: product?.templeLength?.trim() || "",
                    front_material: product?.frontMaterial?.map((item) => item?.name?.en)?.join(","),
                    lens_material: product?.lensMaterial?.map((item) => item?.name?.en)?.join(","),
                    frame_shape: product?.frameShape?.name?.en?.trim() || "",
                    frame_type: product?.frameType?.name?.en?.trim() || "",
                    type: product?.type?.map((item) => item?.name?.en)?.join(","),
                    gender: product?.gender?.join(","),
                    description_en: product?.description?.en?.trim() || "",
                    description_ar: product?.description?.ar?.trim() || "",
                    description_images: product?.descriptionImages?.join(","),
                    price: product?.price?.aed || "",
                    offer_price: product?.offerPrice?.aed || "",
                    thumb_image: product?.thumbnail?.trim(),
                    product_images: product?.images?.join(","),
                    isCashbackEnabled: product?.isCashbackEnabled ? 1 : 0,
                    cashbackPercentage: product?.cashbackPercentage ?? 0,
                    virtual_try_available: product?.isVirtualTry ? 1 : 0,
                    weight: product?.weight || "",
                    position: product?.position || "",
                    isTaxIncluded: product?.isTaxIncluded ? 1 : 0,
                    show_discount_percentage: product?.showDiscountPercentage ? 1 : 0,
                    stock: product?.stock || "",
                    is_new_arrival: product?.isNewArrival ? 1 : 0,
                    is_returnable: product?.isReturnable ? 1 : 0,
                    label: product.label?.name?.en?.trim() || "",
                    recommended_products: product?.recommendedProducts?.map((item) => item.sku)?.join(','),
                    bought_together: product?.boughtTogether?.map((item) => item.sku)?.join(','),
                    is_active: product?.isActive ? 1 : 0,
                    is_default_variant: product?.isDefaultVariant ? 1 : 0,
                    sphValues: sphValues || "",
                    cylValues: cylValues || "",
                    axisValues: axisValues || "",
                    addValues: addValues || "",
                    power_price: product?.powerPrice || "",
                    add_to_cart: product?.isAddToCart ? 1 : 0,
                    choose_lens: product?.isChooseLens ? 1 : 0,
                    meta_title_en: product?.seoDetails?.title?.en?.trim() || "",
                    meta_title_ar: product?.seoDetails?.title?.ar?.trim() || "",
                    meta_description_en: product?.seoDetails?.description?.en?.trim() || "",
                    meta_description_ar: product?.seoDetails?.description?.ar?.trim() || "",
                    meta_keywords_en: product?.seoDetails?.keywords?.en?.trim() || "",
                    meta_keywords_ar: product?.seoDetails?.keywords?.ar?.trim() || "",
                    meta_canonical_en: product?.seoDetails?.canonical?.en?.trim() || "",
                    meta_canonical_ar: product?.seoDetails?.canonical?.ar?.trim() || "",
                    // slug: product.slug || "",
                    // parent_sku: product?.parent?.sku || "",
                    // name_en: product.name.en || "",
                    // name_ar: product.name.ar || "",
                    // product_type: product?.productType || "",
                    // main_product: product?.mainProduct?.sku || "",
                    // variants: product?.variants
                    //     .map((item) => item.sku)
                    //     .join(','),
                    // sph: product?.sph ? 1 : 0,
                    // cyl: product?.cyl ? 1 : 0,
                    // axis: product?.axis ? 1 : 0,
                    // multi_focal: product?.multiFocal ? "TRUE" : "",
                    // ...sizes
                }
            )
        }
        const header = [
            { id: "upc", title: "upc" },
            { id: "sku", title: "sku" },
            { id: "supplier_sku", title: "supplier_sku" },
            { id: "brand", title: "brand" },
            { id: "categories", title: "categories" },
            { id: "model_name", title: "model_name" },
            { id: "model_code", title: "model_code" },
            { id: "color_code", title: "color_code" },
            { id: "color", title: "color" },
            { id: "html_color_code", title: "html_color_code" },
            { id: "lense_color", title: "lense_color" },
            { id: "size", title: "size" },
            { id: "size_bridge", title: "size_bridge" },
            { id: "temple_length", title: "temple_length" },
            { id: "front_material", title: "front_material" },
            { id: "lens_material", title: "lens_material" },
            { id: "frame_shape", title: "frame_shape" },
            { id: "frame_type", title: "frame_type" },
            { id: "type", title: "type" },
            { id: "gender", title: "gender" },
            { id: "description_en", title: "description_en" },
            { id: "description_ar", title: "description_ar" },
            { id: "description_images", title: "description_images" },
            { id: "price", title: "price" },
            { id: "offer_price", title: "offer_price" },
            { id: "thumb_image", title: "thumb_image" },
            { id: "product_images", title: "product_images" },
            { id: "isCashbackEnabled", title: "isCashbackEnabled" },
            { id: "cashbackPercentage", title: "cashbackPercentage" },
            { id: "virtual_try_available", title: "virtual_try_available" },
            { id: "weight", title: "weight" },
            { id: "position", title: "position" },
            { id: "isTaxIncluded", title: "isTaxIncluded" },
            { id: "show_discount_percentage", title: "show_discount_percentage" },
            { id: "stock", title: "stock" },
            { id: "is_new_arrival", title: "is_new_arrival" },
            { id: "is_returnable", title: "is_returnable" },
            { id: "label", title: "label" },
            { id: "recommended_products", title: "recommended_products" },
            { id: "bought_together", title: "bought_together" },
            { id: "is_active", title: "is_active" },
            { id: "is_default_variant", title: "is_default_variant" },
            { id: "sphValues", title: "sphValues" },
            { id: "cylValues", title: "cylValues" },
            { id: "axisValues", title: "axisValues" },
            { id: "addValues", title: "addValues" },
            { id: "power_price", title: "power_price" },
            { id: "add_to_cart", title: "add_to_cart" },
            { id: "choose_lens", title: "choose_lens" },
            { id: "meta_title_en", title: "meta_title_en" },
            { id: "meta_title_ar", title: "meta_title_ar" },
            { id: "meta_description_en", title: "meta_description_en" },
            { id: "meta_description_ar", title: "meta_description_ar" },
            { id: "meta_keywords_en", title: "meta_keywords_en" },
            { id: "meta_keywords_ar", title: "meta_keywords_ar" },
            { id: "meta_canonical_en", title: "meta_canonical_en" },
            { id: "meta_canonical_ar", title: "meta_canonical_ar" },
        ]

        // for (let i of [...Array(m + 1).keys()]) {
        //     header.push({ id: `size_${i + 1}`, title: `size_${i + 1}` })
        //     header.push({ id: `stock_${i + 1}`, title: `stock_${i + 1}` })
        //     header.push({ id: `price_${i + 1}`, title: `price_${i + 1}` })
        //     header.push({ id: `offer_price_${i + 1}`, title: `offer_price_${i + 1}` })
        // }

        const csvWriter = createCsvWriter({
            path: "products.csv",
            header,
            alwaysQuote: true,
            // encoding: "utf16le"
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="products.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('products.csv');


        // helper.deliverResponse(res, 200, response, {
        //     error_code: messages.SUCCESS_RESPONSE.error_code,
        //     error_message: messages.SUCCESS_RESPONSE.error_message,
        // });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.exportContactLensCSV = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let query = { isDelete: false, storeId: storeid, productType: "contactLens" };
        const products = await service.findLean(query)
        let items = []
        let m = 0;
        for (let product of products) {
            const strings = getAllHierarchies([...product?.category, ...product?.subCategory])
            const result = removeSubstrings(strings);
            let sphValues, cylValues, axisValues, addValues;
            sphValues = product?.contactSph?.map((power) => power?.name).join(",");
            cylValues = product?.contactCyl?.map((power) => power?.name).join(",");
            axisValues = product?.contactAxis?.map((power) => power?.name).join(",");
            items.push(
                {
                    upc: product?.upc?.trim() || "",
                    sku: product.sku?.trim() || "",
                    supplier_sku: product?.supplierSku?.trim() || "",
                    name_en: product.name.en || "",
                    name_ar: product?.name?.ar || "",
                    categories: result.join(','),
                    brand: product?.brand?.name?.en?.trim() || "",
                    sub_brand: product?.subBrand?.name?.en?.trim() || "",
                    color: product?.color?.name?.en?.trim() || "",
                    html_color_code: product?.color?.color?.join(","),
                    size: product?.size?.name?.trim() || "",
                    gender: product?.gender?.join(","),
                    description_en: product?.description?.en?.trim() || "",
                    description_ar: product?.description?.ar?.trim() || "",
                    description_two_en: product?.descriptionTwo?.en?.trim() || "",
                    description_two_ar: product?.descriptionTwo?.ar?.trim() || "",
                    description_images: product?.descriptionImages?.join(","),
                    price: product?.price?.aed || "",
                    offer_price: product?.offerPrice?.aed || "",
                    thumb_image: product?.thumbnail?.trim(),
                    product_images: product?.images?.join(","),
                    sph: product?.sph ? 1 : 0,
                    cyl: product?.cyl ? 1 : 0,
                    axis: product?.axis ? 1 : 0,
                    multi_focal: product?.multiFocal ? 1 : 0,
                    sphValues: sphValues || "",
                    cylValues: cylValues || "",
                    axisValues: axisValues || "",
                    power_price: product?.powerPrice || "",
                    isCashbackEnabled: product?.isCashbackEnabled ? 1 : 0,
                    cashbackPercentage: product?.cashbackPercentage ?? 0,
                    // virtual_try_available: product?.isVirtualTry ? 1 : 0,
                    weight: product?.weight || "",
                    position: product?.position || "",
                    isTaxIncluded: product?.isTaxIncluded ? 1 : 0,
                    show_discount_percentage: product?.showDiscountPercentage ? 1 : 0,
                    stock: product?.stock || "",
                    is_new_arrival: product?.isNewArrival ? 1 : 0,
                    is_returnable: product?.isReturnable ? 1 : 0,
                    label: product.label?.name?.en?.trim() || "",
                    recommended_products: product?.recommendedProducts?.map((item) => item.sku)?.join(','),
                    bought_together: product?.boughtTogether?.map((item) => item.sku)?.join(','),
                    is_active: product?.isActive ? 1 : 0,
                    is_default_variant: product?.isDefaultVariant ? 1 : 0,
                    // addValues: addValues || "",
                    add_to_cart: product?.isAddToCart ? 1 : 0,
                    // choose_lens: product?.isChooseLens ? 1 : 0,
                    meta_title_en: product?.seoDetails?.title?.en?.trim() || "",
                    meta_title_ar: product?.seoDetails?.title?.ar?.trim() || "",
                    meta_description_en: product?.seoDetails?.description?.en?.trim() || "",
                    meta_description_ar: product?.seoDetails?.description?.ar?.trim() || "",
                    meta_keywords_en: product?.seoDetails?.keywords?.en?.trim() || "",
                    meta_keywords_ar: product?.seoDetails?.keywords?.ar?.trim() || "",
                    meta_canonical_en: product?.seoDetails?.canonical?.en?.trim() || "",
                    meta_canonical_ar: product?.seoDetails?.canonical?.ar?.trim() || "",
                    // slug: product.slug || "",
                    // parent_sku: product?.parent?.sku || "",
                    // product_type: product?.productType || "",
                    // main_product: product?.mainProduct?.sku || "",
                    // variants: product?.variants
                    //     .map((item) => item.sku)
                    //     .join(','),
                    // sph: product?.sph ? 1 : 0,
                    // cyl: product?.cyl ? 1 : 0,
                    // axis: product?.axis ? 1 : 0,
                    // ...sizes
                }
            )
        }
        const header = [
            { id: "upc", title: "upc" },
            { id: "sku", title: "sku" },
            { id: "supplier_sku", title: "supplier_sku" },
            { id: "name_en", title: "name_en" },
            { id: "name_ar", title: "name_ar" },
            { id: "categories", title: "categories" },
            { id: "brand", title: "brand" },
            { id: "sub_brand", title: "sub_brand" },
            { id: "color", title: "color" },
            { id: "html_color_code", title: "html_color_code" },
            { id: "size", title: "size" },
            { id: "gender", title: "gender" },
            { id: "description_en", title: "description_en" },
            { id: "description_ar", title: "description_ar" },
            { id: "description_two_en", title: "description_two_en" },
            { id: "description_two_ar", title: "description_two_ar" },
            { id: "description_images", title: "description_images" },
            { id: "price", title: "price" },
            { id: "offer_price", title: "offer_price" },
            { id: "thumb_image", title: "thumb_image" },
            { id: "product_images", title: "product_images" },
            { id: "sph", title: "sph" },
            { id: "cyl", title: "cyl" },
            { id: "axis", title: "axis" },
            { id: "multi_focal", title: "multi_focal" },
            { id: "sphValues", title: "sphValues" },
            { id: "cylValues", title: "cylValues" },
            { id: "axisValues", title: "axisValues" },
            { id: "power_price", title: "power_price" },
            { id: "isCashbackEnabled", title: "isCashbackEnabled" },
            { id: "cashbackPercentage", title: "cashbackPercentage" },
            { id: "weight", title: "weight" },
            { id: "position", title: "position" },
            { id: "isTaxIncluded", title: "isTaxIncluded" },
            { id: "show_discount_percentage", title: "show_discount_percentage" },
            { id: "stock", title: "stock" },
            { id: "is_new_arrival", title: "is_new_arrival" },
            { id: "is_returnable", title: "is_returnable" },
            { id: "label", title: "label" },
            { id: "recommended_products", title: "recommended_products" },
            { id: "bought_together", title: "bought_together" },
            { id: "is_active", title: "is_active" },
            { id: "is_default_variant", title: "is_default_variant" },
            { id: "add_to_cart", title: "add_to_cart" },
            { id: "meta_title_en", title: "meta_title_en" },
            { id: "meta_title_ar", title: "meta_title_ar" },
            { id: "meta_description_en", title: "meta_description_en" },
            { id: "meta_description_ar", title: "meta_description_ar" },
            { id: "meta_keywords_en", title: "meta_keywords_en" },
            { id: "meta_keywords_ar", title: "meta_keywords_ar" },
            { id: "meta_canonical_en", title: "meta_canonical_en" },
            { id: "meta_canonical_ar", title: "meta_canonical_ar" },
        ]

        // for (let i of [...Array(m + 1).keys()]) {
        //     header.push({ id: `size_${i + 1}`, title: `size_${i + 1}` })
        //     header.push({ id: `stock_${i + 1}`, title: `stock_${i + 1}` })
        //     header.push({ id: `price_${i + 1}`, title: `price_${i + 1}` })
        //     header.push({ id: `offer_price_${i + 1}`, title: `offer_price_${i + 1}` })
        // }

        const csvWriter = createCsvWriter({
            path: "products.csv",
            header,
            alwaysQuote: true,
            // encoding: "utf16le"
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="products.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('products.csv');


        // helper.deliverResponse(res, 200, response, {
        //     error_code: messages.SUCCESS_RESPONSE.error_code,
        //     error_message: messages.SUCCESS_RESPONSE.error_message,
        // });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

exports.exportAccessoryCSV = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae"
        let query = { isDelete: false, storeId: storeid, productType: "accessory" };
        const products = await service.findLean(query)
        let items = []
        let m = 0;
        for (let product of products) {
            const strings = getAllHierarchies([...product?.category, ...product?.subCategory])
            const result = removeSubstrings(strings);

            items.push(
                {
                    upc: product?.upc?.trim() || "",
                    sku: product.sku?.trim() || "",
                    supplier_sku: product?.supplierSku?.trim() || "",
                    name_en: product.name.en || "",
                    name_ar: product?.name?.ar || "",
                    categories: result.join(','),
                    brand: product?.brand?.name?.en?.trim() || "",
                    color: product?.color?.name?.en?.trim() || "",
                    html_color_code: product?.color?.color?.join(","),
                    size: product?.size?.name?.trim() || "",
                    gender: product?.gender?.join(","),
                    description_en: product?.description?.en?.trim() || "",
                    description_ar: product?.description?.ar?.trim() || "",
                    description_two_en: product?.descriptionTwo?.en?.trim() || "",
                    description_two_ar: product?.descriptionTwo?.ar?.trim() || "",
                    description_images: product?.descriptionImages?.join(","),
                    price: product?.price?.aed || "",
                    offer_price: product?.offerPrice?.aed || "",
                    thumb_image: product?.thumbnail?.trim(),
                    product_images: product?.images?.join(","),
                    isCashbackEnabled: product?.isCashbackEnabled ? 1 : 0,
                    cashbackPercentage: product?.cashbackPercentage ?? 0,
                    weight: product?.weight || "",
                    position: product?.position || "",
                    isTaxIncluded: product?.isTaxIncluded ? 1 : 0,
                    show_discount_percentage: product?.showDiscountPercentage ? 1 : 0,
                    stock: product?.stock || "",
                    is_new_arrival: product?.isNewArrival ? 1 : 0,
                    is_returnable: product?.isReturnable ? 1 : 0,
                    label: product.label?.name?.en?.trim() || "",
                    recommended_products: product?.recommendedProducts?.map((item) => item.sku)?.join(','),
                    bought_together: product?.boughtTogether?.map((item) => item.sku)?.join(','),
                    is_active: product?.isActive ? 1 : 0,
                    is_default_variant: product?.isDefaultVariant ? 1 : 0,
                    add_to_cart: product?.isAddToCart ? 1 : 0,
                    meta_title_en: product?.seoDetails?.title?.en?.trim() || "",
                    meta_title_ar: product?.seoDetails?.title?.ar?.trim() || "",
                    meta_description_en: product?.seoDetails?.description?.en?.trim() || "",
                    meta_description_ar: product?.seoDetails?.description?.ar?.trim() || "",
                    meta_keywords_en: product?.seoDetails?.keywords?.en?.trim() || "",
                    meta_keywords_ar: product?.seoDetails?.keywords?.ar?.trim() || "",
                    meta_canonical_en: product?.seoDetails?.canonical?.en?.trim() || "",
                    meta_canonical_ar: product?.seoDetails?.canonical?.ar?.trim() || "",
                }
            )
        }
        const header = [
            { id: "upc", title: "upc" },
            { id: "sku", title: "sku" },
            { id: "supplier_sku", title: "supplier_sku" },
            { id: "name_en", title: "name_en" },
            { id: "name_ar", title: "name_ar" },
            { id: "categories", title: "categories" },
            { id: "brand", title: "brand" },
            { id: "color", title: "color" },
            { id: "html_color_code", title: "html_color_code" },
            { id: "size", title: "size" },
            { id: "gender", title: "gender" },
            { id: "description_en", title: "description_en" },
            { id: "description_ar", title: "description_ar" },
            { id: "description_two_en", title: "description_two_en" },
            { id: "description_two_ar", title: "description_two_ar" },
            { id: "description_images", title: "description_images" },
            { id: "price", title: "price" },
            { id: "offer_price", title: "offer_price" },
            { id: "thumb_image", title: "thumb_image" },
            { id: "product_images", title: "product_images" },
            { id: "isCashbackEnabled", title: "isCashbackEnabled" },
            { id: "cashbackPercentage", title: "cashbackPercentage" },
            { id: "weight", title: "weight" },
            { id: "position", title: "position" },
            { id: "isTaxIncluded", title: "isTaxIncluded" },
            { id: "show_discount_percentage", title: "show_discount_percentage" },
            { id: "stock", title: "stock" },
            { id: "is_new_arrival", title: "is_new_arrival" },
            { id: "is_returnable", title: "is_returnable" },
            { id: "label", title: "label" },
            { id: "recommended_products", title: "recommended_products" },
            { id: "bought_together", title: "bought_together" },
            { id: "is_active", title: "is_active" },
            { id: "is_default_variant", title: "is_default_variant" },
            { id: "add_to_cart", title: "add_to_cart" },
            { id: "meta_title_en", title: "meta_title_en" },
            { id: "meta_title_ar", title: "meta_title_ar" },
            { id: "meta_description_en", title: "meta_description_en" },
            { id: "meta_description_ar", title: "meta_description_ar" },
            { id: "meta_keywords_en", title: "meta_keywords_en" },
            { id: "meta_keywords_ar", title: "meta_keywords_ar" },
            { id: "meta_canonical_en", title: "meta_canonical_en" },
            { id: "meta_canonical_ar", title: "meta_canonical_ar" },
        ]

        // for (let i of [...Array(m + 1).keys()]) {
        //     header.push({ id: `size_${i + 1}`, title: `size_${i + 1}` })
        //     header.push({ id: `stock_${i + 1}`, title: `stock_${i + 1}` })
        //     header.push({ id: `price_${i + 1}`, title: `price_${i + 1}` })
        //     header.push({ id: `offer_price_${i + 1}`, title: `offer_price_${i + 1}` })
        // }

        const csvWriter = createCsvWriter({
            path: "products.csv",
            header,
            alwaysQuote: true,
            // encoding: "utf16le"
        });

        await csvWriter.writeRecords(items);

        // Respond with CSV file download
        res.setHeader('Content-Disposition', 'attachment; filename="products.csv"');
        res.setHeader('Content-Type', 'text/csv');
        res.download('products.csv');


        // helper.deliverResponse(res, 200, response, {
        //     error_code: messages.SUCCESS_RESPONSE.error_code,
        //     error_message: messages.SUCCESS_RESPONSE.error_message,
        // });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
};

function getAllHierarchies(categories) {
    // Create a map for quick lookup by id
    const categoryMap = new Map()
    categories.forEach(category => categoryMap.set(category._id?.toString(), category))
    // Create a set to track processed categories
    const processedCategories = new Set()

    // Recursive function to build hierarchy string
    function buildHierarchy(subcategoryId) {
        const category = categoryMap.get(subcategoryId?.toString())
        if (!category) return ""

        let hierarchy = category.name?.en

        if (category.parent) {
            const parentHierarchy = buildHierarchy(category.parent)
            if (parentHierarchy) {
                hierarchy = `${parentHierarchy}>${hierarchy}`
            }
        } else {
            hierarchy = category.name?.en
        }

        return hierarchy
    }

    // Find all root categories and build hierarchies
    const hierarchies = new Set()

    categories.forEach(category => {
        if (category.isRoot) {
            // If it has no parentId, it is a root category
            // Traverse all its descendants
            const queue = [category._id?.toString()]

            while (queue.length > 0) {
                const currentId = queue.shift()
                const currentCategory = categoryMap.get(currentId?.toString())
                if (currentCategory && !processedCategories.has(currentId?.toString())) {
                    // Process this category
                    processedCategories.add(currentId?.toString())
                    const hierarchy = buildHierarchy(currentId)
                    hierarchies.add(hierarchy)

                    // Add children to the queue
                    categories.forEach(cat => {
                        if (cat.parent?.toString() === currentId?.toString()) {
                            queue.push(cat._id?.toString())
                        }
                    })
                }
            }
        }
    })

    return Array.from(hierarchies)
}


function removeSubstrings(arr) {
    // Sort array by length, longest first, to ensure that substrings are removed first
    arr.sort((a, b) => b.length - a.length)

    // Create a new array to hold the unique strings
    const uniqueArr = [...arr]

    // Iterate over each string in the array
    for (let i = 0; i < arr.length; i++) {
        let isSubstring = false
        let j = 0
        // Check if the current string is a substring of any string already in uniqueArr
        for (j = 0; j < uniqueArr.length; j++) {
            if (arr[i].includes(uniqueArr[j]) && uniqueArr[j] !== arr[i]) {
                isSubstring = true
                break
            }
        }

        // If the string is not a substring, add it to the uniqueArr
        if (isSubstring) {
            uniqueArr.splice(j, 1)
        }
    }

    return uniqueArr
}

exports.generateXmlFeed = async (req, res) => {
    try {
        const products = await service.find({ isDelete: false, isActive: true, storeId: "ae" })
        const feed = xmlbuilder.create('rss', { version: '1.0', encoding: 'UTF-8' })
            .att('xmlns:g', 'http://base.google.com/ns/1.0')
            .ele('channel')
            .ele('title', 'Yateem')
            .up()
            .ele('link', 'https://yateem.com')
            .up()
            .ele('description', 'Product feed for Google Shopping')
            .up();

        products.forEach(product => {
            // console.log(product.brand)
            let price;
            let stock = 0;
            if (product?.customizable) {
                if (product?.productType === 'frame') {
                    const sizeDetails = product?.sizes[0]
                    price = sizeDetails?.offerPrice < 1 || sizeDetails?.offerPrice >= sizeDetails?.price
                        ? sizeDetails?.price
                        : sizeDetails?.offerPrice;
                    stock = sizeDetails?.stock
                } else {
                    const sizeDetails = product?.contactSizes[0]
                    price = sizeDetails?.offerPrice < 1 || sizeDetails?.offerPrice >= sizeDetails?.price
                        ? sizeDetails?.price
                        : sizeDetails?.offerPrice;
                    stock = sizeDetails?.stock
                }
            } else {
                price = product?.offerPrice?.aed < 1 || product?.offerPrice?.aed >= product?.price?.aed
                    ? product?.price?.aed
                    : product?.offerPrice?.aed;
                stock = product?.stock
            }

            const availability = stock > 0 ? 'in_stock' : 'out_of_stock';
            const cat = product?.category?.find(item => item?.isRoot == true) ?? product?.category[0]
            feed.ele('item')
                .ele('g:id', product._id).up()
                .ele('g:title', product.name.en).up()
                .ele('g:sku', product.sku).up()
                .ele('g:description', product.description.en).up()
                .ele('g:price', `${price} AED`).up()
                .ele('g:link', `${process.env.FRONTEND_URL}/en/product/${product.slug}`).up()
                .ele('g:image_link', `${process.env.DOMAIN}${product.images[0]}`).up()
                .ele('g:brand', product?.brand?.name?.en).up()
                .ele('g:product_category', cat?.name?.en).up()
                .ele('g:availability', availability).up()
                .ele('g:quantity', stock).up()
                .ele('g:product_type', product?.productType).up()
                .ele('g:condition', "new").up()
                .ele('g:identifier_exists', false).up();
        });

        const xmlFeed = feed.end({ pretty: true });
        fs.writeFileSync('public/google_feed.xml', xmlFeed);

        console.log('Feed generated successfully!');

    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.slugsForSitemap = async (req, res) => {
    try {
        const { storeid } = req.headers
        const [products, brands, categories] = await Promise.all([
            db.Products.find({ isDelete: false, isActive: true, storeId: storeid }, { slug: 1 }),
            db.Brands.find({ isDelete: false, isActive: true, storeId: storeid }, { slug: 1 }),
            db.Category.find({ isDelete: false, isActive: true, storeId: storeid }, { slug: 1 })
        ])
        helper.deliverResponse(res, 200, {
            products: products.map(item => item?.slug),
            brands: brands.map(item => item?.slug),
            categories: categories.map(item => item?.slug)
        }, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });

    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}