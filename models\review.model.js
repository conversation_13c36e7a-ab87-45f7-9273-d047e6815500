const mongoose = require('mongoose')

const reviewSchema = new mongoose.Schema({
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers" },
    message: { type: String },
    date: { type: Date, default: Date.now },
    product: { type: mongoose.Schema.Types.ObjectId, ref: "products" },
    rating: { type: Number },
    images: [{ type: String }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    isApproved: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'ae' }
}, { timestamps: true })

module.exports = mongoose.model('reviews', reviewSchema)