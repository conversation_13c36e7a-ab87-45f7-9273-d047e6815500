const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.ProductEnquiry(data);
        await response.save();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.ProductEnquiry.find(query, projection).sort({createdAt: -1})
            .populate("brand", "name");
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.ProductEnquiry.findOne(query, projection);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.ProductEnquiry.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false,
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.ProductEnquiry.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};
