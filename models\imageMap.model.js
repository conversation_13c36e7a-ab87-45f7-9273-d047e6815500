const mongoose = require("mongoose");

const imageMapSchema = new mongoose.Schema({
    image: { type: String, required: true },
    items: [{
        positionX : { type: Number, required: true },
        positionY : { type: Number, required: true },
        image: { type: String, required: true },
        title: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        buttonText: {
            en: { type: String, required: true },
            ar: { type: String }
        },
        link: { type: String, required: true },
    }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("imageMap", imageMapSchema)