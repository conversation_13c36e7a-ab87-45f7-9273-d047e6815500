const messages = require('../config/constants/messages');
const service = require('../services/banner.service');
const helper = require('../util/responseHelper');
const adminService = require('../services/admin.users.service');

exports.addBanner = async (req, res) => {
    try {
        let { data } = req.body;
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        data = JSON.parse(data);
        data.refid = await service.count({}) + 1
        if (req.files.length > 0) {
            for (let i = 0; i < req.files.length; i++) {
                for (let file of data?.files) {
                    file.file = req.files[i].path
                    data.files[i] = file
                }
            }
        }
        const result = await service.create({ ...data, storeId: storeid });
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, result, {
                error_code: messages.BANNER.CREATED.error_code,
                error_message: messages.BANNER.CREATED.error_message
            });
        } else {
            return helper.deliverResponse(res, 200, result, {
                error_code: messages.BANNER.CREATED.error_code,
                error_message: messages.BANNER.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        })
    }
}

exports.list = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.find({ storeId: storeid });
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.detail = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.findOne({ refid: req.params.id, storeId: storeid });
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.update({ refid: req.params.id, storeId: storeid }, req.body);
        helper.deliverResponse(res, 200, result, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}