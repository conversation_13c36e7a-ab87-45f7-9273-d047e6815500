const express = require('express');
const router = express.Router();
const controller = require('../../controllers/customer.controller');
const authorize = require('../../middlewares/authorize');
const upload = require('../../util/upload');
const prescriptionController = require('../../controllers/prescription.controller');

module.exports = () => {

    router.post('/login', controller.getOtp);
    router.post('/verify-otp', controller.verifyOtp);

    router.post('/register', authorize.verifyToken, controller.register);
    router.get('/profile', authorize.verifyToken, controller.profile);

    router.post('/update-profile', authorize.verifyToken, controller.updateProfile);
    router.get('/wishlist', authorize.verifyToken, controller.wishlist);

    router.post('/add-address', authorize.verifyToken, controller.addAddress);
    router.get('/address', authorize.verifyToken, controller.getAddress);
    router.put('/update-address/:refid', authorize.verifyToken, controller.updateAddress);
    router.put('/delete-address/:refid', authorize.verifyToken, controller.deleteAddress);

    router.post('/upload-prescription', authorize.verifyToken, upload.single('file'), prescriptionController.create);
    router.get('/prescriptions', authorize.verifyToken, prescriptionController.getPrescriptions);
    router.put('/delete-prescription/:refid', authorize.verifyToken, prescriptionController.update);

    router.post('/add-try-cart', controller.validate('add-try-cart'), authorize.verifyToken, controller.addTryCart);
    router.get('/try-cart', authorize.verifyToken, controller.getTryCart);
    router.post('/remove-try-cart', authorize.verifyToken, controller.removeTryCart);

    router.get('/counts', controller.getCounts);

    router.get('/my-cashbacks', authorize.verifyToken, controller.myCashbacks);

    return router;
}