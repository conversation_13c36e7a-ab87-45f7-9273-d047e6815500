const mongoose = require("mongoose");

const tryCartSchema = new mongoose.Schema({
    products: [{ type: mongoose.Schema.Types.ObjectId, ref: "products" }],
    duration: { type: String },
    amount: { type: Number },
    currency: { type: String },
    isDelete: { type: Boolean, default: false },
    refid: { type: String, required: true },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model("trycart", tryCartSchema);