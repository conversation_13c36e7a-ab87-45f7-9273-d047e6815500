const messages = require('../config/constants/messages');
const helper = require('../util/responseHelper');
const service = require('../services/contact.size.service');
const productService = require('../services/products.service');
const generateUniqueNumber = require('../util/getRefid');

exports.create = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            return helper.deliverResponse(res, 200, response, {
                error_code: messages.SIZES.CREATED.error_code,
                error_message: messages.SIZES.CREATED.error_message
            });
        }
    } catch (error) {
        helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.list = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        let query = { isDelete: false, storeId: storeid };
        if (body?.isActive) query.isActive = body?.isActive
        const response = await service.find(query);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.detail = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ 
            refid: req?.params?.refid, 
            isDelete: false,
            storeId: storeid 
        });
        if (!response) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        }
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        console.log(body)
        const response = await service.update(
            { refid: req?.params?.refid, isDelete: false, storeId: storeid }, 
            body
        );
        console.log(response)
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            });
        } else {
            return helper.deliverResponse(res, 200, response, {
                error_code: messages.SIZES.UPDATED.error_code,
                error_message: messages.SIZES.UPDATED.error_message
            });
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    try {
        const { refid } = req.params;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const size = await service.findOne({ refid, isDelete: false, storeId: storeid });
        const products = await productService.find({ 'sizes.size': size?._id, isDelete: false, storeId: storeid });
        if (products.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Size is used in products"
            });
            return;
        }
        await service.update({ refid, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.SIZES.DELETED.error_code,
            error_message: messages.SIZES.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}