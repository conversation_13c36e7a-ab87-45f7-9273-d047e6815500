const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/product.head.service");
const productService = require("../services/products.service");
const generateUniqueNumber = require("../util/getRefid");

exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        const isSkuExist = await service.findOne({ sku: body?.sku, isDelete: false, storeId: storeid });
        if (isSkuExist) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "SKU Already Exists"
            });
        }
        body.refid = generateUniqueNumber();

        let defaultPromise = []

        for (let [key, product] of body?.products?.entries()) {
            defaultPromise.push(productService.update({ _id: product?.value, storeId: storeid }, { isDefaultVariant: product?.isDefault }))
        }

        await Promise.all(defaultPromise)

        const data = {
            refid: body.refid,
            name: body?.name,
            sku: body?.sku,
            type: body?.type,
            storeId: storeid,
            products: body?.products?.map((product) => product?.value)
        }

        const response = await service.create(data);

        let productPromise = []
        for (let product of body?.products) {
            productPromise.push(productService.update({ _id: product?.value, storeId: storeid }, { parent: response?._id }))
        }

        await Promise.all(productPromise)

        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, response, messages.NON_LIST.CREATED);
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.find = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { page, limit, sort } = req.query;

        page = page ?? 0
        limit = limit ?? 0
        sort = sort ?? { createdAt: -1 }

        const { body } = req;
        
        const filters = body?.filters ?? {}
        let query = { isDelete: false, storeId: storeid, ...filters };
        if(body.isActive) query.isActive = body?.isActive;
        
        if (body?.keyword) {
            query["$or"] = [
                { "name": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "sku": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
                { "refid": { $regex: ".*" + body?.keyword + ".*", $options: "i" } },
            ]
        }

        const [productHeads, total] = await Promise.all([
            service.find(query, {}, page, limit, sort),
            service.count(query)
        ])

        helper.deliverResponse(res, 200, {productHeads, total}, messages.SUCCESS_RESPONSE);
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.findOne = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if (!response) {
            helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, response, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        let { refid } = req.params;

        const head = await service.findOne({ refid, isDelete: false, storeId: storeid })

        const isSkuExist = await service.findOne({ sku: body?.sku, isDelete: false, storeId: storeid, _id: { $ne: head?._id } });
        if (isSkuExist) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "SKU Already Exists"
            });
        }

        for (let [key, product] of body?.products?.entries()) {
            await productService.update({ _id: product?.value, storeId: storeid }, { isDefaultVariant: product?.isDefault, parent: head?._id })
        }

        const data = {
            refid: body.refid,
            name: body?.name,
            sku: body?.sku,
            type: body?.type,
            storeId: storeid,
            products: body?.products?.map((product) => product?.value)
        }

        const response = await service.update({ refid, isDelete: false, storeId: storeid }, data);
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, response, messages.NON_LIST.UPDATED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const response = await service.update({ refid: refid, isDelete: false, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, messages.NON_LIST.DELETED);
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.checkProductAssociation = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { productId } = req.body;
        
        const [productHeadExists, product] = await Promise.all([
            service.findOne({ products: { $in: [productId] }, isDelete: false, storeId: storeid }),
            productService.findOne({ _id: productId, isDelete: false, storeId: storeid }, { productType: 1 })
        ]) 

        if (productHeadExists) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Product is already associated with a product head"
            });
        }

        helper.deliverResponse(res, 200, {
            type: product?.productType
        }, {
            error_code: 0,
            error_message: "Product is not associated with any product head"
        });
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};
