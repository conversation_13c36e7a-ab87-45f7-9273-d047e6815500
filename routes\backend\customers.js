const express = require("express");
const router = express.Router();
const controller = require("../../controllers/customer.controller");
const cartController = require("../../controllers/cart.controller");
const upload = require('../../util/upload')

module.exports = () => {
    router.post("/create-customer", controller.create);
    router.post("/get-customers", controller.getCustomers);
    router.get("/customer-details/:refid", controller.customerDetails);
    router.post("/update-customer", controller.update);

    router.post("/add-address", controller.validate("add-address"), controller.addAddress);
    router.put("/update-address/:refid", controller.updateAddress);
    router.post("/get-address", controller.getAddress);
    router.post("/address-details", controller.addressDetails);

    router.get("/get-cart", cartController.cartList);
    router.get("/export-cart", cartController.exportCart);
    router.get("/get-cart-details/:refid", cartController.cartDetails);
    router.get("/lens-enquiries", controller.lensEnquiries);
    router.get("/lens-enquiries/:id", controller.lensEnquiryDetails);

    router.post("/revenue-report", controller.revenueReport);
    router.post("/guest-report", controller.guestReport);

    router.get('/customer-export', controller.customerExport);
    router.post('/customer-import', upload.single('file'), controller.customerImport);

    return router;
};
