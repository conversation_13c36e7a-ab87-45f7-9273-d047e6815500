const mongoose = require('mongoose');

const attributeSchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: { type: String, required: true }
    },
    refid: { type: String, required: true },
    // type: { type: String, required: true, enum: ['Text', 'Color', 'File'] },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    isFiltered: { type: Boolean, default: false },
    // createdBy: { type: mongoose.Types.ObjectId, ref: 'admin.users' },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

attributeSchema.set('validateBeforeSave', false);

// attributeSchema.pre("save", function(next){
//     this.constructor.count({}).then(count => {
//         this.refid = count + 1;
//         this.validateSync();
//         next();
//     });
// })

attributeSchema.post('save', function (doc, next) {
    doc.constructor.count({}).then(count => {
        doc.refid = count + 1;
        doc.updateOne({ _id: doc._id }, { refid: doc.refid }).then(() => {
            console.log("Updated" + doc.refid)
            next();
        })
    });
})

module.exports = mongoose.model('attributes', attributeSchema)