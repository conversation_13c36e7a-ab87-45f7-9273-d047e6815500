require('dotenv').config()
const baseUrl = process.env.DOMAIN

exports.forgotPassword = async (reseturl) => {
    return `
    <html>
  <head>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: #f9f9f9;
      }
      .header {
        text-align: center;
        margin-bottom: 20px;
      }
      .content {
        padding: 10px;
      }
      .button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #007bff;
        color: #ffffff !important;
        text-decoration: none;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img
          src="cid:logo"
          alt="Logo" />
        <h2>Password Reset Instructions</h2>
      </div>
      <div class="content">
        <p>Dear Admin,</p>
        <p>
          We understand you're having trouble accessing your account. Don't worry, we're here to
          help! Follow the steps below to reset your password:
        </p>
        <ol>
          <li>Click the Reset Link below:</li>
        </ol>
        <p style="text-align: center"><a class="button" href="${reseturl}">Reset Password</a></p>
        <p style="text-align: center">OR</p>
        <div style="text-align: center; padding: 0 30px">
          <a href="${reseturl}">${reseturl}</a>
        </div>

        <p>
          Create a new, secure password for your account. We recommend using a combination of
          letters, numbers, and special characters.
        </p>
        <p>
          Double-check your new password for accuracy by entering it again in the confirmation
          field.
        </p>
        <p>
          Remember to save your new password in a safe place. Never share your password with anyone.
        </p>
        <p>
          If you did not request this password reset or have any concerns, please ignore this email.
          Your account's security is our priority, and no changes will be made without your action.
        </p>
        <p>
          If you need further assistance, feel free to contact our support team at
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>
      </div>
    </div>
  </body>
</html> `;
};

exports.abandonedCart = async (data) => {
    const productshtml = data.products
        .map((item) => {
            return ` <table
data-outer-table
border="0"
align="center"
cellpadding="0"
cellspacing="0"
class="outer-table row"
role="presentation"
width="640"
style="width: 640px; max-width: 640px"
data-module="blue-product-1"
>
<!-- blue-product-1 -->
<tr>
    <td
        height="5"
        style="font-size: 5px; line-height: 5px"
        data-height="Spacing top"
    >
        &nbsp;
    </td>
</tr>
<tr>
    <td
        align="center"
        bgcolor="#FFFFFF"
        data-bgcolor="BgColor"
        class="container-padding"
    >
        <!-- Content -->
        <table
            border="0"
            align="center"
            cellpadding="0"
            cellspacing="0"
            role="presentation"
            width="100%"
            style="width: 100%; max-width: 100%"
        >
            <tr>
                <td
                    align="center"
                    bgcolor="#F8F8F8"
                    data-bgcolor="Inner BgColor"
                >
                    <!-- rwd-col -->
                    <table
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        align="center"
                        class="row container-padding"
                        width="90.63%"
                        style="width: 90.63%; max-width: 90.63%"
                    >
                        <tr>
                            <td
                                height="30"
                                style="font-size: 30px; line-height: 30px"
                            >
                                &nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td
                                class="rwd-col"
                                align="center"
                                width="31.03%"
                                style="width: 31.03%; max-width: 31.03%"
                            >
                                <!-- column -->
                                <table
                                    border="0"
                                    align="center"
                                    cellpadding="0"
                                    cellspacing="0"
                                    role="presentation"
                                    width="100%"
                                    style="width: 100%; max-width: 100%"
                                >
                                    <tr
                                        data-element="blue-content-picture"
                                        data-label="Picture"
                                    >
                                        <td align="center">
                                            <img
                                                class="auto-width"
                                                style="display: block; width: 100%; max-width: 100%"
                                                data-image-edit
                                                data-url
                                                data-label="Picture"
                                                data-border-radius-default="0,6,36"
                                                data-border-radius-custom="Pictures"
                                                width="180"
                                                src=${baseUrl + item.product?.thumbnail}
                                                border="0"
                                                editable="true"
                                                alt="picture"
                                            />
                                        </td>
                                    </tr>
                                </table>
                                <!-- column -->
                            </td>
                            <td
                                class="rwd-col"
                                align="center"
                                width="3.45%"
                                height="30"
                                style="width: 3.45%; max-width: 3.45%; height: 30px"
                            >
                                &nbsp;
                            </td>
                            <td
                                class="rwd-col"
                                align="center"
                                width="65.52%"
                                style="width: 65.52%; max-width: 65.52%"
                            >
                                <!-- column -->
                                <table
                                    border="0"
                                    align="center"
                                    cellpadding="0"
                                    cellspacing="0"
                                    role="presentation"
                                    width="100%"
                                    style="width: 100%; max-width: 100%"
                                >
                                    <tr
                                        data-element="blue-content-titles"
                                        data-label="Titles"
                                    >
                                        <td
                                            class="center-text"
                                            data-text-style="Titles"
                                            align="left"
                                            style="
                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                font-size: 20px;
                                                line-height: 24px;
                                                font-weight: 700;
                                                font-style: normal;
                                                color: #333333;
                                                text-decoration: none;
                                                letter-spacing: 0px;
                                            "
                                        >
                                            <singleline>
                                                <div
                                                    mc:edit
                                                    data-text-edit
                                                >
                                                    ${item.product?.name?.en}
                                                </div>
                                            </singleline>
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-titles"
                                        data-label="Titles"
                                    >
                                        <td
                                            height="5"
                                            style="font-size: 5px; line-height: 5px"
                                            data-height="Spacing under titles"
                                        >
                                            &nbsp;
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-paragraph"
                                        data-label="Paragraphs"
                                    >
                                        <td
                                            class="center-text"
                                            data-text-style="Paragraphs"
                                            align="left"
                                            style="
                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                font-size: 16px;
                                                line-height: 24px;
                                                font-weight: 400;
                                                font-style: normal;
                                                color: #333333;
                                                text-decoration: none;
                                                letter-spacing: 0px;
                                            "
                                        >
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-paragraph"
                                        data-label="Paragraphs"
                                    >
                                        <td
                                            height="10"
                                            style="font-size: 10px; line-height: 10px"
                                            data-height="Spacing under paragraphs"
                                        >
                                            &nbsp;
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-option-1"
                                        data-label="1st Option"
                                    >
                                        <td
                                            class="center-text"
                                            data-text-style="Options"
                                            align="left"
                                            style="
                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                font-size: 16px;
                                                line-height: 24px;
                                                font-weight: 400;
                                                font-style: normal;
                                                color: #333333;
                                                text-decoration: none;
                                                letter-spacing: 0px;
                                            "
                                        >
                                            <singleline>
                                                <div
                                                    mc:edit
                                                    data-text-edit
                                                >
                                                    <strong>Color:</strong> ${item.product?.color?.name?.en}
                                                </div>
                                            </singleline>
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-option-2"
                                        data-label="2nd Option"
                                    >
                                        <td
                                            class="center-text"
                                            data-text-style="Options"
                                            align="left"
                                            style="
                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                font-size: 16px;
                                                line-height: 24px;
                                                font-weight: 400;
                                                font-style: normal;
                                                color: #333333;
                                                text-decoration: none;
                                                letter-spacing: 0px;
                                            "
                                        >
                                            <singleline>
                                                <div
                                                    mc:edit
                                                    data-text-edit
                                                >
                                                    <strong>Qty:</strong> ${item.quantity}
                                                </div>
                                            </singleline>
                                        </td>
                                    </tr>
                                    <tr
                                        data-element="blue-content-option-3"
                                        data-label="3rd Option"
                                    >
                                        <td
                                            class="center-text"
                                            data-text-style="Options"
                                            align="left"
                                            style="
                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                font-size: 16px;
                                                line-height: 24px;
                                                font-weight: 400;
                                                font-style: normal;
                                                color: #333333;
                                                text-decoration: none;
                                                letter-spacing: 0px;
                                            "
                                        >
                                            <singleline>
                                                <div
                                                    mc:edit
                                                    data-text-edit
                                                >
                                                    <strong>Price:</strong> AED ${item.priceTotal}
                                                </div>
                                            </singleline>
                                        </td>
                                    </tr>
                                </table>
                                <!-- column -->
                            </td>
                        </tr>
                        <tr>
                            <td
                                height="30"
                                style="font-size: 30px; line-height: 30px"
                            >
                                &nbsp;
                            </td>
                        </tr>
                    </table>
                    <!-- rwd-col -->
                </td>
            </tr>
        </table>
        <!-- Content -->
    </td>
</tr>
<tr>
    <td
        height="20"
        style="font-size: 20px; line-height: 20px"
        data-height="Spacing bottom"
    >
        &nbsp;
    </td>
</tr>
<!-- blue-product-1 -->
</table>`;
        })
        .join("");

    return `<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
    <html
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office"
    >
        <head>
            <meta
                http-equiv="Content-Type"
                content="text/html; charset=UTF-8"
            />
            <meta
                name="viewport"
                content="width=device-width, initial-scale=1"
            />
            <!-- So that mobile will display zoomed in -->
            <meta
                http-equiv="X-UA-Compatible"
                content="IE=edge"
            />
            <!-- enable media queries for windows phone 8 -->
            <meta
                name="format-detection"
                content="telephone=no"
            />
            <!-- disable auto telephone linking in iOS -->
            <meta
                name="format-detection"
                content="date=no"
            />
            <!-- disable auto date linking in iOS -->
            <meta
                name="format-detection"
                content="address=no"
            />
            <!-- disable auto address linking in iOS -->
            <meta
                name="format-detection"
                content="email=no"
            />
            <!-- disable auto email linking in iOS -->
            <meta
                name="color-scheme"
                content="only"
            />
            <title></title>
    
            <link
                rel="preconnect"
                href="https://fonts.googleapis.com"
            />
            <link
                rel="preconnect"
                href="https://fonts.gstatic.com"
                crossorigin
            />
            <link
                href="https://fonts.googleapis.com/css2?family=Barlow:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap"
                rel="stylesheet"
            />
    
            <style type="text/css">
                /*Basics*/
                body {
                    margin: 0px !important;
                    padding: 0px !important;
                    display: block !important;
                    min-width: 100% !important;
                    width: 100% !important;
                    -webkit-text-size-adjust: none;
                }
                table {
                    border-spacing: 0;
                    mso-table-lspace: 0pt;
                    mso-table-rspace: 0pt;
                }
                table td {
                    border-collapse: collapse;
                    mso-line-height-rule: exactly;
                }
                td img {
                    -ms-interpolation-mode: bicubic;
                    width: auto;
                    max-width: auto;
                    height: auto;
                    margin: auto;
                    display: block !important;
                    border: 0px;
                }
                td p {
                    margin: 0;
                    padding: 0;
                }
                td div {
                    margin: 0;
                    padding: 0;
                }
                td a {
                    text-decoration: none;
                    color: inherit;
                }
                /*Outlook*/
                .ExternalClass {
                    width: 100%;
                }
                .ExternalClass,
                .ExternalClass p,
                .ExternalClass span,
                .ExternalClass font,
                .ExternalClass td,
                .ExternalClass div {
                    line-height: inherit;
                }
                .ReadMsgBody {
                    width: 100%;
                    background-color: #ffffff;
                }
                /* iOS BLUE LINKS */
                a[x-apple-data-detectors] {
                    color: inherit !important;
                    text-decoration: none !important;
                    font-size: inherit !important;
                    font-family: inherit !important;
                    font-weight: inherit !important;
                    line-height: inherit !important;
                }
                /*Gmail blue links*/
                u + #body a {
                    color: inherit;
                    text-decoration: none;
                    font-size: inherit;
                    font-family: inherit;
                    font-weight: inherit;
                    line-height: inherit;
                }
                /*Buttons fix*/
                .undoreset a,
                .undoreset a:hover {
                    text-decoration: none !important;
                }
                .yshortcuts a {
                    border-bottom: none !important;
                }
                .ios-footer a {
                    color: #aaaaaa !important;
                    text-decoration: none;
                }
                /* data-outer-table="800 - 600" */
                .outer-table {
                    width: 640px !important;
                    max-width: 640px !important;
                }
                /* data-inner-table="780 - 540" */
                .inner-table {
                    width: 580px !important;
                    max-width: 580px !important;
                }
                /*Responsive-Tablet*/
                @media only screen and (max-width: 799px) and (min-width: 601px) {
                    .outer-table.row {
                        width: 640px !important;
                        max-width: 640px !important;
                    }
                    .inner-table.row {
                        width: 580px !important;
                        max-width: 580px !important;
                    }
                }
                /*Responsive-Mobile*/
                @media only screen and (max-width: 600px) and (min-width: 320px) {
                    table.row {
                        width: 100% !important;
                        max-width: 100% !important;
                    }
                    td.row {
                        width: 100% !important;
                        max-width: 100% !important;
                    }
                    .img-responsive img {
                        width: 100% !important;
                        max-width: 100% !important;
                        height: auto !important;
                        margin: auto;
                    }
                    .center-float {
                        float: none !important;
                        margin: auto !important;
                    }
                    .center-text {
                        text-align: center !important;
                    }
                    .container-padding {
                        width: 100% !important;
                        padding-left: 15px !important;
                        padding-right: 15px !important;
                    }
                    .container-padding10 {
                        width: 100% !important;
                        padding-left: 10px !important;
                        padding-right: 10px !important;
                    }
                    .hide-mobile {
                        display: none !important;
                    }
                    .menu-container {
                        text-align: center !important;
                    }
                    .autoheight {
                        height: auto !important;
                    }
                    .m-padding-10 {
                        margin: 10px 0 !important;
                    }
                    .m-padding-15 {
                        margin: 15px 0 !important;
                    }
                    .m-padding-20 {
                        margin: 20px 0 !important;
                    }
                    .m-padding-30 {
                        margin: 30px 0 !important;
                    }
                    .m-padding-40 {
                        margin: 40px 0 !important;
                    }
                    .m-padding-50 {
                        margin: 50px 0 !important;
                    }
                    .m-padding-60 {
                        margin: 60px 0 !important;
                    }
                    .m-padding-top10 {
                        margin: 10px 0 0 0 !important;
                    }
                    .m-padding-top15 {
                        margin: 15px 0 0 0 !important;
                    }
                    .m-padding-top20 {
                        margin: 20px 0 0 0 !important;
                    }
                    .m-padding-top30 {
                        margin: 30px 0 0 0 !important;
                    }
                    .m-padding-top40 {
                        margin: 40px 0 0 0 !important;
                    }
                    .m-padding-top50 {
                        margin: 50px 0 0 0 !important;
                    }
                    .m-padding-top60 {
                        margin: 60px 0 0 0 !important;
                    }
                    .m-height10 {
                        font-size: 10px !important;
                        line-height: 10px !important;
                        height: 10px !important;
                    }
                    .m-height15 {
                        font-size: 15px !important;
                        line-height: 15px !important;
                        height: 15px !important;
                    }
                    .m-height20 {
                        font-size: 20px !important;
                        line-height: 20px !important;
                        height: 20px !important;
                    }
                    .m-height25 {
                        font-size: 25px !important;
                        line-height: 25px !important;
                        height: 25px !important;
                    }
                    .m-height30 {
                        font-size: 30px !important;
                        line-height: 30px !important;
                        height: 30px !important;
                    }
                    .radius6 {
                        border-radius: 6px !important;
                    }
                    .fade-white {
                        background-color: rgba(255, 255, 255, 0.8) !important;
                    }
                    .rwd-on-mobile {
                        display: inline-block !important;
                        padding: 5px !important;
                    }
                    .center-on-mobile {
                        text-align: center !important;
                    }
                    .rwd-col {
                        width: 100% !important;
                        max-width: 100% !important;
                        display: inline-block !important;
                    }
                }
            </style>
            <style
                type="text/css"
                class="export-delete"
            >
                .composer--mobile table.row {
                    width: 100% !important;
                    max-width: 100% !important;
                }
                .composer--mobile td.row {
                    width: 100% !important;
                    max-width: 100% !important;
                }
                .composer--mobile .img-responsive img {
                    width: 100% !important;
                    max-width: 100% !important;
                    height: auto !important;
                    margin: auto;
                }
                .composer--mobile .center-float {
                    float: none !important;
                    margin: auto !important;
                }
                .composer--mobile .center-text {
                    text-align: center !important;
                }
                .composer--mobile .container-padding {
                    width: 100% !important;
                    padding-left: 15px !important;
                    padding-right: 15px !important;
                }
                .composer--mobile .container-padding10 {
                    width: 100% !important;
                    padding-left: 10px !important;
                    padding-right: 10px !important;
                }
                .composer--mobile .hide-mobile {
                    display: none !important;
                }
                .composer--mobile .menu-container {
                    text-align: center !important;
                }
                .composer--mobile .autoheight {
                    height: auto !important;
                }
                .composer--mobile .m-padding-10 {
                    margin: 10px 0 !important;
                }
                .composer--mobile .m-padding-15 {
                    margin: 15px 0 !important;
                }
                .composer--mobile .m-padding-20 {
                    margin: 20px 0 !important;
                }
                .composer--mobile .m-padding-30 {
                    margin: 30px 0 !important;
                }
                .composer--mobile .m-padding-40 {
                    margin: 40px 0 !important;
                }
                .composer--mobile .m-padding-50 {
                    margin: 50px 0 !important;
                }
                .composer--mobile .m-padding-60 {
                    margin: 60px 0 !important;
                }
                .composer--mobile .m-padding-top10 {
                    margin: 30px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top15 {
                    margin: 15px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top20 {
                    margin: 20px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top30 {
                    margin: 30px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top40 {
                    margin: 40px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top50 {
                    margin: 50px 0 0 0 !important;
                }
                .composer--mobile .m-padding-top60 {
                    margin: 60px 0 0 0 !important;
                }
                .composer--mobile .m-height10 {
                    font-size: 10px !important;
                    line-height: 10px !important;
                    height: 10px !important;
                }
                .composer--mobile .m-height15 {
                    font-size: 15px !important;
                    line-height: 15px !important;
                    height: 15px !important;
                }
                .composer--mobile .m-height20 {
                    font-srobotoize: 20px !important;
                    line-height: 20px !important;
                    height: 20px !important;
                }
                .composer--mobile .m-height25 {
                    font-size: 25px !important;
                    line-height: 25px !important;
                    height: 25px !important;
                }
                .composer--mobile .m-height30 {
                    font-size: 30px !important;
                    line-height: 30px !important;
                    height: 30px !important;
                }
                .composer--mobile .radius6 {
                    border-radius: 6px !important;
                }
                .composer--mobile .fade-white {
                    background-color: rgba(255, 255, 255, 0.8) !important;
                }
                .composer--mobile .rwd-on-mobile {
                    display: inline-block !important;
                    padding: 5px !important;
                }
                .composer--mobile .center-on-mobile {
                    text-align: center !important;
                }
                .composer--mobile .rwd-col {
                    width: 100% !important;
                    max-width: 100% !important;
                    display: inline-block !important;
                }
            </style>
        </head>
    
        <body
            data-bgcolor="Body"
            style="margin-top: 0; margin-bottom: 0; padding-top: 0; padding-bottom: 0; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%"
            bgcolor="#FFFFFF"
        >
            <span
                class="preheader-text"
                data-preheader-text
                style="color: transparent; height: 0; max-height: 0; max-width: 0; opacity: 0; overflow: hidden; visibility: hidden; width: 0; display: none; mso-hide: all"
            ></span>
    
            <!-- Preheader white space hack -->
            <div style="display: none; max-height: 0px; overflow: hidden">
                &zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;
            </div>
    
            <div
                data-primary-font="Barlow"
                data-secondary-font="Rubik"
                style="display: none; font-size: 0px; line-height: 0px; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden; visibility: hidden; mso-hide: all"
            ></div>
    
            <table
                border="0"
                align="center"
                cellpadding="0"
                cellspacing="0"
                width="100%"
                style="width: 100%; max-width: 100%"
            >
                <tr>
                    <!-- Outer Table -->
                    <td
                        align="center"
                        data-bgcolor="Body"
                        bgcolor="#FFFFFF"
                        data-composer
                    >
                        <table
                            data-outer-table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            class="outer-table row"
                            role="presentation"
                            width="640"
                            style="width: 640px; max-width: 640px"
                            data-module="blue-logo"
                        >
                            <!-- blue-logo -->
                            <tr>
                                <td
                                    align="center"
                                    bgcolor="#FFFFFF"
                                    data-bgcolor="BgColor"
                                    class="container-padding"
                                >
                                    <!-- Content -->
                                    <table
                                        border="0"
                                        align="center"
                                        cellpadding="0"
                                        cellspacing="0"
                                        role="presentation"
                                        width="100%"
                                        style="width: 100%; max-width: 100%"
                                    >
                                        <tr>
                                            <td
                                                height="20"
                                                style="font-size: 20px; line-height: 20px"
                                                data-height="Spacing top"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                        <tr
                                            data-element="blue-logo"
                                            data-label="Logo"
                                        >
                                            <td
                                                align="center"
                                                class="center-text"
                                            >
                                                <img
                                                    style="width: 120px; border: 0px; display: inline !important"
                                                    src="cid:logo"
                                                    width="120"
                                                    border="0"
                                                    editable="true"
                                                    data-icon
                                                    data-image-edit
                                                    data-url
                                                    data-label="Logo"
                                                    data-image-width
                                                    alt="logo"
                                                />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                height="20"
                                                style="font-size: 20px; line-height: 20px"
                                                data-height="Spacing bottom"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- Content -->
                                </td>
                            </tr>
                            <!-- blue-logo -->
                        </table>
    
                        <table
                            data-outer-table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            class="outer-table row"
                            width="640"
                            style="width: 640px; max-width: 640px"
                            data-module="blue-header"
                        >
                            <!-- blue-header -->
                            <tr>
                                <td
                                    align="center"
                                    class="img-responsive container-padding"
                                >
                                    <img
                                        class="auto-width"
                                        style="display: block; width: 50%; max-width: 100%; border: 0px"
                                        data-image-edit
                                        data-url
                                        data-label="Header image"
                                        width="640"
                                        src="cid:reminder"
                                        border="0"
                                        editable="true"
                                        alt="picture"
                                    />
                                </td>
                            </tr>
                            <!-- blue-header -->
                        </table>
    
                        <table
                            data-outer-table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            class="outer-table row"
                            role="presentation"
                            width="640"
                            style="width: 640px; max-width: 640px"
                            data-module="blue-preface-20"
                        >
                            <!-- blue-preface-20 -->
                            <tr>
                                <td
                                    align="center"
                                    bgcolor="#FFFFFF"
                                    data-bgcolor="BgColor"
                                    class="container-padding"
                                >
                                    <table
                                        data-inner-table
                                        border="0"
                                        align="center"
                                        cellpadding="0"
                                        cellspacing="0"
                                        role="presentation"
                                        class="inner-table row"
                                        width="580"
                                        style="width: 580px; max-width: 580px"
                                    >
                                        <tr>
                                            <td
                                                height="40"
                                                style="font-size: 40px; line-height: 40px"
                                                data-height="Spacing top"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                align="center"
                                                data-bgcolor="BgColor"
                                                bgcolor="#FFFFFF"
                                            >
                                                <!-- content -->
                                                <table
                                                    border="0"
                                                    align="center"
                                                    cellpadding="0"
                                                    cellspacing="0"
                                                    role="presentation"
                                                    width="100%"
                                                    style="width: 100%; max-width: 100%"
                                                >
                                                    <tr
                                                        data-element="blue-headline"
                                                        data-label="Headlines"
                                                    >
                                                        <td
                                                            class="center-text"
                                                            data-text-style="Headlines"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 48px;
                                                                line-height: 54px;
                                                                font-weight: 900;
                                                                font-style: normal;
                                                                color: #222222;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <singleline>
                                                                <div
                                                                    mc:edit
                                                                    data-text-edit
                                                                >
                                                                    Did you forgot something?
                                                                </div>
                                                            </singleline>
                                                        </td>
                                                    </tr>
                                                    <tr
                                                        data-element="blue-headline"
                                                        data-label="Headlines"
                                                    >
                                                        <td
                                                            height="15"
                                                            style="font-size: 15px; line-height: 15px"
                                                            data-height="Spacing under headline"
                                                        >
                                                            &nbsp;
                                                        </td>
                                                    </tr>
                                                    <tr
                                                        data-element="blue-paragraph"
                                                        data-label="Paragraphs"
                                                    >
                                                        <td
                                                            class="center-text"
                                                            data-text-style="Paragraphs"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 16px;
                                                                line-height: 26px;
                                                                font-weight: 400;
                                                                font-style: normal;
                                                                color: #333333;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <singleline>
                                                                <div
                                                                    mc:edit
                                                                    data-text-edit
                                                                >
                                                                    We noticed you left some goodies in your cart at Yateem Optcians, and we just wanted to check in! No worries if you
                                                                    weren't ready to checkout just yet, but in case you still have your eye on these items:
                                                                </div>
                                                            </singleline>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <!-- content -->
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                height="20"
                                                style="font-size: 20px; line-height: 20px"
                                                data-height="Spacing bottom"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <!-- blue-preface-20 -->
                        </table>
    
                        <table
                            data-outer-table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            class="outer-table row"
                            role="presentation"
                            width="640"
                            style="width: 640px; max-width: 640px"
                            data-module="blue-title"
                        >
                            <!-- blue-title -->
                            <tr>
                                <td
                                    height="20"
                                    style="font-size: 20px; line-height: 20px"
                                    data-height="Spacing top"
                                >
                                    &nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td
                                    align="center"
                                    bgcolor="#FFFFFF"
                                    data-bgcolor="BgColor"
                                    class="container-padding"
                                >
                                    <!-- Content -->
                                    <table
                                        border="0"
                                        align="center"
                                        cellpadding="0"
                                        cellspacing="0"
                                        role="presentation"
                                        width="100%"
                                        style="width: 100%; max-width: 100%"
                                    >
                                        <tr
                                            data-element="blue-title-titles"
                                            data-label="Titles"
                                        >
                                            <td
                                                class="center-text"
                                                data-text-style="Titles"
                                                align="center"
                                                style="
                                                    font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                    font-size: 24px;
                                                    line-height: 36px;
                                                    font-weight: 700;
                                                    font-style: normal;
                                                    color: #2e5266;
                                                    text-decoration: none;
                                                    letter-spacing: 0px;
                                                "
                                            >
                                                <singleline>
                                                    <div
                                                        mc:edit
                                                        data-text-edit
                                                    >
                                                        Products on cart
                                                    </div>
                                                </singleline>
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- Content -->
                                </td>
                            </tr>
                            <tr>
                                <td
                                    height="5"
                                    style="font-size: 5px; line-height: 5px"
                                    data-height="Spacing bottom"
                                >
                                    &nbsp;
                                </td>
                            </tr>
                        </table>
                        <!-- blue-title -->
    
                       ${productshtml}
    
                        <table
                            data-outer-table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            class="outer-table row"
                            role="presentation"
                            width="640"
                            style="width: 640px; max-width: 640px"
                            data-module="blue-cta"
                        >
                            <!-- blue-cta -->
                            <tr>
                                <td
                                    height="20"
                                    style="font-size: 20px; line-height: 20px"
                                    data-height="Spacing bottom"
                                >
                                    &nbsp;
                                </td>
                            </tr>
                            <tr
                                data-element="blue-button"
                                data-label="Buttons"
                            >
                                <td align="center">
                                    <!-- Button -->
                                    <table
                                        border="0"
                                        cellspacing="0"
                                        cellpadding="0"
                                        role="presentation"
                                        align="center"
                                        class="center-float"
                                    >
                                        <tr>
                                            <td
                                                align="center"
                                                data-border-radius-default="0,6,36"
                                                data-border-radius-custom="Buttons"
                                                data-bgcolor="Buttons"
                                                bgcolor="#000000"
                                                style="border-radius: 0px"
                                            >
                                                <!--[if (gte mso 9)|(IE)]>
                                    <table border="0" cellpadding="0" cellspacing="0" align="center">
                                      <tr>
                                        <td align="center" width="35"></td>
                                        <td align="center" height="50" style="height:50px;">
                                        <![endif]-->
                                                <singleline>
                                                    <a
                                                        href="${process.env.FRONTEND_URL}/cart"
                                                        mc:edit
                                                        data-button
                                                        data-text-style="Buttons"
                                                        style="
                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                            font-size: 16px;
                                                            line-height: 20px;
                                                            font-weight: 700;
                                                            font-style: normal;
                                                            color: #ffffff;
                                                            text-decoration: none;
                                                            letter-spacing: 0px;
                                                            padding: 15px 35px 15px 35px;
                                                            display: inline-block;
                                                        "
                                                        ><span>RESUME YOUR ORDER</span></a
                                                    >
                                                </singleline>
                                                <!--[if (gte mso 9)|(IE)]>
                                        </td>
                                        <td align="center" width="35"></td>
                                      </tr>
                                    </table>
                                  <![endif]-->
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- Buttons -->
                                </td>
                            </tr>
                            <tr>
                                <td
                                    height="40"
                                    style="font-size: 40px; line-height: 40px"
                                    data-height="Spacing bottom"
                                >
                                    &nbsp;
                                </td>
                            </tr>
                            <!-- blue-cta -->
                        </table>
    
                        <table
                            border="0"
                            align="center"
                            cellpadding="0"
                            cellspacing="0"
                            role="presentation"
                            width="100%"
                            style="width: 100%; max-width: 100%"
                            data-module="blue-footer"
                        >
                            <!-- blue-footer -->
                            <tr>
                                <td
                                    align="center"
                                    bgcolor="#F8F8F8"
                                    data-bgcolor="BgColor"
                                    data-border-color="Footer Border Color"
                                    class="container-padding"
                                    style="border-top: 10px solid #f1f1f1"
                                >
                                    <!-- Content -->
                                    <table
                                        border="0"
                                        align="center"
                                        cellpadding="0"
                                        cellspacing="0"
                                        role="presentation"
                                        class="row"
                                        width="520"
                                        style="width: 520px; max-width: 520px"
                                    >
                                        <tr>
                                            <td
                                                height="60"
                                                style="font-size: 60px; line-height: 60px"
                                                data-height="Footer spacing top"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
    
                                        <tr
                                            data-element="blue-footer-links"
                                            data-label="Footer Links"
                                        >
                                            <td align="center">
                                                <table
                                                    border="0"
                                                    align="center"
                                                    cellpadding="0"
                                                    cellspacing="0"
                                                    role="presentation"
                                                >
                                                    <tr class="center-on-mobile">
                                                        <td
                                                            data-element="blue-footer-1st-link"
                                                            data-label="1st Link"
                                                            data-text-style="Footer Links"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <!-- Links -->
                                                            <singleline>
                                                                <a
                                                                    href="${process.env.FRONTEND_URL}"
                                                                    mc:edit
                                                                    data-button
                                                                    data-text-style="Footer Links"
                                                                    style="
                                                                        font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                        font-size: 14px;
                                                                        line-height: 24px;
                                                                        font-weight: 400;
                                                                        font-style: normal;
                                                                        color: #666666;
                                                                        text-decoration: none;
                                                                        letter-spacing: 0px;
                                                                        display: inline-block;
                                                                        vertical-align: middle;
                                                                    "
                                                                    ><span>HOME</span></a
                                                                >
                                                            </singleline>
                                                            <!-- Links -->
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-1"
                                                            data-label="1st Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 400;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-2nd-link"
                                                            data-label="2nd Link"
                                                            data-text-style="Footer Links"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <!-- Links -->
                                                            <singleline>
                                                                <a
                                                                    href="${process.env.FRONTEND_URL}/products"
                                                                    mc:edit
                                                                    data-button
                                                                    data-text-style="Footer Links"
                                                                    style="
                                                                        font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                        font-size: 14px;
                                                                        line-height: 24px;
                                                                        font-weight: 400;
                                                                        font-style: normal;
                                                                        color: #666666;
                                                                        text-decoration: none;
                                                                        letter-spacing: 0px;
                                                                        display: inline-block;
                                                                        vertical-align: middle;
                                                                    "
                                                                    ><span>STORE</span></a
                                                                >
                                                            </singleline>
                                                            <!-- Links -->
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-2"
                                                            data-label="2nd Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 400;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-3rd-link"
                                                            data-label="3rd Link"
                                                            data-text-style="Footer Links"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <!-- Links -->
                                                            <singleline>
                                                                <a
                                                                    href="${process.env.FRONTEND_URL}/contact-us"
                                                                    mc:edit
                                                                    data-button
                                                                    data-text-style="Footer Links"
                                                                    style="
                                                                        font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                        font-size: 14px;
                                                                        line-height: 24px;
                                                                        font-weight: 400;
                                                                        font-style: normal;
                                                                        color: #666666;
                                                                        text-decoration: none;
                                                                        letter-spacing: 0px;
                                                                        display: inline-block;
                                                                        vertical-align: middle;
                                                                    "
                                                                    ><span>CONTACT US</span></a
                                                                >
                                                            </singleline>
                                                            <!-- Links -->
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-3"
                                                            data-label="3rd Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 400;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-4rd-link"
                                                            data-label="4rd Link"
                                                            data-text-style="Footer Links"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <!-- Links -->
                                                            <singleline>
                                                                <a
                                                                    href="${process.env.FRONTEND_URL}/policy/privacy-policy"
                                                                    mc:edit
                                                                    data-button
                                                                    data-text-style="Footer Links"
                                                                    style="
                                                                        font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                        font-size: 14px;
                                                                        line-height: 24px;
                                                                        font-weight: 400;
                                                                        font-style: normal;
                                                                        color: #666666;
                                                                        text-decoration: none;
                                                                        letter-spacing: 0px;
                                                                        display: inline-block;
                                                                        vertical-align: middle;
                                                                    "
                                                                    ><span>PRIVACY POLICY</span></a
                                                                >
                                                            </singleline>
                                                            <!-- Links -->
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-3"
                                                            data-label="4th Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 400;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-5th-link"
                                                            data-label="5th Link"
                                                            data-text-style="Footer Links"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <!-- Links -->
                                                            <singleline>
                                                                <a
                                                                    href="${process.env.FRONTEND_URL}/policy/terms-and-conditions"
                                                                    mc:edit
                                                                    data-button
                                                                    data-text-style="Footer Links"
                                                                    style="
                                                                        font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                        font-size: 14px;
                                                                        line-height: 24px;
                                                                        font-weight: 400;
                                                                        font-style: normal;
                                                                        color: #666666;
                                                                        text-decoration: none;
                                                                        letter-spacing: 0px;
                                                                        display: inline-block;
                                                                        vertical-align: middle;
                                                                    "
                                                                    ><span>TERMS</span></a
                                                                >
                                                            </singleline>
                                                            <!-- Links -->
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr
                                            data-element="blue-footer-links"
                                            data-label="Footer Links"
                                        >
                                            <td
                                                height="30"
                                                style="font-size: 30px; line-height: 30px"
                                                data-height="Spacing under footer links"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                        <tr
                                        data-element="blue-logo"
                                        data-label="Logo"
                                    >
                                        <td
                                            align="center"
                                            class="center-text"
                                        >
                                            <img
                                                style="width: 120px; border: 0px; display: inline !important"
                                                src="cid:logo"
                                                width="120"
                                                border="0"
                                                editable="true"
                                                data-icon
                                                data-image-edit
                                                data-url
                                                data-label="Logo"
                                                data-image-width
                                                alt="logo"
                                            />
                                        </td>
                                    </tr>
                                        <tr
                                            data-element="blue-footer-social-icons"
                                            data-label="Social Icons"
                                        >
                                            <td
                                                height="30"
                                                style="font-size: 30px; line-height: 30px"
                                                data-height="Spacing under social icons"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                        <tr
                                            data-element="blue-footer-paragraphs"
                                            data-label="Paragraphs"
                                        >
                                            <td align="center">
                                                <table
                                                    border="0"
                                                    align="center"
                                                    cellpadding="0"
                                                    cellspacing="0"
                                                    role="presentation"
                                                    class="row"
                                                    width="480"
                                                    style="width: 480px; max-width: 480px"
                                                >
                                                    <tr>
                                                        <td
                                                            class="center-text"
                                                            data-text-style="Paragraphs"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <multiline>
                                                                <div
                                                                    mc:edit
                                                                    data-text-edit
                                                                >
                                                                   Yateem Optician<br />
                                                                   <!--Address name St. 24, City Name, State, Country Name -->
                                                                </div>
                                                            </multiline>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr
                                            data-element="blue-footer-paragraphs"
                                            data-label="Paragraphs"
                                        >
                                            <td
                                                height="40"
                                                style="font-size: 40px; line-height: 40px"
                                                data-height="Spacing above tags"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                        <!--     <tr
                                            data-element="blue-footer-tags"
                                            data-label="Tags"
                                        >
                                            <td align="center">
                                                <table
                                                    border="0"
                                                    align="center"
                                                    cellpadding="0"
                                                    cellspacing="0"
                                                    role="presentation"
                                                >
                                                    <tr class="center-on-mobile">
                                                        <td
                                                            data-element="blue-footer-unsubscribe"
                                                            data-label="Unsubscribe"
                                                            data-text-style="Paragraphs"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <unsubscribe
                                                                href="#"
                                                                data-mergetag="Unsubscribe"
                                                                style="
                                                                    font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                    font-size: 14px;
                                                                    font-weight: 300;
                                                                    line-height: 24px;
                                                                    color: #666666;
                                                                    text-decoration: none;
                                                                "
                                                                >Unsubscribe</unsubscribe
                                                            >
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-1"
                                                            data-label="1st Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 300;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-webversion"
                                                            data-label="Web version"
                                                            data-text-style="Paragraphs"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <webversion
                                                                href="#"
                                                                data-mergetag="Web version"
                                                                style="
                                                                    font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                    font-size: 14px;
                                                                    font-weight: 300;
                                                                    line-height: 24px;
                                                                    color: #666666;
                                                                    text-decoration: none;
                                                                "
                                                                >View on browser</webversion
                                                            >
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-gap-2"
                                                            data-label="2nd Gap"
                                                            class="hide-mobile"
                                                            align="center"
                                                            valign="middle"
                                                        >
                                                            <table
                                                                border="0"
                                                                align="center"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                role="presentation"
                                                            >
                                                                <tr>
                                                                    <td width="5"></td>
                                                                    <td
                                                                        class="center-text"
                                                                        data-text-style="Paragraphs"
                                                                        align="center"
                                                                        style="
                                                                            font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                            font-size: 14px;
                                                                            line-height: 24px;
                                                                            font-weight: 300;
                                                                            font-style: normal;
                                                                            color: #666666;
                                                                            text-decoration: none;
                                                                            letter-spacing: 0px;
                                                                        "
                                                                    >
                                                                        |
                                                                    </td>
                                                                    <td width="5"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td
                                                            data-element="blue-footer-forward"
                                                            data-label="Forward"
                                                            data-text-style="Paragraphs"
                                                            class="rwd-on-mobile center-text"
                                                            align="center"
                                                            style="
                                                                font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                font-size: 14px;
                                                                line-height: 24px;
                                                                font-weight: 300;
                                                                font-style: normal;
                                                                color: #666666;
                                                                text-decoration: none;
                                                                letter-spacing: 0px;
                                                            "
                                                        >
                                                            <forward
                                                                href="#"
                                                                data-mergetag="Forward"
                                                                style="
                                                                    font-family: 'Barlow', Arial, Helvetica, sans-serif;
                                                                    font-size: 14px;
                                                                    font-weight: 300;
                                                                    line-height: 24px;
                                                                    color: #666666;
                                                                    text-decoration: none;
                                                                "
                                                                >Forward</forward
                                                            >
                                                        </td>
                                                    </tr> -->
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                height="40"
                                                style="font-size: 40px; line-height: 40px"
                                                data-height="Footer spacing bottom"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- Content -->
                                </td>
                            </tr>
                            <!-- blue-footer -->
                        </table>
                    </td>
                </tr>
                <!-- Outer-Table -->
            </table>
        </body>
    </html> `;
};

exports.orderPlacedAdmin = async (data) => {
    return `
      <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@100;200;300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    </head>

    <body style="font-family: 'Manrope', sans-serif;">
    Dear Admin,<br>
    You have received a new order on your website. Below are the details:
    <div style="margin: auto; max-width: 600px;width: 100%;">

        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0; border-bottom: 0;">
            <tbody>

                <tr>
                    <td colspan="4" style="background: #e1e9fe;padding: 20px 0;">
                        <div style="margin: auto; width: 200px;">
                            <img src="cid:logo" alt="Logo" style="width: 100%;">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" style="padding: 10px 30px;">
                      <table style="width: 100%;border-spacing: 5px 0px; border-bottom: 0;">
                        <tbody>
                            <tr>
                                <td rowspan=${data?.order?.paymentMethod == "ONLINE" ? "3" : "2"}>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Shipping Address
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                        ${data?.address?.name}, ${data?.address?.countryCode + " " + data?.address?.mobile}<br>
                                        ${data.address?.street}, ${data.address?.suiteUnit}, ${data.address?.postalCode}<br>
                                        ${data.address?.city}, ${data.address?.emirates}, ${data.address?.country}
                                    </p>
                                </td>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Method
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                            ${data?.order?.paymentMethod}
                                        </p>
                                </td>
                            </tr>
                            ${data?.order?.paymentMethod == "ONLINE" ? `
                                <tr>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Gateway
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                            ${data?.order?.gateway}
                                        </p>
                                </td>
                                </tr>
                                ` : ""}
                            <tr>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Status
                                    </h5>
                                    <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                        ${data?.order?.paymentStatus}
                                    </p>
                                </td>
                            </tr>
                                                                        <tr >
                        <td>
                        <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                            Ordered No
                        </h5>
                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                            ${data?.order?.orderNo}
                        </p>
                        </td>
                    <td>
                        <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                            Ordered Date
                        </h5>
                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                            ${new Date(data?.order?.orderDate).toDateString()}
                        </p>
                    </td>
                </tr>
                        </tbody>
                      </table>
                    </td>
                </tr>

            </tbody>

        </table>
        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0; border-bottom: 0;">

            <thead>
                <tr>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px 15px 30px; text-align: left;">
                        Products
                    </th>
                      <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px; text-align: center;">
                        Details
                    </th>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px; text-align: center;">
                        Qty
                    </th>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;">
                        Total
                    </th>
                </tr>
            </thead>

            <tbody> 
                ${data.products.map(
        (item) =>
            `<tr>
                        <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 0px 15px 30px; display: flex; align-items: flex-start;justify-content: flex-start;column-gap: 10px;">
                           <!-- <div style="width: 50px; flex: none;">
                                <img src=${baseUrl}${item?.thumbnail} alt="" style="width: 100%;" />
                            </div> -->
                            <p style="flex: 1;">${item?.name}</p>
                        </td>
                                                <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 0px; text-align: center;">
                            ${item?.contactLens?.sphLeft ? `
                            <p>SPH Left : ${item?.contactLens?.sphLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.sphRight ? `
                            <p>SPH Right : ${item?.contactLens?.sphRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.cylLeft ? `
                            <p>CYL Left : ${item?.contactLens?.cylLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.cylRight ? `
                            <p>CYL Right : ${item?.contactLens?.cylRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.axisLeft ? `
                            <p>Axis Left : ${item?.contactLens?.axisLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.axisRight ? `
                            <p>Axis Right : ${item?.contactLens?.axisRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.pd ? `
                            <p>Axis Right : ${item?.contactLens?.pd}</p>
                            `: ""}
                        </td>
                        <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 0px; text-align: center;">
                            <p>${item?.quantity}</p>
                        </td>
                        <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;">
                            <p>AED ${item?.total} </p>
                        </td>
                    </tr>`
    )}
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Subtotal
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.baseTotal - (data?.order?.vatAmount ?? 0)}
                    </td>
                </tr>
                ${data?.order?.vat ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        VAT (${data?.order?.vat}%)
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.vatAmount}
                    </td>
                </tr>` : ""}
                ${(data?.order?.isGiftWrapping && data?.order?.giftWrappingFee > 0) ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Gift Wrapping Fee
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.giftWrappingFee}
                    </td>
                </tr>` : ""}

                ${data?.order?.savings ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Savings
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        -${data?.order?.savings}
                    </td>
                </tr>` : ""
        }

                ${data?.order?.tryCartDeduction ?
            `<tr>
                    <td colspan="2"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        TryCart Deduction
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        -${data?.order?.tryCartDeduction || 0}
                    </td>
                </tr>` : ""
        }

                ${data?.order?.shippingCharge > 0 ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Shipping
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        +${data?.order?.shippingCharge || 0}
                    </td>
                </tr>`
            : ""
        }

                <tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Total
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.total}
                    </td>
                </tr>
            </tfoot>
        </table>

        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0;">
            <tbody>
                <tr>
                    <td colspan="4" style="background: #e1e9fe;padding: 20px 0;text-align: center;">
                        <a href="#" style="font-size: 14px;color: #1f2738;text-decoration: none;
                        line-height: 150%;font-weight: 400 ;">Yateem Optician</a>
                    </td>
                </tr>

            </tbody>
        </table>

    </div>
</body>
</html>`;
}

exports.orderPlaced = async (data) => {
    console.log(data?.products)
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@100;200;300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    </head>

    <body style="font-family: 'Manrope', sans-serif;">

    <div style="margin: auto; max-width: 600px;width: 100%;">

        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0; border-bottom: 0;">
            <tbody>

                <tr>
                    <td colspan="4" style="background: #e1e9fe;padding: 20px 0;">
                        <div style="margin: auto; width: 200px;">
                            <img src="cid:logo" alt="Logo" style="width: 100%;">
                        </div>
                    </td>
                </tr>

                <tr>
                    <td colspan="4" style="padding: 30px; text-align: center;">

                        <h4 style="font-size: 25px;font-weight: 600;color: #1f2738;line-height: 150%;text-transform: uppercase;margin: 10px 0;">
                            Your order has been ${data?.status === "SHIPPED VIA ECO" || data?.status === "SHIPPED VIA INHOUSE" ? "SHIPPED" : data?.status}
                        </h4>

                        <p style="font-size: 1  6px;font-weight: 400;color: #000000;line-height: 150%;margin: 10px 0;">
                            Thank you for purchasing from Yateem Optician. Your Order is ${data?.status === "SHIPPED VIA ECO" || data?.status === "SHIPPED VIA INHOUSE" ? "SHIPPED" : data?.status
        }.
                            Please find your order details.
                        </p>

                        <a href="${process.env.FRONTEND_URL}/my-accounts/my-orders/${data.order?.orderNo
        }" style="background: #1f2738;
                            padding: 0.625rem 1.125rem;
                            border-radius: 6.875rem;
                            color: #ffffff;
                            font-weight: 500;
                            font-size: 16px;
                            border: 1px solid #1f2738;
                            outline: none;text-decoration: none;display: inline-block;
                        margin-top: 0.9375rem;">
                            Manage Order
                        </a>

                    </td>
                </tr>

                <tr>
                    <td colspan="4" style="padding: 10px 30px;">
                         <table style="width: 100%;border-spacing: 5px 0px; border-bottom: 0;">
                        <tbody>
                            <tr>
                                <td rowspan=${data?.order?.paymentMethod == "ONLINE" ? "3" : "2"}>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Shipping Address
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                        ${data?.address?.name}, ${data?.address?.countryCode + " " + data?.address?.mobile}<br>
                                        ${data.address?.street}, ${data.address?.suiteUnit}, ${data.address?.postalCode}<br>
                                        ${data.address?.city}, ${data.address?.emirates}, ${data.address?.country}<br>
                                        ${data?.order?.trackingId ? (`Tracking :<a href="https://app.ecofreight.ae/en/tracking/${data?.order?.trackingId}">${data?.order?.trackingId}</a><br>`) : ""}
                                        ${data?.order?.awb ? (`<a href="${data?.order?.awb}">AWB</a>`) : ""}
                                    </p>
                                </td>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Method
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                            ${data?.order?.paymentMethod}
                                        </p>
                                </td>
                            </tr>
                                ${data?.order?.paymentMethod == "ONLINE" ? `
                                <tr>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Gateway
                                    </h5>
                                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                            ${data?.order?.gateway}
                                        </p>
                                </td>
                                </tr>
                                ` : ""}
                            <tr>
                                <td>
                                    <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                                        Payment Status
                                    </h5>
                                    <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                                        ${data?.order?.paymentStatus}
                                    </p>
                                </td>
                            </tr>
                                            <tr >
                        <td>
                        <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                            Ordered No
                        </h5>
                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                            ${data?.order?.orderNo}
                        </p>
                        </td>
                    <td>
                        <h5 style="font-size: 18px;font-weight: 600;color: #1f2738;line-height: 150%;margin: 5px 0;">
                            Ordered Date
                        </h5>
                        <p style="font-size: 14px;color: #000000;line-height: 150%;margin: 0;">
                            ${new Date(data?.order?.orderDate).toDateString()}
                        </p>
                    </td>
                </tr>
                        </tbody>
                      </table>
                    </td>
                </tr>

            </tbody>

        </table>
        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0; border-bottom: 0;">

            <thead>
                <tr>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px 15px 30px; text-align: left;">
                        Products
                    </th>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px; text-align: center;">
                        Details
                    </th>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 0px; text-align: center;">
                        Qty
                    </th>
                    <th style="font-size: 16px;font-weight: 600;color: #1f2738;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;">
                        Total
                    </th>
                </tr>
            </thead>

            <tbody> 
                ${data.products.map(
            (item) =>
                `<tr>
                        <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 0px 15px 30px; display: flex; align-items: flex-start;justify-content: flex-start;column-gap: 10px;">
                           <!-- <div style="width: 50px; flex: none;">
                                <img src=${baseUrl}${item?.thumbnail} alt="" style="width: 100%;" />
                            </div> -->
                            <p style="flex: 1;">${item?.name}</p>
                        </td>
                        <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 0px; text-align: center;">
                            ${item?.contactLens?.sphLeft ? `
                            <p>SPH Left : ${item?.contactLens?.sphLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.sphRight ? `
                            <p>SPH Right : ${item?.contactLens?.sphRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.cylLeft ? `
                            <p>CYL Left : ${item?.contactLens?.cylLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.cylRight ? `
                            <p>CYL Right : ${item?.contactLens?.cylRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.axisLeft ? `
                            <p>Axis Left : ${item?.contactLens?.axisLeft?.name}</p>
                            `: ""}
                            ${item?.contactLens?.axisRight ? `
                            <p>Axis Right : ${item?.contactLens?.axisRight?.name}</p>
                            `: ""}
                            ${item?.contactLens?.pd ? `
                            <p>Axis Right : ${item?.contactLens?.pd}</p>
                            `: ""}
                        </td>
                        <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 0px; text-align: center;">
                            <p>${item?.quantity}</p>
                        </td>
                        <td
                            style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;">
                            <p>AED ${(item?.price * item?.quantity) - (item?.couponAmount ?? 0)} </p>
                        </td>
                    </tr>`
        )}
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Subtotal
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.baseTotal - (data?.order?.vatAmount ?? 0)}
                    </td>
                </tr>

                ${data?.order?.vat ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        VAT (${data?.order?.vat}%)
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        +${data?.order?.vatAmount}
                    </td>
                </tr>` : ""}
            ${(data?.order?.isGiftWrapping && data?.order?.giftWrappingFee > 0) ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Gift Wrapping Fee
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.giftWrappingFee}
                    </td>
                </tr>` : ""}
                ${data?.order?.paymentCharge && data?.order?.paymentMethod === "COD" ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        COD Charge
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.paymentCharge}
                    </td>
                </tr>` : ""}

                ${data?.order?.savings ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Savings
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        -${data?.order?.savings}
                    </td>
                </tr>` : ""
        }

                ${data?.order?.tryCartDeduction ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        TryCart Deduction
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        -${data?.order?.tryCartDeduction || 0}
                    </td>
                </tr>` : ""
        }

                ${data?.order?.shippingCharge > 0 ?
            `<tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Shipping
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding: 15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        +${data?.order?.shippingCharge || 0}
                    </td>
                </tr>`
            : ""
        }

                <tr>
                    <td colspan="3"
                        style="font-size: 14px;font-weight: 600;color: #1f2738;line-height: 150%;padding:  15px 0px; text-align: right;border-top: 1px solid #e1e9fe;">
                        Total
                    </td>
                    <td style="font-size: 14px;font-weight: 600;color: #000000;line-height: 150%;padding:  15px 30px 15px 0; text-align: right;border-top: 1px solid #e1e9fe;">
                        ${data?.order?.total}
                    </td>
                </tr>
            </tfoot>
        </table>

        <table style="width: 100%;border: 1px solid #e1e9fe;border-spacing: 0;">
            <tbody>
                <tr>
                    <td colspan="4" style="background: #e1e9fe;padding: 20px 0;text-align: center;">
                        <a href="#" style="font-size: 14px;color: #1f2738;text-decoration: none;
                        line-height: 150%;font-weight: 400 ;">Yateem Optician</a>
                    </td>
                </tr>

            </tbody>
        </table>

    </div>
</body>
</html>`;
};

exports.contactForm = async (data) => {
    return `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                        <h2 style="background-color: #4CAF50; color: white; padding: 10px; text-align: center; border-radius: 10px 10px 0 0;">Thank You for Contacting Us!</h2>
                        <p>Dear ${data?.fullName},</p>
                        <p>Thank you for reaching out to us! We have received your message and will get back to you shortly.</p>
                        <p><strong>Here are the details we received:</strong></p>
                        <ul>
                            <li><strong>Name:</strong> ${data.fullName}</li>
                            <li><strong>Email:</strong> ${data.email}</li>
                            <li><strong>Phone Number:</strong>${data.countryCode} ${data.mobile}</li>
                            <li><strong>Message:</strong> ${data.message}</li>
                        </ul>
                        <p>Best regards,</p>
                        <p>Yateem Optician</p>
                        <footer style="background-color: #f1f1f1; padding: 10px; text-align: center; border-radius: 0 0 10px 10px;">
                            <p style="font-size: 12px; color: #888;">If you did not submit this form, please ignore this email.</p>
                        </footer>
                    </div>
                </div>`;
};

exports.lensEnquiry = async (enquiry, type) => {
    let url = enquiry?.prescriptionFile?.replace(/\\\\/g, '/');
    url = url?.replace(/\\/g, '/');
    console.log(url)
    return `
        <html><body>
        <h2>New Lens Enquiry Received</h2>
        <h3>Thank you. Our expert team will contact you shortly.</h3>
        <h4>Product:</h4>
        <p>${enquiry?.product?.name?.en}</p>
        <h4>USER DETAILS:</h4>
        Name: ${enquiry.userDetails.name}<br />
        Phone: ${enquiry.userDetails.phone}<br />
        Address: ${enquiry.userDetails.message}<br />

        <h4>PRESCRIPTION DETAILS:</h4>
        <h5>Type: ${enquiry.vision === "single" ? "Single" : "Progressive"}</h5>
        <h5>Brand: ${enquiry.brand?.name?.en}</h5>
        ${type === "manual" ? (
            `${enquiry.prescription?.leftSph && (
                `Left Sph: ${enquiry.prescription.leftSph.name}<br />`
            )}
        ${enquiry.prescription?.rightSph && (
                `Right Sph: ${enquiry.prescription.rightSph.name}<br />`
            )}
        ${enquiry.prescription?.leftCyl && (
                `Left Cyl: ${enquiry.prescription.leftCyl.name}<br />`
            )}
        ${enquiry.prescription?.rightCyl && (
                `Right Cyl: ${enquiry.prescription.rightCyl.name}<br />`
            )}
        ${enquiry.prescription?.leftAxis && (
                `Left Axis: ${enquiry.prescription.leftAxis.name}<br />`
            )}
        ${enquiry.prescription?.rightAxis && (
                `Right Axis: ${enquiry.prescription.rightAxis.name}<br />`
            )}
        ${enquiry.prescription?.leftAdd ? (
                `Left Add: ${enquiry.prescription.leftAdd.name}<br />`
            ) : ""}
        ${enquiry.prescription?.rightAdd ? (
                `Right Add: ${enquiry.prescription.rightAdd.name}<br />`
            ) : ""}
        ${enquiry.prescription?.pd ? (
                `PD: ${enquiry.prescription.pd}<br />`
            ) : ''}
            `
        ) : type === "file" ? (
            `<a href="${process.env.DOMAIN}${url}">View Prescription</a><br />`
        ) : (
            `Frame Only<br />`
        )
        }
        </body>
        </html>
        `
}
