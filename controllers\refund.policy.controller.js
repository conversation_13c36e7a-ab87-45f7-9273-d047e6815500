const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require('../services/refund.policy.service')
const axios = require('axios');

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" });
        helper.deliverResponse(res, 200, response, messages.REFUND_POLICY.CREATED);
    } catch (error) {
        helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.getRefundPolicy = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : 'sa';
        const response = await service.find({ isDelete: false, storeId: storeid });
        let data;
        if (language) {
            data = response.map(item => {
                return {
                    ...item?._doc,
                    title: item.title[language],
                    content: item.content[language]
                };
            });
        } else {
            data = response;
        }
        helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    } catch (error) {
        helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const response = await service.update({ isDelete: false, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "policy" });
        helper.deliverResponse(res, 200, response, messages.REFUND_POLICY.UPDATED);
    } catch (error) {
        helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}