const express = require('express');
const router = express.Router();
const controller = require('../../controllers/order.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/place-order', controller.validate('placeOrder'), controller.placeOrder);
     router.post('/tabby-check', authorize.verifyToken, controller.tabbyCheck);
    router.get('/orders', authorize.verifyToken, controller.getOrders);
    router.get('/try-cart-order', authorize.verifyToken, controller.getTryCartOrder);
    router.get('/order-detail/:orderNo', authorize.verifyToken, controller.orderDetail);
    router.put('/cancel-order', authorize.verifyToken, controller.cancelOrder);
    router.get('/verify-subscription', controller.verifySubscription);
    router.post('/verify-payment', controller.verifyPayment);
    router.post('/empty-trycart', authorize.verifyToken, controller.emptyTryCart);

    return router;
}