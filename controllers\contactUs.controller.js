const helper = require('../util/responseHelper')
const messages = require('../config/constants/messages')
const service = require('../services/contactUs.service')
const bannerService = require('../services/contact.banner.service')
const axios = require('axios')
const template = require('../util/templates')
const emailHelper = require('../util/emailHelper')
const { uploadWebp } = require('../util/uploadWebp')

exports.create = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        body.refid = await service.count({}) + 1
        body.storeId = storeid
        const result = await service.create(req?.body)
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        const mailOptions = {
            from: process.env.EMAIL_USER,
            to: body?.email,
            subject: 'Thank You for Contacting Us!',
            html: await template.contactForm(body),
        }
        emailHelper.sendMail(mailOptions, (error, info) => {
            if (error) {
            } else {
            }
        });
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.CONTACT_US.CREATED.error_code,
            error_message: messages.CONTACT_US.CREATED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getContactUs = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.find({ storeId: storeid })
        console.log(result)
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.addBanner = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;

        if (body.image) delete body.image
        if (req.files) {
            // Handle regular image
            if (req.files['image'] && req.files['image'][0]) {
                const name = `contact-us/image/${Date.now()}-${req.files['image'][0].originalname}`;
                const {key} = await uploadWebp(req.files['image'][0].path, name);
                body.image = key;
            }
            
            // Handle OG image
            if (req.files['ogImage'] && req.files['ogImage'][0]) {
                const name = `contact-us/ogImage/${Date.now()}-${req.files['ogImage'][0].originalname}`;
                const {key} = await uploadWebp(req.files['ogImage'][0].path, name);
                body.seoDetails = body.seoDetails || {};
                body.seoDetails.ogImage = key;
            }
        }

        body.refid = await bannerService.count({}) + 1
        body.storeId = storeid;
        const result = await bannerService.create(body)
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        await axios.post(process.env.REVALIDATE, { tag: "contact-banner" })
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.CONTACT_US.BANNER.error_code,
            error_message: messages.CONTACT_US.BANNER.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getBanner = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let result = await bannerService.findOne({ isDelete: false, storeId: storeid }, { __v: 0 })
        result.image = process.env.DOMAIN + result.image;
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getBannerWeb = async (req, res) => {
    try {
        let { language } = req?.headers
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        language = language ? language: 'en'
        let result = await bannerService.findOne({ isDelete: false, storeId: storeid }, { __v: 0 })
        result.image = process.env.DOMAIN + result.image;
        const data = {
            ...result._doc,
            pageTitle: result.pageTitle[language],
            formTitle: result.formTitle[language],
            title: result.title[language],
            description: result.description[language],
            buttonText: result.buttonText[language],
            officeHoursTitle: result.officeHoursTitle[language],
            officeHours: result.officeHours[language],
            storeOneTitle: result.storeOneTitle[language],
            storeOneAddress: result.storeOneAddress[language],
            storeTwoTitle: result.storeTwoTitle[language],
            storeTwoAddress: result.storeTwoAddress[language],
        }
        helper.deliverResponse(res, 200, data, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.updateBanner = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        let { body } = req;
        console.log(req.files, "files here")

        if (body.image) delete body.image
        if (req.files) {
            // Handle regular image
            if (req.files['image'] && req.files['image'][0]) {
                const name = `contact-us/image/${Date.now()}-${req.files['image'][0].originalname}`;
                const {key} = await uploadWebp(req.files['image'][0].path, name);
                body.image = key;
            }
            
            // Handle OG image
            if (req.files['ogImage'] && req.files['ogImage'][0]) {
                const name = `contact-us/ogImage/${Date.now()}-${req.files['ogImage'][0].originalname}`;
                const {key} = await uploadWebp(req.files['ogImage'][0].path, name);
                body.seoDetails = body.seoDetails || {};
                body.seoDetails.ogImage = key;
            }
        }

        console.log(body, "body here");
        

        const result = await bannerService.update({ storeId: storeid }, body)
        if (result instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                error_code: messages.SERVER_ERROR.error_code,
                error_message: messages.SERVER_ERROR.error_message
            })
        }
        await axios.post(process.env.REVALIDATE, { tag: "contact-banner" })
        helper.deliverResponse(res, 200, {}, {
            error_code: messages.CONTACT_US.BANNER_UPDATED.error_code,
            error_message: messages.CONTACT_US.BANNER_UPDATED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}
