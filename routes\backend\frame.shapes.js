const express = require('express');
const router = express.Router();
const controller = require('../../controllers/frame.shapes.controller')
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-frame-shape', upload.single('image'), controller.create);
    router.get('/get-frame-shapes', controller.getFrameShapes);
    router.get('/frame-shape-details/:refid', controller.frameShapeDetails);
    router.post('/update-frame-shape', upload.single('image'), controller.update);
    router.get('/active-frameShapes', controller.activeFrameShapes);
    router.put('/delete-frame-shape/:refid', controller.delete);

    return router;
}
