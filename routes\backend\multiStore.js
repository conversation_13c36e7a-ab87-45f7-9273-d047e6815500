const express = require('express');
const router = express.Router();
const controller = require('../../controllers/multiStore.contorller')

module.exports = () => {
    router.post("/create-multi-store", controller.create);
    router.get("/get-active-multi-stores", controller.countries);
    router.get("/multi-stores", controller.countries);
    router.get("/multi-store-details/:refid", controller.findOne);
    router.put("/update-multi-store", controller.update);

    return router;
}