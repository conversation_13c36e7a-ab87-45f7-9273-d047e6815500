const mongoose = require('mongoose')

const subscriptionSchema = new mongoose.Schema({
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers", required: true },
    product: { type: mongoose.Schema.Types.ObjectId, ref: "products", required: true },
    plan : { type: mongoose.Schema.Types.ObjectId, ref: "subscribe.plans", required: true },
    order : { type: mongoose.Schema.Types.ObjectId, ref: "Order", required: true },
    date: { type: Date, default: Date.now },
    nextOrderDate : { type: Date , required: true },
    subscriptionIdentifier : { type: String, required: true },
    status : { type: String },
    unsubscribed : { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('subscriptions', subscriptionSchema)