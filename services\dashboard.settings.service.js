const db = require('../models/index');

exports.create = async (data) => {
    try {
        let response = new db.DashboardSettings(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.DashboardSettings.find(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.DashboardSettings.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.DashboardSettings.findOneAndUpdate(query, { $set: { items: data } }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.upsert = async (query, data) => {
    try {
        let response = await db.DashboardSettings.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: true,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.addWidget = async (query, data) => {
    try {
        let response = await db.DashboardSettings.findOneAndUpdate(query, { $push: { items: data } }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.DashboardSettings.count(query)
        return response
    } catch (error) {
        throw error;
    }
}