const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/newsletter.service");

exports.validate = (method) => {
    switch (method) {
        case 'newsletter': {
            return [
                body('email', 'Email is required').exists(),
            ]
        }
    }
}

exports.subscribe = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : "ae";
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return helper.deliverResponse(res, 422, errors, {
                "error_code": messages.VALIDATION_ERROR.error_code,
                "error_message": messages.VALIDATION_ERROR.error_message
            })
        }

        let { body } = req;
        const emailExist = await service.findOne({ email: body.email, isDelete: false, storeId: storeid });
        if (emailExist) {
            if(emailExist.isActive === false){
                const response = await service.update({ email: body.email, isDelete: false, storeId: storeid }, { isActive: true });
                return helper.deliverResponse(res, 200, response, {
                    "error_code": messages.NEWSLETTER.SUBSCRIBED.error_code,
                    "error_message": messages.NEWSLETTER.SUBSCRIBED.error_message
                });
            }
            return helper.deliverResponse(res, 422, {}, {
                "error_code": messages.NEWSLETTER.ALREADY_SUBSCRIBED.error_code,
                // "error_message": messages.NEWSLETTER.ALREADY_SUBSCRIBED.error_message
                "error_message": language === 'en' ? messages.NEWSLETTER.ALREADY_SUBSCRIBED.error_message : "اشتركت بالفعل"
            })
        }
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.NEWSLETTER.SUBSCRIBED.error_code,
            "error_message": messages.NEWSLETTER.SUBSCRIBED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
    }
}

exports.getNewsletter = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        const { fromDate, toDate } = body;

        const matchQuery = {
            isDelete: false,
            storeId: storeid,
        };

        if (fromDate) {
            matchQuery.createdAt = { $gte: new Date(fromDate) };
        }

        if (toDate) {
            matchQuery.createdAt = {
                ...matchQuery.createdAt,
                $lte: new Date(toDate)
            };
        }

        const response = await service.find(matchQuery);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.NEWSLETTER.DELETED.error_code,
            error_message: messages.NEWSLETTER.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};

exports.unsubscribe = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params;
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isActive: false });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.NEWSLETTER.UNSUBSCRIBED.error_code,
            error_message: messages.NEWSLETTER.UNSUBSCRIBED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
};
