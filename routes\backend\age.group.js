const express = require('express');
const router = express.Router();
const controller = require('../../controllers/age.group.controller');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/create-age-group', authorize.verifyToken, controller.create);
    router.post('/get-age-groups', controller.getAgeGroups);
    router.get('/age-group-details/:refid', controller.ageGroupDetails);
    router.post('/update-age-group', authorize.verifyToken, controller.update);
    router.put('/delete-age-group/:refid', authorize.verifyToken, controller.delete);

    return router;
}
