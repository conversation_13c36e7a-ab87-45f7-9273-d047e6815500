const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.SubscribeSummary(data);
        await response.save();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.SubscribeSummary.find(query, projection);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.SubscribeSummary.findOne(query, projection);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.SubscribeSummary.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false,
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.SubscribeSummary.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};
