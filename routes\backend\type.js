const express = require("express");
const router = express.Router();
const controller = require("../../controllers/type.controller");

module.exports = () => {
    router.post("/types/", controller.create);
    router.get("/types/", controller.find);
    router.get("/types/:refid", controller.findOne);
    router.put("/types/:refid", controller.update);
    router.put("/delete-types/:refid", controller.delete);
    return router;
};
