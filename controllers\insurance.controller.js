const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require("../services/insurance.enquiry.service");
const providerService = require("../services/insurance.provider.service");
const contentService = require("../services/insurance.content.service");
const axios = require('axios');
const emailHelper = require("../util/emailHelper");
const { uploadWebp } = require('../util/uploadWebp');
const { findSub } = require('../services/category.service');
const fs = require('fs');

exports.addEnquiry = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        let { body } = req;
        if (req?.file) body.file = req?.file?.path;
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        
        if (req?.file) {
            const name = `insurance/${Date.now()}-${req?.file?.originalname}`
            const { key } = await uploadWebp(req?.file?.path, name, req?.file?.mimetype)
            body.file = key;
        }

        const response = await service.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, Error, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        }
        const provider = await providerService.findOne({ _id: body.insurance, isDelete: false, storeId: storeid })
        const ccs = process.env.INSURANCE_CCS?.split(" ")
        const mailOptions = {
            from: process.env.EMAIL_USER,
            to: ccs,
            subject: "Insurance Enquiry",
            text: `Name: ${body?.name}\nEmail: ${body?.email}\nMobile: ${body?.countryCode} ${body?.mobile}\nNationality: ${body?.nationality ?? ""}\nEmiratesId: ${body?.emiratesId ?? ""}\nProvider: ${provider?.name?.[language] ?? ""}`,
        };

        emailHelper.sendMail(mailOptions, (error, info) => {
            if (error) {
            } else {
            }
        });

        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.INSURANCE.SUBMITTED[language].error_code,
            "error_message": messages.INSURANCE.SUBMITTED[language].error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, error, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getEnquiry = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.addProvider = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        if (req?.file) {
            const name = `insurance-providers/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.logo = key
        };
        body.refid = await providerService.count({}) + 1
        body.storeId = storeid;
        const response = await providerService.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "insurance-providers" });
            return helper.deliverResponse(res, 200, response, {
                "error_code": messages.INSURANCE.CREATED.error_code,
                "error_message": messages.INSURANCE.CREATED.error_message
            })
        }
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getProviders = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await providerService.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.providerDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await providerService.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.updateProvider = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;

        if (body?.logo) delete body.logo;

        if (req?.file) {
            const name = `insurance-providers/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.logo = key
        };
        const response = await providerService.update({ refid: req?.params?.refid, storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "insurance-providers" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.INSURANCE.UPDATED.error_code,
            "error_message": messages.INSURANCE.UPDATED.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.activeProviders = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const response = await providerService.find({ isDelete: false, isActive: true, storeId: storeid });
        let data = [];
        for (let item of response) {
            data.push({
                ...item?._doc,
                name: item?.name?.[language] ?? item?.name.en,
                logo: process.env.DOMAIN + item.logo
            })
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.addContent = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        if (body.ogImage) delete body.ogImage;

        if (req?.file) {
            const name = `insurance-content/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.seoDetails.ogImage = key
        };

        body.refid = await contentService.count({}) + 1
        body.storeId = storeid;
        const response = await contentService.create(body);
        if (response instanceof Error) {
            return helper.deliverResponse(res, 422, {}, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        } else {
            await axios.post(process.env.REVALIDATE, { tag: "insurance-content" });
            return helper.deliverResponse(res, 200, response, {
                "error_code": messages.INSURANCE.CONTENT_CREATED.error_code,
                "error_message": messages.INSURANCE.CONTENT_CREATED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.getContent = async (req, res) => {
    try {
        let { language, storeid } = req.headers
        // language = language ? language : 'en';
        storeid = storeid ? storeid : 'sa';
        const response = await contentService.find({ isDelete: false, storeId: storeid });
        let data
        if (language) {
            data = response.map(item => {
                return {
                    pageTitle: item?.pageTitle[language],
                    pageDescription: item?.pageDescription[language],
                    descriptionOne: item?.descriptionOne[language],
                    descriptionTwo: item?.descriptionTwo[language],
                    terms: {
                        title: item?.terms?.title?.[language],
                        content: item?.terms?.content?.[language]
                    },
                    popup: {
                        title: item?.popup?.title?.[language],
                        description: item?.popup?.description?.[language],
                        primaryBtn: item?.popup?.primaryBtn?.[language],
                        secondaryBtn: item?.popup?.secondaryBtn?.[language],
                        primaryBtnLink: item?.popup?.primaryBtnLink,
                        secondaryBtnLink: item?.popup?.secondaryBtnLink
                    },
                    seoDetails: item?.seoDetails
                }
            })
        } else {
            data = response
        }
        return helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.updateContent = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        if (body.ogImage) delete body.ogImage;

        if (req?.file) {
            const name = `insurance-content/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.seoDetails.ogImage = key
        };

        const response = await contentService.update({ storeId: storeid }, body);
        await axios.post(process.env.REVALIDATE, { tag: "insurance-content" });
        return helper.deliverResponse(res, 200, response, {
            "error_code": messages.INSURANCE.CONTENT_UPDATED.error_code,
            "error_message": messages.INSURANCE.CONTENT_UPDATED.error_message
        })
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}

exports.deleteProvider = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const response = await providerService.update({ refid: req?.params?.refid, storeId: storeid }, { isDelete: true });
        await axios.post(process.env.REVALIDATE, { tag: "insurance-providers" });
        if (response instanceof Error) {
            helper.deliverResponse(res, 422, {}, {
                "error_code": messages.SERVER_ERROR.error_code,
                "error_message": messages.SERVER_ERROR.error_message,
            })
        } else {
            helper.deliverResponse(res, 200, response, {
                "error_code": messages.INSURANCE.PROVIDER_DELETED.error_code,
                "error_message": messages.INSURANCE.PROVIDER_DELETED.error_message,
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message,
        })
    }
}