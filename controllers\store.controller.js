const messages = require('../config/constants/messages')
const helper = require('../util/responseHelper')
const db = require('../models')
const slug = require('../util/slug')
const adminService = require('../services/admin.users.service')
const service = require('../services/store.service')
const contentService = require('../services/store.cms.service')
const axios = require('axios');
const { uploadWebp } = require('../util/uploadWebp')

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const { email } = res?.locals?.user
        const adminDetails = await adminService.findOne({ email: email, isDelete: false })
        body.slug = await slug.createSlug(db.Stores, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en) })
        body.refid = await service.count({}) + 1
        body.createdBy = adminDetails?._id
        body.storeId = storeid;
        if (req?.file) {
            const name = `stores/${body.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.thumbnail = key;
        }
        const response = await service.create(body);
        const revalidate = await axios.post(process.env.REVALIDATE, { tag: "stores" })
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.STORE.CREATED.error_code,
            "error_message": messages.STORE.CREATED.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.getStores = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const { clickAndCollect } = req.query;
        let query = { isDelete: false, isActive: true, storeId: storeid };
        if (clickAndCollect) query.isClickAndCollect = true;
        const response = await service.find(query);
        let data;
        if (language) {
            const storeCms = await contentService.find({ isDelete: false, storeId: storeid });
            data = {
                title: storeCms[0]?.content[language] ? storeCms[0]?.content[language] : storeCms[0]?.content['en'],
                stores: response?.map(item => {
                    return {
                        ...item?._doc,
                        name: item.name[language],
                        address: item.address[language],
                        thumbnail: item.thumbnail ? process.env.DOMAIN + item.thumbnail : null
                    };
                })
            }
        } else {
            data = response;
        }
        helper.deliverResponse(res, 200, data, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }

}

exports.storeDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.getStoresFilters = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const stores = await service.find({ isDelete: false, storeId: storeid }, { services: 1, facilities: 1, store: 1 });
        const services = [], facilities = [], store = [];
        for (let item of stores) {
            for (let service of item?.services) {
                if (!services.includes(service)) services.push(service);
            }
            for (let facility of item?.facilities) {
                if (!facilities.includes(facility)) facilities.push(facility);
            }
            if (!store.includes(item?.store)) store.push(item?.store);
        }
        const response = { services, facilities, store }
        if(!response) return helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        });
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.SUCCESS_RESPONSE.error_code,
            "error_message": messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const storeDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.name?.en !== storeDetails?.name?.en) body.slug = await slug.createSlug(db.Stores, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en) });
        
        if (req?.file) {
            const name = `stores/${body.slug? body.slug : storeDetails?.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.thumbnail = key;
        }

        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        const revalidate = await axios.post(process.env.REVALIDATE, { tag: "stores" })
        helper.deliverResponse(res, 200, response, {
            "error_code": messages.STORE.UPDATED.error_code,
            "error_message": messages.STORE.UPDATED.error_message
        })
    } catch (error) {
        console.log(error)
        helper.deliverResponse(res, 422, {}, {
            "error_code": messages.SERVER_ERROR.error_code,
            "error_message": messages.SERVER_ERROR.error_message
        })
    }
}

exports.createStoreCms = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.refid = await contentService.count({}) + 1;
        body.storeId = storeid;
        const response = await contentService.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.STORE.CONTENT_CREATEDerror_code,
            error_message: messages.STORE.CONTENT_CREATED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.getStoreCms = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await contentService.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.updateStoreCms = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const response = await contentService.update({ storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.STORE.CONTENT_UPDATED.error_code,
            error_message: messages.STORE.CONTENT_UPDATED.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true })
        if (response) {
            helper.deliverResponse(res, 200, response, {
                error_code: messages.STORE.DELETED.error_code,
                error_message: messages.STORE.DELETED.error_message
            })
        } else {
            helper.deliverResponse(res, 422, {}, {
                error_code: messages.DELETE_FAILED.error_code,
                error_message: messages.DELETE_FAILED.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        })
    }
}