const mongoose = require('mongoose')

const generalSettingsSchema = new mongoose.Schema({
    text: [{
        en: { type: String },
        ar: { type: String }
    }],
    whatsappNumber: { type: String },
    logo: { type: String },
    socialMedia: [{
        icon: { type: String },
        link: { type: String },
        name: { type: String },
    }],
    tryCartBtnText: {
        en: { type: String },
        ar: { type: String }
    },
    tamara: { type: Boolean, default: false },
    tabby: { type: Boolean, default: false },
    clickAndCollect: { type: Boolean, default: false },
    isStoreLocatorIframe: { type: <PERSON>olean, default: true },
    isGiftWrapping: { type: Boolean, default: false },
    giftWrappingFee: { type: Number, default: 0 },
    contact: {
        address: {
            en: { type: String },
            ar: { type: String }
        },
        email: { type: String },
        mobileOne: { type: String },
        mobileTwo: { type: String },
    },
    copyRight: {
        en: { type: String },
        ar: { type: String }
    },
    analyticsId: { type: String },
    tagManagerId: { type: String },
    pixelId: { type: String },
    popularSearch: [{ type: String }],
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'admin.users' },
    storeId: { type: String, required: true, default: 'ae' },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String }
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String }
        },
        ogImage: { type: String }
    }
}, { timestamps: true })

module.exports = mongoose.model('general.settings', generalSettingsSchema)
