const mongoose = require("mongoose");

const lensEnquirySchema = new mongoose.Schema(
  {
    customer: { type: mongoose.Schema.Types.ObjectId, ref: "customers" },
    deviceToken: { type: String },
    product: { type: mongoose.Schema.Types.ObjectId, ref: "products" },
    quantity: { type: String },
    vision: { type: String },
    refid: { type: String },
    prescription: {
      leftSph: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      rightSph: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      leftCyl: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      rightCyl: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      leftAxis: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      rightAxis: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      leftAdd: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      rightAdd: { type: mongoose.Schema.Types.ObjectId, ref: "lens.power" },
      pd: { type: String },
    },
    prescriptionFile: { type: String },
    lensType: { type: String },
    brand: { type: mongoose.Schema.Types.ObjectId, ref: "lens.brand" },
    photochromic: { type: String },
    index: { type: mongoose.Schema.Types.ObjectId, ref: "lens.index" },
    coating: { type: mongoose.Schema.Types.ObjectId, ref: "coating" },
    userDetails: {
      name: { type: String },
      phone: { type: String },
      email: { type: String },
      message: { type: String },
    },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
  },
  { timestamps: true }
);

module.exports = mongoose.model("lensEnquiries", lensEnquirySchema);
