const express = require('express');
const router = express.Router();
const controller = require('../../controllers/dashboard.settings.controller')
const imageMapController = require('../../controllers/imageMap.controller')
const beforeAfterController = require('../../controllers/before.after.controller')

module.exports = () => {
    router.get('/home-order', controller.get);

    router.get('/before-after', beforeAfterController.getBeforeAfter);

    router.get('/imageMap', imageMapController.imageMap)

    return router;
}