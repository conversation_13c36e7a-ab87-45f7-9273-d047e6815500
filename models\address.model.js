const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
    name: { type: String, required: true },
    refid: { type: String, required: true },
    country: { type: String  },
    emirates: { type: String  },
    street: { type: String  },
    suiteUnit: { type: String },
    email: { type: String },
    city: { type: String,  },
    postalCode: { type: String },
    countryCode: { type: String, required: true },
    mobile: { type: String, required: true },
    type: { type: String, enum: ['Home', 'Work', 'Other'], required: true, default: "Home" },
    customer: { type: mongoose.Schema.Types.ObjectId, ref: 'customers' },
    isDefault: { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('address', addressSchema);