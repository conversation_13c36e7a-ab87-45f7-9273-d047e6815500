const mongoose = require('mongoose');

const privacyPolicySchema = new mongoose.Schema({
    title : {
        en: { type: String, required: true },
        ar: { type: String }
    },
    content: {
        en: { type: String, required: true },
        ar: { type: String },
    },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String },
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
    refid: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('privacy.policies', privacyPolicySchema);