const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.Subscription(data);
        await response.save();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.Subscription.find(query, projection)
            .populate("product", "name thumbnail price offerPrice offerPercentage")
            .populate("plan");
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Subscription.findOne(query, projection);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.Subscription.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.Subscription.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.adminFind = async (query, projection = {}) => {
    try {
        let response = await db.Subscription.find(query, projection).sort({ createdAt: -1 })
            .populate("product", "name ")
            .populate("customer", "name mobile ")
            .populate("plan");
        return response;
    } catch (error) {
        throw error;
    }
};
