const express = require('express');
const router = express.Router();
const controller = require('../../controllers/lens.index.controller');

module.exports = () => {
    router.post('/create-lens-index', controller.create);
    router.get('/get-lens-index', controller.list);
    router.get('/lens-index-details/:refid', controller.detail);
    router.put('/update-lens-index/:refid', controller.update);
    router.put('/delete-lens-index/:refid', controller.delete);

    return router;
}