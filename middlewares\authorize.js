const messages = require('../config/constants/messages');
const jwt = require('jsonwebtoken');
const helper = require('../util/responseHelper');
const { KEYS } = require('../config/constants/key');
const guestService = require('../services/guest.service');
const adminService = require('../services/admin.users.service');
const customerService = require('../services/customer.service');
const textService = require('../services/text.service');

// exports.verifyToken = (req, res, next) => {
//     let header = req.headers.authorization;
//     if (header == undefined) {
//         helper.deliverResponse(res, 401, {}, {
//             "error_code": messages.TOKEN_REQUIRED.error_code,
//             "error_message":"Please Login to continue"
//         });
//         return;
//     }
//     let token = header.split('Bearer ')[1];
//     jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
//         if (err) {
//             helper.deliverResponse(res, 401, {}, {
//                 "error_code": messages.INVALID_TOKEN.error_code,
//                 "error_message": messages.INVALID_TOKEN.error_message
//             });
//             return
//         } else {
//             res.locals.user = decoded;
//             global.requestedUser = decoded;
//             next();
//         }
//     })
// }

exports.verifyToken = async (req, res, next) => {
    let translate;
    let language;
    try {
        let header = req.headers.authorization;

        language = req.headers?.language;
        language = language ?? "en"

        translate = await textService.findOne({}) ?? {}

        if (header == undefined) {
            return helper.deliverResponse(res, 401, {}, {
                "error_code": messages.TOKEN_REQUIRED.error_code,
                "error_message": translate?.popup?.pleaseLogin[language] ?? "Please Login to continue"
            })
        }
        let token = header.split('Bearer ')[1];
        let decoded = await jwt.verify(token, KEYS.DEV.JWTSECRET);
        if (decoded) {
            const customer = await customerService.findOne({ refid: decoded?.customerId, isActive: false });
            if (customer) {
                return helper.deliverResponse(res, 401, {}, {
                    "error_code": 1,
                    "error_message": "Your account has been Blocked. Please contact support"
                })
            }
            res.locals.user = decoded;
            global.requestedUser = decoded;
            next();
        } else {
            return helper.deliverResponse(res, 401, {}, {
                "error_code": messages.INVALID_TOKEN.error_code,
                "error_message": messages.INVALID_TOKEN.error_message
            })
        }
    } catch (error) {
        helper.deliverResponse(res, 401, {}, {
            "error_code": messages.TOKEN_REQUIRED.error_code,
            "error_message": translate?.popup?.pleaseLogin[language] ?? "Please Login to continue"
        })
    }
}

exports.checkPermission = async (req, res, next) => {
    try {
        let header = req.headers.authorization;
        let {permission} = req.headers;
        if (header == undefined) {
            return helper.deliverResponse(res, 401, {}, {
                "error_code": messages.TOKEN_REQUIRED.error_code,
                "error_message": messages.TOKEN_REQUIRED.error_message
            });
        }

        let token = header.split('Bearer ')[1];
        let decoded = await jwt.verify(token, KEYS.DEV.JWTSECRET);
        if (decoded?.refid) {
            const admin = await adminService.findOne({ refid: decoded?.refid, isDelete: false, isActive: true });
            if (admin) {
                if (admin.role?.permissions?.includes(permission)) {
                    next();
                } else {
                    return helper.deliverResponse(res, 403, {}, {
                        "error_code": messages.ROLES.PERMISSION_DENIED.error_code,
                        "error_message": messages.ROLES.PERMISSION_DENIED.error_message
                    });
                }
                res.locals.user = decoded;
                global.requestedUser = decoded;
            } else {
                return helper.deliverResponse(res, 200, {}, {
                    "error_code": messages.ADMIN.NOT_FOUND.error_code,
                    "error_message": messages.ADMIN.NOT_FOUND.error_message
                });
            }     
        }
    } catch (error) {
        return helper.deliverResponse(res, 401, {}, {
            "error_code": messages.INVALID_TOKEN.error_code,
            "error_message": messages.INVALID_TOKEN.error_message
        });
    }
}

exports.verifyCartAuth = async (req, res, next) => {
    let { authorization, devicetoken } = req.headers;
    if (authorization) {
        let token = authorization.split('Bearer ')[1];
        if (token != 'null' || token != null) {
            jwt.verify(token, KEYS.DEV.JWTSECRET, (err, decoded) => {
                if (err) {
                    helper.deliverResponse(res, 401, {}, {
                        error_code: messages.INVALID_TOKEN.error_code,
                        error_message: messages.INVALID_TOKEN.error_message
                    });
                    return
                } else {
                    if (decoded?.userid) {
                        res.locals.user = decoded;
                        global.requestedUser = decoded;
                        next();
                    } else {
                        helper.deliverResponse(res, 401, {}, {
                            "error_code": messages.NO_USER_FOUND.error_code,
                            "error_message": messages.NO_USER_FOUND.error_message
                        });
                    }
                }
            })
        } else {
            const guestDetails = await guestService.findOne({ deviceToken: req.headers.devicetoken, isDelete: false })
            let guestPayload = {
                name: `Guest #${await guestService.count() + 1}`,
                refid: await guestService.count() + 1,
                deviceToken: req.headers.devicetoken,
            }
            let guest = null
            !guestDetails ? guest = await guestService.create(guestPayload) : null
            res.locals.user = { deviceToken: devicetoken, guestDetails: guest?.refid };
            global.requestedUser = { deviceToken: devicetoken };
            next();
        }
    } else {
        res.locals.user = { deviceToken: devicetoken };
        global.requestedUser = { deviceToken: devicetoken };
        next();
    }
}
