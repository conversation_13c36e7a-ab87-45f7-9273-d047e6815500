const messages = require("../config/constants/messages");
const helper = require("../util/responseHelper");
const service = require("../services/meta.tag.service");

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { body } = req;
        body.storeId = storeid;
        const result = await service.create(body);
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.META_TAG.CREATED.error_code,
            error_message: messages.META_TAG.CREATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.list = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const result = await service.findOne({ isDelete: false, storeId: storeid });
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.update = async (req, res) => {
    try {
        let { storeid } = req?.headers;
        storeid = storeid ? storeid : "ae";
        const { body } = req;
        const result = await service.update({ isDelete: false, storeId: storeid }, body);
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, messages.SERVER_ERROR);
            return;
        }
        helper.deliverResponse(res, 200, result, {
            error_code: messages.META_TAG.UPDATED.error_code,
            error_message: messages.META_TAG.UPDATED.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
};

exports.metaTags = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const result = await service.findOne({ isDelete: false, storeId: storeid });
        if (result instanceof Error) {
            helper.deliverResponse(res, 422, result, messages.SERVER_ERROR);
            return;
        }
        result.image = process.env.DOMAIN + result.image;
        helper.deliverResponse(res, 200, result, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        });
    } catch (error) {
        helper.deliverResponse(res, 422, error, messages.SERVER_ERROR);
    }
}