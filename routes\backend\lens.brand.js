const express = require('express');
const router = express.Router();
const controller = require('../../controllers/lens.brand.controller')

module.exports = () => {
    router.post('/create-lens-brand', controller.create);
    router.post('/get-lens-brands', controller.list);
    router.get('/lens-brand-details/:refid', controller.details);
    router.put('/update-lens-brand/:refid', controller.update);
    router.put('/delete-lens-brand/:refid', controller.delete);

    return router;
}
