const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const service = require('../services/contactLens.service')
const axios = require('axios');

exports.create = async (req, res) => {
    try {
        let { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        body.refid = await service.count({}) + 1
        body.storeId = storeid;
        const response = await service.create(body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CONTACT_LENS.CREATED.error_code,
            error_message: messages.CONTACT_LENS.CREATED.error_message,
        });
        await axios.post(process.env.REVALIDATE, { tag: "contact-lens-page" }).then(response => {
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            message: messages.SERVER_ERROR.error_message,
        })
    }
}

exports.find = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.find({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.findOne = async (req, res) => {
    try {
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.findOne({ isDelete: false, storeId: storeid });
        helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message,
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.update = async (req, res) => {
    try {
        const { body } = req;
        let { storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        const response = await service.update({ isDelete: false, storeId: storeid }, body);
        helper.deliverResponse(res, 200, response, {
            error_code: messages.CONTACT_LENS.UPDATED.error_code,
            error_message: messages.CONTACT_LENS.UPDATED.error_message,
        })
        await axios.post(process.env.REVALIDATE, { tag: "contact-lens-page" }).then(response => {
        })
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message,
        });
    }
}

exports.getContactLens = async (req, res) => {
    try {
        let { language, storeid } = req.headers;
        storeid = storeid ? storeid : "ae";
        let data;
        if (language) {
            data = await service.findOne({ isDelete: false, storeId: storeid });
            data = {
                title: data?.title[language],
                description: data?.description[language],
                sectionOne: {
                    title: data?.sectionOne?.title[language],
                    description: data?.sectionOne?.description[language],
                    image: process.env.DOMAIN + data?.sectionOne?.image,
                }
                , sectionTwo: {
                    title: data?.sectionTwo?.title[language],
                    description: data?.sectionTwo?.description[language],
                    steps: data?.sectionTwo?.steps.map(step => {
                        return {
                            title: step.title[language],
                            description: step.description[language],
                            image: step.image
                        }
                    })
                },
                seoDetails: data?.seoDetails
            }
        } else {
            data = await service.findOne({ isDelete: false, storeId: storeid });
        }
        console.log(data)
        helper.deliverResponse(res, 200, data, messages.SUCCESS_RESPONSE);
    }
    catch (error) {
        helper.deliverResponse(res, 422, {}, messages.SERVER_ERROR);
    }
}
