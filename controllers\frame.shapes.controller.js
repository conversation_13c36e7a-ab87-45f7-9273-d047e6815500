const helper = require('../util/responseHelper')
const messages = require("../config/constants/messages");
const { body, validationResult } = require("express-validator");
const service = require("../services/frame.shapes.service");
const db = require("../models");
const slug = require('../util/slug')
const productServie = require('../services/products.service');
const { uploadWebp } = require('../util/uploadWebp');
const generateUniqueNumber = require('../util/getRefid');

exports.validate = (method) => {
    switch (method) {
        case 'create': {
            return [
                body('name[en]', 'Name is required').exists(),
                body('name[ar]', 'Name is required').exists(),
                body('image', 'Image is required').exists(),
            ]
        }
    }
}

exports.create = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        body.slug = await slug.createSlug(db.FrameShapes, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid });
        body.refid = generateUniqueNumber()
        body.storeId = storeid;
        if (req?.file){
            const name = `frame-shape/${body.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.image = key;
        }
        const response = await service.create(body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_SHAPES.CREATED.error_code,
            error_message: messages.FRAME_SHAPES.CREATED.error_message
        });
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.getFrameShapes = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.frameShapeDetails = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.findOne({ refid: req?.params?.refid, isDelete: false, storeId: storeid });
        if(!response) return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.update = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        let { body } = req;
        const frameShapeDetails = await service.findOne({ refid: body?.refid, isDelete: false, storeId: storeid });
        if (body?.name?.en !== frameShapeDetails?.name?.en) body.slug = await slug.createSlug(db.FrameShapes, body?.name?.en, { slug: await slug.generateSlug(body?.name?.en), storeId: storeid })
        if (body?.image) delete body.image
        if (req?.file){
            const name = `frame-shape/${body?.slug}/${Date.now()}-${req?.file?.originalname}.webp`
            const { key } = await uploadWebp(req?.file?.path, name)
            body.image = key;

        }

        const response = await service.update({ refid: body?.refid, isDelete: false, storeId: storeid }, body);
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_SHAPES.UPDATED.error_code,
            error_message: messages.FRAME_SHAPES.UPDATED.error_message
        });
    } catch (error) {
        console.log(error)
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.frameShapes = async (req, res) => {
    try {
        let { language, storeid } = req?.headers
        language = language ? language : 'en'
        storeid = storeid ? storeid : 'sa'
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        if (response?.length > 0) {
            const responseData = response.map(item => {
                return {
                    name: item.name[language],
                    image: item.image ? process.env.DOMAIN + item.image : null,
                    refid: item.refid,
                    _id: item._id
                }
            })
            helper.deliverResponse(res, 200, responseData, {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        } else {
            helper.deliverResponse(res, 200, [], {
                error_code: messages.SUCCESS_RESPONSE.error_code,
                error_message: messages.SUCCESS_RESPONSE.error_message
            })
        }
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.activeFrameShapes = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const response = await service.find({ isDelete: false, isActive: true, storeId: storeid });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.SUCCESS_RESPONSE.error_code,
            error_message: messages.SUCCESS_RESPONSE.error_message
        });
    } catch (error) {
        return helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}

exports.delete = async (req, res) => {
    let { storeid } = req?.headers;
    storeid = storeid ? storeid : "ae";
    try {
        const { refid } = req.params
        const frameShape = await service.findOne({ refid, isDelete: false, storeId: storeid });
        const products = await productServie.find({ frameShape: frameShape?._id, isDelete: false, storeId: storeid })
        if (products?.length > 0) {
            helper.deliverResponse(res, 422, {}, {
                error_code: 1,
                error_message: "Frame shape is used in products"
            });
            return;
        }
        const response = await service.update({ refid, isDelete: false, storeId: storeid }, { isDelete: true });
        return helper.deliverResponse(res, 200, response, {
            error_code: messages.FRAME_SHAPES.DELETED.error_code,
            error_message: messages.FRAME_SHAPES.DELETED.error_message
        });
    } catch (error) {
        helper.deliverResponse(res, 422, {}, {
            error_code: messages.SERVER_ERROR.error_code,
            error_message: messages.SERVER_ERROR.error_message
        });
    }
}