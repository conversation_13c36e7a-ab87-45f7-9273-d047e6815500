const express = require('express');
const router = express.Router();
const controller = require('../../controllers/blogs.controller')
const upload = require('../../util/upload');
const authorize = require('../../middlewares/authorize');

module.exports = () => {
    router.post('/create-blog', authorize.verifyToken, upload.single('image'), controller.create);
    router.get('/get-blogs', controller.getBlogs);
    router.get('/blog-details/:refid', controller.getBlogDetails);
    router.post('/update-blog', authorize.verifyToken, upload.single('image'), controller.update);
    router.put('/delete-blog/:refid', authorize.verifyToken, controller.delete);

    return router;
}
