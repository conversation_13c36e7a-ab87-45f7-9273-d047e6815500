const db = require('../models/index')

exports.create = async (data) => {
    try {
        let response = new db.Reviews(data)
        await response.save()
        return response
    } catch (error) {
        throw error;
    }
}

exports.find = async (query, projection = {}) => {
    try {
        let response = await db.Reviews.find(query, projection).sort({ createdAt: -1 })
            .populate('customer', 'name mobile image')
            .populate('product', 'name ')
        return response
    } catch (error) {
        throw error;
    }
}

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Reviews.findOne(query, projection)
        return response
    } catch (error) {
        throw error;
    }
}

exports.update = async (query, data) => {
    try {
        let response = await db.Reviews.findOneAndUpdate(query, { $set: data }, {
            new: true,
            upsert: false,
            useFindAndModify: false
        }).exec()
        return response
    } catch (error) {
        throw error;
    }
}

exports.count = async (query) => {
    try {
        let response = await db.Reviews.find(query).countDocuments()
        return response
    } catch (error) {
        throw error;
    }
}

exports.aggregate = async (query) => {
    try {
        let response = await db.Reviews.aggregate(query)
        return response
    } catch (error) {
        throw error;
    }
}