const mongoose = require('mongoose');

const brandsSchema = new mongoose.Schema({
    name: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    banner: {
        en: { type: String },
        ar: { type: String }
    },
    isMain: { type: Boolean, default: true },
    parent: { type: mongoose.Schema.Types.ObjectId, ref: 'brands' },
    subBrands: [{ type: mongoose.Schema.Types.ObjectId, ref: 'brands' }],
    image: { type: String },
    isCashbackEnabled: { type: Boolean, default: false },
    cashbackPercentage: { type: Number, default: 0 },
    poster: {
        en: { type: String },
        ar: { type: String }
    },
    inHome: { type: Boolean, default: false },
    slug: { type: String, required: true },
    refid: { type: String, required: true, unique: true },
    store: [{ type: mongoose.Schema.Types.ObjectId, ref: "stores" }],
    brandId: { type: String, unique: true },
    overview: {
        en: { type: String },
        ar: { type: String }
    },
    position: { type: Number, default: 0 },
    show: { type: Boolean, default: true },
    page: [
        {
            type: { type: String },
            bannerType: { type: String },
            mobileSrc: { type: String },
            src: { type: String },
            isActive: { type: Boolean, default: true },
            title: { type: String },
            banner: {
                en: { type: String },
                ar: { type: String }
            },
            content: {
                en: { type: String },
                ar: { type: String }
            },
            name: {
                en: { type: String },
                ar: { type: String }
            },
            products: [{ type: mongoose.Schema.Types.ObjectId, ref: "products" }],
        }
    ],
    seoDetails: {
        title: {
            en: { type: String },
            ar: { type: String }
        },
        description: {
            en: { type: String },
            ar: { type: String }
        },
        keywords: {
            en: { type: String },
            ar: { type: String },
        },
        canonical: {
            en: { type: String },
            ar: { type: String },
        },
        ogImage: { type: String },
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

brandsSchema.index({ refid: 1 }, { unique: true });
brandsSchema.index({ isActive: 1, isDelete: 1 });

module.exports = mongoose.model('brands', brandsSchema);