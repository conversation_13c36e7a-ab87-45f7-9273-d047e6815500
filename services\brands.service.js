const db = require("../models/index");

exports.create = async (data) => {
    try {
        let response = new db.Brands(data);
        await response.save();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.find = async (query, projection = {}, sort = { createdAt: -1 }) => {
    try {
        let response = await db.Brands.find(query, projection).sort(sort);
        return response;
    } catch (error) {
        throw error;
    }
};

exports.findOne = async (query, projection = {}) => {
    try {
        let response = await db.Brands.findOne(query, projection)
        .populate("store", "name")
        .populate("subBrands", "name refid")
        .populate({
            path: "page.products",
            select: "name thumbnail color brand price offerPrice offerPercentage slug refid productType category images cashbackPercentage isCashbackEnabled upc showDiscountPercentage isVirtualTry isDefaultVariant label storeId",
            populate: [
                { path: "color", select: "name color" },
                { path: "brand", select: "name slug" },
                { path: "category", select: "name isRoot isCashbackEnabled cashbackPercentage" },
                { path: "label", select: "name" },
            ]
        })
        return response;
    } catch (error) {
        throw error;
    }
};

exports.update = async (query, data) => {
    try {
        let response = await db.Brands.findOneAndUpdate(
            query,
            { $set: data },
            {
                new: true,
                upsert: false,
                useFindAndModify: false,
            }
        ).exec();
        return response;
    } catch (error) {
        throw error;
    }
};

exports.count = async (query) => {
    try {
        let response = await db.Brands.find(query).countDocuments();
        return response;
    } catch (error) {
        throw error;
    }
};
