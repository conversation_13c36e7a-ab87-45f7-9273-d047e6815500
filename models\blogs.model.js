const mongoose = require('mongoose');

const blogSchema = new mongoose.Schema({
    title: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    image: { type: String, required: true },
    summary: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    content: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    refid: { type: String, required: true },
    slug: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users", required: true },
    author: {
        en: { type: String },
        ar: { type: String }
    },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true });

module.exports = mongoose.model('blogs', blogSchema)