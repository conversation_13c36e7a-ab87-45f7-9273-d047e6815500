const mongoose = require('mongoose')

const insuranceContentSchema = new mongoose.Schema({
    pageTitle: {
        en: { type: String,  required: true },
        ar: { type: String }
    },
    pageDescription: {
        en: { type: String,  required: true },
        ar: { type: String }
    },
    descriptionOne: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    descriptionTwo: {
        en: { type: String, required: true },
        ar: { type: String }
    },
    terms: {
        title: {
            en: { type: String, required: true },
            ar: { type: String },
        },
        content: {
            en: { type: String, required: true },
            ar: { type: String },
        }
    },
    refid: { type: String, required: true },
    popup: {
        title: {
            en: { type: String, required: true },
            ar: { type: String },
        },
        description: {
            en: { type: String },
            ar: { type: String },
        },
        primaryBtn: {
            en: { type: String },
            ar: { type: String },
        },
        primaryBtnLink: { type: String },
        secondaryBtn: {
            en: { type: String },
            ar: { type: String },
        },
        secondaryBtnLink: { type: String },
    },
    seoDetails: {
        title: {
            en: { type: String,  },
            ar: { type: String }
        },
        description: {
            en: { type: String,  },
            ar: { type: String }
        },
        keywords: {
            en: { type: String,  },
            ar: { type: String }
        },
        canonical: {
            en: { type: String,  },
            ar: { type: String }
        },
        ogImage: { type: String }
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    storeId: { type: String, required: true, default: 'sa' }
}, { timestamps: true })

module.exports = mongoose.model('insurance.content', insuranceContentSchema)