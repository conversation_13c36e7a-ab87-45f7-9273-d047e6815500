const express = require('express');
const router = express.Router();
const lensBrandcontroller = require('../../controllers/lens.brand.controller');
const lensPowerController = require('../../controllers/lens.power.controller');
const lensTypeController = require('../../controllers/lens.type.controller');
const lensIndexController = require('../../controllers/lens.index.controller');
const lensCoatingController = require('../../controllers/coating.controller');

module.exports = () => {
    router.get('/lens-brands', lensBrandcontroller.lensBrands);
    router.get('/lens-powers/:brand', lensPowerController.brandPowers);
    router.get('/lens-types/:brand', lensTypeController.brandTypes);
    router.get('/lens-index/:brand', lensIndexController.lensIndex)
    router.get('/coating/:brand', lensCoatingController.lensCoating)

    return router;
}