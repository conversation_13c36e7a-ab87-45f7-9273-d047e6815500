const express = require('express');
const router = express.Router();
const controller = require('../../controllers/dashboard.settings.controller')
const beforeAfterController = require('../../controllers/before.after.controller')
const upload = require('../../util/upload')
const iamgeMapController = require('../../controllers/imageMap.controller')

module.exports = () => {
    router.post('/create-home-order', controller.create);
    router.get('/get-home-order', controller.list);
    router.put('/update-home-order/:refid', controller.update);
    router.put('/add-widget/:refid', controller.addWidget);
    router.put('/widget-status',controller.widgetStatus)
    router.put('/set-single-collection', controller.setSingleCollection);
    router.put('/set-multiple-collection', controller.setMultipleCollection);
    router.get('/selected-collections', controller.getSelectedCollection);

    router.post('/set-banner', controller.setBanner);
    router.post('/set-slider', controller.setSlider);

    router.get('/before-after', beforeAfterController.getBeforeAfter);
    router.post('/before-after', beforeAfterController.addBeforeAfter);
    router.put('/before-after/:refid', beforeAfterController.updateBeforeAfter);
    router.delete('/before-after/:id', beforeAfterController.deleteBeforeAfter);

    router.post('/imageMap', iamgeMapController.create)
    router.get('/imageMap', iamgeMapController.get)
    router.put('/imageMap', iamgeMapController.update)

    return router;
}