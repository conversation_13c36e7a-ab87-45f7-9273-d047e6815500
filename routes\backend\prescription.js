const express = require('express');
const router = express.Router();
const controller = require('../../controllers/prescription.controller');
const upload = require('../../util/upload');

module.exports = () => {
    router.post('/create-prescription', upload.single('image'), controller.create);
    router.get('/get-prescriptions', controller.getPrescriptions);
    router.post('/update-prescription', upload.single('image'), controller.update);

    return router;
}
